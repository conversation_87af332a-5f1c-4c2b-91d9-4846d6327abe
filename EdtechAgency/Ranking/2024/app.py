import time
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

chrome_options = Options()
chrome_options.add_argument("--incognito")
chrome_options.add_argument('--headless') 
driver = webdriver.Chrome(options=chrome_options)

# List of app links
df = pd.read_excel('/Users/<USER>/Downloads/list.xlsx')
app_links = df['Link'].tolist()  # Corrected line

def extract_app_info(url, wait):
    data = {}
    try:
        # Open the app link
        driver.get(url)

        # STAR RATE
        try:
            data['Rate'] = wait.until(EC.presence_of_element_located((By.XPATH, "//*[@id='yDmH0d']/c-wiz[2]/div/div/div[1]/div/div[1]/div/div/c-wiz/div[2]/div[2]/div/div/div[1]/div[1]/div/div"))).text
        except Exception:
            data['Rate'] = None
        
        # DOWNLOAD
        try:
            data['Download'] = wait.until(EC.presence_of_element_located((By.XPATH, "//*[@id='yDmH0d']/c-wiz[2]/div/div/div[1]/div/div[1]/div/div/c-wiz/div[2]/div[2]/div/div/div[2]/div[1]"))).text
        except Exception:
            data['Download'] = None

    except Exception as e:
        print(f"An error occurred while processing URL {url}: {e}")
        return None

    return data

# Initialize WebDriverWait
wait = WebDriverWait(driver, 10)

# List to store app data
data_list = []

# Loop through each app link to extract data
for url in app_links:
    data = extract_app_info(url, wait)
    if data is not None:
        data_list.append(data)

# Close the WebDriver
driver.quit()

# Convert the data to a DataFrame
df = pd.DataFrame(data_list)

# Save the DataFrame to a CSV file
df.to_csv('app_data.csv', index=False)

print("App data has been extracted and saved to 'app_data.csv'.")