{"cells": [{"cell_type": "code", "execution_count": 12, "id": "161535e4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 24 Excel files to merge.\n", "Processing file 2/24: overview-trend-2025-05-08T13_59_55Z.xlsx\n", "Processing file 3/24: overview-trend-2025-05-08T14_04_17Z.xlsx\n", "Processing file 4/24: overview-trend-2025-05-08T14_05_10Z.xlsx\n", "Processing file 5/24: overview-trend-2025-05-08T14_13_16Z.xlsx\n", "Processing file 6/24: overview-trend-2025-05-08T14_15_56Z.xlsx\n", "Processing file 7/24: overview-trend-2025-05-08T14_04_36Z.xlsx\n", "Processing file 8/24: overview-trend-2025-05-08T14_14_08Z.xlsx\n", "Processing file 9/24: overview-trend-2025-05-08T14_03_37Z.xlsx\n", "Processing file 10/24: overview-trend-2025-05-08T14_14_31Z.xlsx\n", "Processing file 11/24: overview-trend-2025-05-12T03_29_34Z.xlsx\n", "Processing file 12/24: overview-trend-2025-05-08T14_13_36Z.xlsx\n", "Processing file 13/24: overview-trend-2025-05-08T14_14_52Z.xlsx\n", "Processing file 14/24: overview-trend-2025-05-08T14_02_22Z.xlsx\n", "Processing file 15/24: overview-trend-2025-05-08T14_09_04Z.xlsx\n", "Processing file 16/24: overview-trend-2025-05-08T14_01_48Z.xlsx\n", "Processing file 17/24: overview-trend-2025-05-08T14_02_45Z.xlsx\n", "Processing file 18/24: overview-trend-2025-05-08T14_05_26Z.xlsx\n", "Processing file 19/24: overview-trend-2025-05-08T14_08_21Z.xlsx\n", "Processing file 20/24: overview-trend-2025-05-12T04_03_25Z.xlsx\n", "Processing file 21/24: vn.bitdegree.org.xlsx\n", "Processing file 22/24: overview-trend-2025-05-08T14_08_46Z.xlsx\n", "Processing file 23/24: overview-trend-2025-05-12T03_29_19Z.xlsx\n", "Processing file 24/24: overview-trend-2025-05-12T04_04_00Z.xlsx\n", "Successfully merged 24 files into /Users/<USER>/Downloads/merge_12-05.xlsx\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n", "/Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/openpyxl/styles/stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import os\n", "\n", "def merge_excel_files(directory_path, output_file_path):\n", "    \"\"\"\n", "    Merge multiple Excel files from a directory into a single Excel file.\n", "    Keeps only the first header and removes duplicate headers from subsequent files.\n", "    \n", "    Parameters:\n", "    directory_path (str): Path to the directory containing Excel files\n", "    output_file_path (str): Path where the merged Excel file will be saved\n", "    \n", "    Returns:\n", "    bool: True if successful, False otherwise\n", "    \"\"\"\n", "    try:\n", "        # Get all Excel files in the directory\n", "        excel_files = [f for f in os.listdir(directory_path) \n", "                      if f.endswith('.xlsx') or f.endswith('.xls')]\n", "        \n", "        if not excel_files:\n", "            print(\"No Excel files found in the specified directory.\")\n", "            return False\n", "        \n", "        print(f\"Found {len(excel_files)} Excel files to merge.\")\n", "        \n", "        # Create an empty list to store all dataframes\n", "        all_data = []\n", "        \n", "        # Process first file to get headers\n", "        first_file = os.path.join(directory_path, excel_files[0])\n", "        first_df = pd.read_excel(first_file)\n", "        all_data.append(first_df)\n", "        \n", "        # Process remaining files and skip their headers\n", "        for i, file in enumerate(excel_files[1:], 1):\n", "            file_path = os.path.join(directory_path, file)\n", "            print(f\"Processing file {i+1}/{len(excel_files)}: {file}\")\n", "            \n", "            # Read the current Excel file\n", "            current_df = pd.read_excel(file_path)\n", "            \n", "            # # Skip the header row (first row)\n", "            # current_df = current_df.iloc[1:]\n", "            \n", "            # Append to the list\n", "            all_data.append(current_df)\n", "        \n", "        # Concatenate all dataframes\n", "        merged_data = pd.concat(all_data, ignore_index=True)\n", "        \n", "        # Save the merged data to a new Excel file\n", "        merged_data.to_excel(output_file_path, index=False)\n", "        \n", "        print(f\"Successfully merged {len(excel_files)} files into {output_file_path}\")\n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"Error: {str(e)}\")\n", "        return False\n", "\n", "from datetime import datetime\n", "time = datetime.today().strftime(\"%d-%m\")\n", "merge_excel_files(f\"/Users/<USER>/Downloads/Semrush_12-05\", f\"/Users/<USER>/Downloads/merge_12-05.xlsx\")"]}, {"cell_type": "code", "execution_count": null, "id": "6eb1ab6f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}