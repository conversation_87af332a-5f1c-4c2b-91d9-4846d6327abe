{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Specify the directory where the Excel files are located\n", "directory = '/Users/<USER>/Downloads/'\n", "\n", "# Specify the prefix of the Excel files you want to concatenate\n", "file_prefix = 'webautomation_data_similarweb'\n", "\n", "# Get all files in the directory\n", "all_files = os.listdir(directory)\n", "\n", "# Filter files that start with the specified prefix\n", "excel_files = [file for file in all_files if file.startswith(file_prefix) and file.endswith('.xlsx')]\n", "\n", "# Initialize an empty list to store DataFrame objects\n", "dfs = []\n", "\n", "# Read and concatenate Excel files\n", "for file in excel_files:\n", "    # Construct the full path to the Excel file\n", "    file_path = os.path.join(directory, file)\n", "    \n", "    # Read Excel file into a DataFrame\n", "    df = pd.read_excel(file_path)\n", "    \n", "    # Append DataFrame to the list\n", "    dfs.append(df)\n", "\n", "# Concatenate all DataFrames into a single DataFrame\n", "data = pd.concat(dfs, ignore_index=True)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Function to extract the value of the first key from a dictionary-like string\n", "def extract_first_key_value(cell_value):\n", "    data_dict = eval(cell_value)\n", "    first_key_value = data_dict.get(list(data_dict.keys())[0])\n", "    return first_key_value\n", "\n", "# Apply the function to each cell in the column\n", "data['Country_No'] = data['top_countries'].apply(extract_first_key_value)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["data = data[['domain_name','Country_No']]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["data.rename(columns={'domain_name':'Website',\n", "                     'Country_No':'Country Distribution'},inplace=True)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Website</th>\n", "      <th>Country Distribution</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>maxsolution.com.vn</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>trinam.com.vn</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>thienan.vn</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>vietnamstudent.vn</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>manabie.vn</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               Website  Country Distribution\n", "3   maxsolution.com.vn                     4\n", "2        trinam.com.vn                     3\n", "9           thienan.vn                     1\n", "12   vietnamstudent.vn                     7\n", "10          manabie.vn                     3"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["data.sample(5)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from datetime import datetime \n", "time = datetime.today().strftime(\"%m-%d\")\n", "data.to_excel(f'/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Ranking/CountryDistribution_{time}.xlsx',index=None)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: gspread in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (6.1.0)\n", "Requirement already satisfied: google-auth>=1.12.0 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from gspread) (2.22.0)\n", "Requirement already satisfied: google-auth-oauthlib>=0.4.1 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from gspread) (0.5.2)\n", "Requirement already satisfied: StrEnum==0.4.15 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from gspread) (0.4.15)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from google-auth>=1.12.0->gspread) (5.3.1)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from google-auth>=1.12.0->gspread) (0.2.8)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from google-auth>=1.12.0->gspread) (4.7.2)\n", "Requirement already satisfied: six>=1.9.0 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from google-auth>=1.12.0->gspread) (1.16.0)\n", "Requirement already satisfied: urllib3<2.0 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from google-auth>=1.12.0->gspread) (1.26.16)\n", "Requirement already satisfied: requests-oauthlib>=0.7.0 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from google-auth-oauthlib>=0.4.1->gspread) (1.3.0)\n", "Requirement already satisfied: pyasn1<0.5.0,>=0.4.6 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from pyasn1-modules>=0.2.1->google-auth>=1.12.0->gspread) (0.4.8)\n", "Requirement already satisfied: oauthlib>=3.0.0 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from requests-oauthlib>=0.7.0->google-auth-oauthlib>=0.4.1->gspread) (3.2.2)\n", "Requirement already satisfied: requests>=2.0.0 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from requests-oauthlib>=0.7.0->google-auth-oauthlib>=0.4.1->gspread) (2.31.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib>=0.4.1->gspread) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib>=0.4.1->gspread) (3.4)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib>=0.4.1->gspread) (2024.2.2)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install gspread"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: oauth2client in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (4.1.3)\n", "Requirement already satisfied: httplib2>=0.9.1 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from oauth2client) (0.22.0)\n", "Requirement already satisfied: pyasn1>=0.1.7 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from oauth2client) (0.4.8)\n", "Requirement already satisfied: pyasn1-modules>=0.0.5 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from oauth2client) (0.2.8)\n", "Requirement already satisfied: rsa>=3.1.4 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from oauth2client) (4.7.2)\n", "Requirement already satisfied: six>=1.6.1 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from oauth2client) (1.16.0)\n", "Requirement already satisfied: pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from httplib2>=0.9.1->oauth2client) (3.1.0)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install oauth2client"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: google-api-python-client in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (2.129.0)\n", "Requirement already satisfied: oauth2client in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (4.1.3)\n", "Requirement already satisfied: httplib2<1.dev0,>=0.19.0 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from google-api-python-client) (0.22.0)\n", "Requirement already satisfied: google-auth!=2.24.0,!=2.25.0,<3.0.0.dev0,>=1.32.0 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from google-api-python-client) (2.22.0)\n", "Requirement already satisfied: google-auth-httplib2<1.0.0,>=0.2.0 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from google-api-python-client) (0.2.0)\n", "Requirement already satisfied: google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0.dev0,>=1.31.5 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from google-api-python-client) (2.17.1)\n", "Requirement already satisfied: uritemplate<5,>=3.0.1 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from google-api-python-client) (4.1.1)\n", "Requirement already satisfied: pyasn1>=0.1.7 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from oauth2client) (0.4.8)\n", "Requirement already satisfied: pyasn1-modules>=0.0.5 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from oauth2client) (0.2.8)\n", "Requirement already satisfied: rsa>=3.1.4 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from oauth2client) (4.7.2)\n", "Requirement already satisfied: six>=1.6.1 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from oauth2client) (1.16.0)\n", "Requirement already satisfied: googleapis-common-protos<2.0.dev0,>=1.56.2 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0.dev0,>=1.31.5->google-api-python-client) (1.62.0)\n", "Requirement already satisfied: protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0.dev0,>=3.19.5 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0.dev0,>=1.31.5->google-api-python-client) (4.21.12)\n", "Requirement already satisfied: requests<3.0.0.dev0,>=2.18.0 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0.dev0,>=1.31.5->google-api-python-client) (2.31.0)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0.dev0,>=1.32.0->google-api-python-client) (5.3.1)\n", "Requirement already satisfied: urllib3<2.0 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0.dev0,>=1.32.0->google-api-python-client) (1.26.16)\n", "Requirement already satisfied: pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from httplib2<1.dev0,>=0.19.0->google-api-python-client) (3.1.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0.dev0,>=1.31.5->google-api-python-client) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0.dev0,>=1.31.5->google-api-python-client) (3.4)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/opt/anaconda3/envs/myenv/lib/python3.11/site-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0.dev0,>=1.31.5->google-api-python-client) (2024.2.2)\n"]}], "source": ["!pip3 install --upgrade google-api-python-client oauth2client"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import gspread\n", "from oauth2client.service_account import ServiceAccountCredentials\n", "\n", "# Define the scope and credentials to access Google Sheets\n", "scope = ['https://spreadsheets.google.com/feeds',\n", "         'https://www.googleapis.com/auth/drive']\n", "\n", "# add credentials to the account\n", "creds = ServiceAccountCredentials.from_json_keyfile_name('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Ranking/credentials.json', scope)\n", "\n", "# authorize the clientsheet \n", "client = gspread.authorize(creds)\n", "\n", "# get the instance of the Spreadsheet\n", "sheet = client.open('list Edtech_2024')\n", "\n", "# get the first sheet of the Spreadsheet\n", "worksheet = sheet.get_worksheet(2)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'Website': 'theenest.edu.vn', 'Country Distribution': 5},\n", " {'Website': 'viete.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'garastem.com', 'Country Distribution': 3},\n", " {'Website': 'stemsquare.vn', 'Country Distribution': 1},\n", " {'Website': 'tutoru.vn', 'Country Distribution': 1},\n", " {'Website': 'toppy.edu.vn', 'Country Distribution': 0},\n", " {'Website': 'hanoiedu.com', 'Country Distribution': 2},\n", " {'Website': 'edupay.asia', 'Country Distribution': 4},\n", " {'Website': 'tesse.io', 'Country Distribution': 32},\n", " {'Website': 'iostudy.net', 'Country Distribution': 10},\n", " {'Website': 'sunbot.vn', 'Country Distribution': 1},\n", " {'Website': 'luyenthidaihoc247.com', 'Country Distribution': 0},\n", " {'Website': 'tutorin.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'vietrobot.edu.vn', 'Country Distribution': 4},\n", " {'Website': 'estudy.langmaster.vn', 'Country Distribution': 1},\n", " {'Website': 'aztest.vn', 'Country Distribution': 2},\n", " {'Website': 'lora.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'popoky.com', 'Country Distribution': 0},\n", " {'Website': 'mindspaeduvn.wordpress.com', 'Country Distribution': 0},\n", " {'Website': 'stemlab.vn', 'Country Distribution': 1},\n", " {'Website': 'online2study.com', 'Country Distribution': 0},\n", " {'Website': 'robotstemtpa.vn', 'Country Distribution': 3},\n", " {'Website': 'azota.vn', 'Country Distribution': 11},\n", " {'Website': 'onluyen.vn', 'Country Distribution': 5},\n", " {'Website': 'dtsoft.vn', 'Country Distribution': 13},\n", " {'Website': 'k12online.vn', 'Country Distribution': 4},\n", " {'Website': 'ssstudy.vn', 'Country Distribution': 1},\n", " {'Website': 'openstudy.vn', 'Country Distribution': 1},\n", " {'Website': 'hoc24.vn', 'Country Distribution': 17},\n", " {'Website': 'hocmai.vn', 'Country Distribution': 21},\n", " {'Website': 'goingsunny.com', 'Country Distribution': 0},\n", " {'Website': 'easyedu.vn', 'Country Distribution': 2},\n", " {'Website': 'study.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'ayotree.com', 'Country Distribution': 12},\n", " {'Website': 'kienguru.vn', 'Country Distribution': 7},\n", " {'Website': 'enetviet.com', 'Country Distribution': 6},\n", " {'Website': 'luyenthipro.com', 'Country Distribution': 6},\n", " {'Website': 'smas.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'perfect.com.vn', 'Country Distribution': 1},\n", " {'Website': '789.vn', 'Country Distribution': 0},\n", " {'Website': 'eschoolvn.edu.vn', 'Country Distribution': 10},\n", " {'Website': 'one.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'emis.misa.vn', 'Country Distribution': 2},\n", " {'Website': 'study365.edu.vn', 'Country Distribution': 0},\n", " {'Website': 'dtstudy.vn', 'Country Distribution': 1},\n", " {'Website': 'azota.vn', 'Country Distribution': 11},\n", " {'Website': 'onluyen.vn', 'Country Distribution': 5},\n", " {'Website': 'dtsoft.vn', 'Country Distribution': 13},\n", " {'Website': 'k12online.vn', 'Country Distribution': 4},\n", " {'Website': 'ssstudy.vn', 'Country Distribution': 1},\n", " {'Website': 'openstudy.vn', 'Country Distribution': 1},\n", " {'Website': 'hoc24.vn', 'Country Distribution': 17},\n", " {'Website': 'hocmai.vn', 'Country Distribution': 21},\n", " {'Website': 'goingsunny.com', 'Country Distribution': 0},\n", " {'Website': 'easyedu.vn', 'Country Distribution': 2},\n", " {'Website': 'study.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'ayotree.com', 'Country Distribution': 12},\n", " {'Website': 'kienguru.vn', 'Country Distribution': 7},\n", " {'Website': 'enetviet.com', 'Country Distribution': 6},\n", " {'Website': 'luyenthipro.com', 'Country Distribution': 6},\n", " {'Website': 'smas.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'perfect.com.vn', 'Country Distribution': 1},\n", " {'Website': '789.vn', 'Country Distribution': 0},\n", " {'Website': 'eschoolvn.edu.vn', 'Country Distribution': 10},\n", " {'Website': 'one.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'emis.misa.vn', 'Country Distribution': 2},\n", " {'Website': 'study365.edu.vn', 'Country Distribution': 0},\n", " {'Website': 'datacamp.com', 'Country Distribution': 137},\n", " {'Website': 'thuhocphi.com', 'Country Distribution': 2},\n", " {'Website': 'pluralsight.com', 'Country Distribution': 129},\n", " {'Website': 'psctelecom.com.vn', 'Country Distribution': 4},\n", " {'Website': 'hellochao.vn', 'Country Distribution': 3},\n", " {'Website': 'ninoapp.vn', 'Country Distribution': 1},\n", " {'Website': 'bachkim.vn', 'Country Distribution': 1},\n", " {'Website': 'littlelives.com', 'Country Distribution': 18},\n", " {'Website': 'softenmind.com', 'Country Distribution': 4},\n", " {'Website': 'code4startup.com', 'Country Distribution': 23},\n", " {'Website': 'udacity.com', 'Country Distribution': 114},\n", " {'Website': 'edumall.vn', 'Country Distribution': 9},\n", " {'Website': 'codecombat.com', 'Country Distribution': 79},\n", " {'Website': 'freecodecamp.org', 'Country Distribution': 139},\n", " {'Website': 'wewiin.com', 'Country Distribution': 1},\n", " {'Website': 'cati.edu.vn', 'Country Distribution': 9},\n", " {'Website': 'codecademy.com', 'Country Distribution': 133},\n", " {'Website': 'qlmn.vn', 'Country Distribution': 1},\n", " {'Website': 'kc.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'vnresource.vn', 'Country Distribution': 2},\n", " {'Website': 'pointavenue.com', 'Country Distribution': 19},\n", " {'Website': 'luyenthipro.com', 'Country Distribution': 6},\n", " {'Website': 'itest.com.vn', 'Country Distribution': 6},\n", " {'Website': 'busuu.com', 'Country Distribution': 126},\n", " {'Website': 'akadon.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'thanhtoan.violet.vn', 'Country Distribution': 0},\n", " {'Website': 'phuoctienb.violet.vn', 'Country Distribution': 1},\n", " {'Website': 'tnmaker.net', 'Country Distribution': 2},\n", " {'Website': 'colearn.vn', 'Country Distribution': 1},\n", " {'Website': 'herventure.org', 'Country Distribution': 7},\n", " {'Website': 'cungthi.vn', 'Country Distribution': 0},\n", " {'Website': 'ongbut.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'helloenglish.com', 'Country Distribution': 121},\n", " {'Website': 'steprimo.com', 'Country Distribution': 92},\n", " {'Website': 'ringleplus.com', 'Country Distribution': 17},\n", " {'Website': 'testchinese.com', 'Country Distribution': 0},\n", " {'Website': 'tryhsk.com', 'Country Distribution': 11},\n", " {'Website': 'uommamhientai.edu.vn', 'Country Distribution': 0},\n", " {'Website': 'tmo.taimienphi.vn', 'Country Distribution': 3},\n", " {'Website': 'hskonline.com', 'Country Distribution': 40},\n", " {'Website': 'testpro.com.vn', 'Country Distribution': 1},\n", " {'Website': 'icorrect.vn', 'Country Distribution': 3},\n", " {'Website': 'acabiz.vn', 'Country Distribution': 1},\n", " {'Website': 'daykemtainha.vn', 'Country Distribution': 3},\n", " {'Website': 'jobway.vn', 'Country Distribution': 0},\n", " {'Website': 'onthitracnghiem.online', 'Country Distribution': 1},\n", " {'Website': 'bigschool.vn', 'Country Distribution': 1},\n", " {'Website': 'lize.vn', 'Country Distribution': 0},\n", " {'Website': 'onthi247.vn', 'Country Distribution': 6},\n", " {'Website': 'thivao10.net', 'Country Distribution': 0},\n", " {'Website': 'maxsolution.com.vn', 'Country Distribution': 4},\n", " {'Website': 'megaschool.edu.vn', 'Country Distribution': 3},\n", " {'Website': 'quangich.com', 'Country Distribution': 3},\n", " {'Website': 'tienganhk12.com', 'Country Distribution': 13},\n", " {'Website': 'edulive.net', 'Country Distribution': 1},\n", " {'Website': 'thionline.com.vn', 'Country Distribution': 0},\n", " {'Website': 'mschool.mobiedu.vn', 'Country Distribution': 0},\n", " {'Website': 'iface.vn', 'Country Distribution': 0},\n", " {'Website': 'testbank.vn', 'Country Distribution': 4},\n", " {'Website': 'vio.edu.vn', 'Country Distribution': 9},\n", " {'Website': 'itest.com.vn', 'Country Distribution': 6},\n", " {'Website': 'onthidaihoc247.com', 'Country Distribution': 0},\n", " {'Website': 'vn.elsaspeak.com', 'Country Distribution': 17},\n", " {'Website': 'classclap.vn', 'Country Distribution': 5},\n", " {'Website': 'thinkingschool.vn', 'Country Distribution': 3},\n", " {'Website': 'vuihoc.vn', 'Country Distribution': 21},\n", " {'Website': 'app.onluyen.vn', 'Country Distribution': 2},\n", " {'Website': 'hocmai247.vn', 'Country Distribution': 1},\n", " {'Website': 'lingua-attack.com', 'Country Distribution': 32},\n", " {'Website': 'schoolcento.com', 'Country Distribution': 2},\n", " {'Website': 'zunia.vn', 'Country Distribution': 2},\n", " {'Website': 'ahaslides.com', 'Country Distribution': 122},\n", " {'Website': 'asknow.vn', 'Country Distribution': 1},\n", " {'Website': 'yoot.vn', 'Country Distribution': 16},\n", " {'Website': 'jobtest.vn', 'Country Distribution': 6},\n", " {'Website': 'riki.edu.vn', 'Country Distribution': 5},\n", " {'Website': 'yaho.life', 'Country Distribution': 3},\n", " {'Website': 'ebomb.edu.vn', 'Country Distribution': 3},\n", " {'Website': 'organizations.cambly.com', 'Country Distribution': 22},\n", " {'Website': 'englishninjas.com', 'Country Distribution': 3},\n", " {'Website': 'sofiri.com', 'Country Distribution': 37},\n", " {'Website': 'schoolup.vn', 'Country Distribution': 1},\n", " {'Website': 'vtutor.com', 'Country Distribution': 27},\n", " {'Website': 'mobiedu.vn', 'Country Distribution': 3},\n", " {'Website': 'oceandemy.com', 'Country Distribution': 0},\n", " {'Website': 'schoolbird.co', 'Country Distribution': 0},\n", " {'Website': 'hoclagioi.net', 'Country Distribution': 1},\n", " {'Website': 'holospeak.co', 'Country Distribution': 2},\n", " {'Website': 'pantado.edu.vn', 'Country Distribution': 16},\n", " {'Website': 'igstutor.vn', 'Country Distribution': 1},\n", " {'Website': 'ketnoigiaoduc.vn', 'Country Distribution': 3},\n", " {'Website': 'cuduapp.com', 'Country Distribution': 0},\n", " {'Website': 'lazi.vn', 'Country Distribution': 18},\n", " {'Website': 'hoc247.net', 'Country Distribution': 24},\n", " {'Website': 'schoolonline.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'tech12h.com', 'Country Distribution': 12},\n", " {'Website': 'teky.edu.vn', 'Country Distribution': 13},\n", " {'Website': 'yola.vn', 'Country Distribution': 8},\n", " {'Website': 'tuyensinh247.com', 'Country Distribution': 33},\n", " {'Website': 'thiquocgia.vn', 'Country Distribution': 6},\n", " {'Website': 'school.onluyen.vn', 'Country Distribution': 1},\n", " {'Website': 'wewiin.com', 'Country Distribution': 1},\n", " {'Website': 'mclass.vn', 'Country Distribution': 5},\n", " {'Website': 'dodaihoc.edubit.vn', 'Country Distribution': 0},\n", " {'Website': 'vietjack.com', 'Country Distribution': 40},\n", " {'Website': 'ioe.vn', 'Country Distribution': 8},\n", " {'Website': 'loigiaihay.com', 'Country Distribution': 31},\n", " {'Website': 'moon.vn', 'Country Distribution': 35},\n", " {'Website': 'mozaweb.com', 'Country Distribution': 69},\n", " {'Website': 'vungoi.vn', 'Country Distribution': 12},\n", " {'Website': 'novateen.vn', 'Country Distribution': 2},\n", " {'Website': 'tienganh123.com', 'Country Distribution': 11},\n", " {'Website': 'itutor.world', 'Country Distribution': 0},\n", " {'Website': 'luyenthi123.com', 'Country Distribution': 5},\n", " {'Website': 'giainhanh.vn', 'Country Distribution': 1},\n", " {'Website': 'matific.com', 'Country Distribution': 81},\n", " {'Website': 'fqa.vn', 'Country Distribution': 11},\n", " {'Website': 'eduquiz.vn', 'Country Distribution': 5},\n", " {'Website': 'study.hanoi.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'thithu.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'hoidap247.com', 'Country Distribution': 29},\n", " {'Website': 'click2learn.vn', 'Country Distribution': 0},\n", " {'Website': 'herapo.vn', 'Country Distribution': 1},\n", " {'Website': 'qandastudy.vn', 'Country Distribution': 1},\n", " {'Website': 'edubit.vn', 'Country Distribution': 4},\n", " {'Website': 'ohstem.vn', 'Country Distribution': 2},\n", " {'Website': 'geniebook.com', 'Country Distribution': 36},\n", " {'Website': 'omegaenglish.vn', 'Country Distribution': 1},\n", " {'Website': 'toanhoc247.com', 'Country Distribution': 1},\n", " {'Website': 'toanhoc77.wordpress.com', 'Country Distribution': 1},\n", " {'Website': 'hocthoi.net', 'Country Distribution': 1},\n", " {'Website': 'hachium.com', 'Country Distribution': 1},\n", " {'Website': 'alohoc.vn', 'Country Distribution': 2},\n", " {'Website': 'clevai.edu.vn', 'Country Distribution': 6},\n", " {'Website': 'mathx.vn', 'Country Distribution': 5},\n", " {'Website': 'trangnguyen.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'sne.vn', 'Country Distribution': 0},\n", " {'Website': 'baigiangxanh.com', 'Country Distribution': 1},\n", " {'Website': 'classin.com.vn', 'Country Distribution': 6},\n", " {'Website': 'thionline.com.vn', 'Country Distribution': 0},\n", " {'Website': 'stemhouse.edu.vn', 'Country Distribution': 7},\n", " {'Website': 'fermat.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'haylamdo.com', 'Country Distribution': 6},\n", " {'Website': 'hoola.vn', 'Country Distribution': 1},\n", " {'Website': 'edunet.vn', 'Country Distribution': 2},\n", " {'Website': 'robotstemtpa.vn', 'Country Distribution': 3},\n", " {'Website': 'trainercentral.com', 'Country Distribution': 18},\n", " {'Website': 'des.vn', 'Country Distribution': 5},\n", " {'Website': 'agilearn.vn', 'Country Distribution': 2},\n", " {'Website': 'peo.vn', 'Country Distribution': 1},\n", " {'Website': 'mvvacademy.edu.vn', 'Country Distribution': 4},\n", " {'Website': 'edutek.io', 'Country Distribution': 1},\n", " {'Website': 'pdca.vn', 'Country Distribution': 3},\n", " {'Website': 'dostem.edu.vn', 'Country Distribution': 4},\n", " {'Website': 'digiland.com.vn', 'Country Distribution': 1},\n", " {'Website': 'oes.vn', 'Country Distribution': 15},\n", " {'Website': 'elitelearning.vn', 'Country Distribution': 1},\n", " {'Website': 'edubit.vn', 'Country Distribution': 4},\n", " {'Website': 'vieted.com', 'Country Distribution': 3},\n", " {'Website': 'lms.tntalent.vn', 'Country Distribution': 1},\n", " {'Website': 'cls.vn', 'Country Distribution': 1},\n", " {'Website': 'mge.vn', 'Country Distribution': 4},\n", " {'Website': 'bitlearn.vn', 'Country Distribution': 1},\n", " {'Website': 'gitiho.com', 'Country Distribution': 13},\n", " {'Website': 'hocviensangtao.edu.vn', 'Country Distribution': 5},\n", " {'Website': 'unica.vn', 'Country Distribution': 20},\n", " {'Website': 'zaidap.com', 'Country Distribution': 2},\n", " {'Website': 'wetalk.school', 'Country Distribution': 0},\n", " {'Website': 'toploigiai.vn', 'Country Distribution': 9},\n", " {'Website': 'codekitten.vn', 'Country Distribution': 20},\n", " {'Website': 'gamma.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'holospeak.vn', 'Country Distribution': 0},\n", " {'Website': 'vnedu.vn', 'Country Distribution': 8},\n", " {'Website': 'voca.vn', 'Country Distribution': 2},\n", " {'Website': 'kidsup.net', 'Country Distribution': 5},\n", " {'Website': 'dungmori.com', 'Country Distribution': 6},\n", " {'Website': 'loga.vn', 'Country Distribution': 4},\n", " {'Website': 'baitapsgk.com', 'Country Distribution': 1},\n", " {'Website': 'duolingo.com', 'Country Distribution': 5},\n", " {'Website': 'kidsloop.vn', 'Country Distribution': 0},\n", " {'Website': 'kids.hoc247.vn', 'Country Distribution': 2},\n", " {'Website': 'tata.edu.vn', 'Country Distribution': 3},\n", " {'Website': 'lets.goclass.vn', 'Country Distribution': 1},\n", " {'Website': 'study.hanoi.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'amazingmath.vn', 'Country Distribution': 2},\n", " {'Website': 'mycoolclass.com', 'Country Distribution': 4},\n", " {'Website': 'ekidenglish.edu.vn', 'Country Distribution': 3},\n", " {'Website': 'sachgiai.com', 'Country Distribution': 2},\n", " {'Website': 'dethikiemtra.com', 'Country Distribution': 10},\n", " {'Website': 'dethihocki.com', 'Country Distribution': 2},\n", " {'Website': 'thithu.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'thithu.vn', 'Country Distribution': 1},\n", " {'Website': 'matran.edu.vn', 'Country Distribution': 10},\n", " {'Website': 'onthidaihoc.online', 'Country Distribution': 0},\n", " {'Website': 'chiasehay.net', 'Country Distribution': 1},\n", " {'Website': 'izi.community', 'Country Distribution': 0},\n", " {'Website': 'lingobee.vn', 'Country Distribution': 2},\n", " {'Website': 'mshoagiaotiep.com', 'Country Distribution': 4},\n", " {'Website': 'lophocvui.com', 'Country Distribution': 0},\n", " {'Website': 'selfomy.com', 'Country Distribution': 2},\n", " {'Website': 'ebomb.edu.vn', 'Country Distribution': 3},\n", " {'Website': 'airclass.io', 'Country Distribution': 1},\n", " {'Website': 'dethithudaihoc.com', 'Country Distribution': 1},\n", " {'Website': 'sisap.vn', 'Country Distribution': 5},\n", " {'Website': 'lika.edu.vn', 'Country Distribution': 0},\n", " {'Website': 'thpt.iss.edu.vn', 'Country Distribution': 0},\n", " {'Website': 'luyenhoc.vn', 'Country Distribution': 1},\n", " {'Website': 'hoctot.net.vn', 'Country Distribution': 1},\n", " {'Website': 'cadasa.vn', 'Country Distribution': 5},\n", " {'Website': 'cloudclass.edu.vn', 'Country Distribution': 3},\n", " {'Website': 'tigodoo.com', 'Country Distribution': 10},\n", " {'Website': 'weupgroup.vn', 'Country Distribution': 4},\n", " {'Website': 'elearning.hikvision.com', 'Country Distribution': 34},\n", " {'Website': 'eduspace.vn', 'Country Distribution': 9},\n", " {'Website': 'funix.edu.vn', 'Country Distribution': 16},\n", " {'Website': 'engo.edu.vn', 'Country Distribution': 0},\n", " {'Website': 'monsterlab.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'amber.edu.vn', 'Country Distribution': 5},\n", " {'Website': 'empire.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'vuasoft.com', 'Country Distribution': 1},\n", " {'Website': 'codx.vn', 'Country Distribution': 3},\n", " {'Website': 'codegym.vn', 'Country Distribution': 6},\n", " {'Website': 'elearningstudio.vn', 'Country Distribution': 0},\n", " {'Website': 'omt.vn', 'Country Distribution': 11},\n", " {'Website': 'koolsoftelearning.com', 'Country Distribution': 3},\n", " {'Website': 'viindoo.com', 'Country Distribution': 28},\n", " {'Website': 'daotaonoibo.vn', 'Country Distribution': 1},\n", " {'Website': 'pma.edu.vn', 'Country Distribution': 4},\n", " {'Website': 'codelearn.io', 'Country Distribution': 13},\n", " {'Website': 'mellori.vn', 'Country Distribution': 0},\n", " {'Website': 'theenest.edu.vn', 'Country Distribution': 5},\n", " {'Website': 'viete.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'garastem.com', 'Country Distribution': 3},\n", " {'Website': 'stemsquare.vn', 'Country Distribution': 1},\n", " {'Website': 'tutoru.vn', 'Country Distribution': 1},\n", " {'Website': 'toppy.edu.vn', 'Country Distribution': 0},\n", " {'Website': 'hanoiedu.com', 'Country Distribution': 2},\n", " {'Website': 'edupay.asia', 'Country Distribution': 4},\n", " {'Website': 'tesse.io', 'Country Distribution': 32},\n", " {'Website': 'iostudy.net', 'Country Distribution': 10},\n", " {'Website': 'sunbot.vn', 'Country Distribution': 1},\n", " {'Website': 'luyenthidaihoc247.com', 'Country Distribution': 0},\n", " {'Website': 'tutorin.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'vietrobot.edu.vn', 'Country Distribution': 4},\n", " {'Website': 'estudy.langmaster.vn', 'Country Distribution': 1},\n", " {'Website': 'aztest.vn', 'Country Distribution': 2},\n", " {'Website': 'lora.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'popoky.com', 'Country Distribution': 0},\n", " {'Website': 'mindspaeduvn.wordpress.com', 'Country Distribution': 0},\n", " {'Website': 'stemlab.vn', 'Country Distribution': 1},\n", " {'Website': 'online2study.com', 'Country Distribution': 0},\n", " {'Website': 'robotstemtpa.vn', 'Country Distribution': 3},\n", " {'Website': 'tiengtrungzhongruan.com', 'Country Distribution': 0},\n", " {'Website': 'nhatnghe.net', 'Country Distribution': 1},\n", " {'Website': 'cmcts.com.vn', 'Country Distribution': 4},\n", " {'Website': 'memrise.com', 'Country Distribution': 112},\n", " {'Website': 'study4.com', 'Country Distribution': 15},\n", " {'Website': 'ecourses.vn', 'Country Distribution': 2},\n", " {'Website': 'vnschool.net', 'Country Distribution': 8},\n", " {'Website': 'codecademy.com', 'Country Distribution': 133},\n", " {'Website': 'perfect.com.vn', 'Country Distribution': 1},\n", " {'Website': 'classdojo.com', 'Country Distribution': 107},\n", " {'Website': 'algo.edu.vn', 'Country Distribution': 24},\n", " {'Website': 'gln.edu.vn', 'Country Distribution': 5},\n", " {'Website': 'learnova.vn', 'Country Distribution': 1},\n", " {'Website': 'school.fts.com.vn', 'Country Distribution': 0},\n", " {'Website': 'koseionline.vn', 'Country Distribution': 4},\n", " {'Website': 'w3schools.com', 'Country Distribution': 141},\n", " {'Website': 'marcomdo.edu.vn', 'Country Distribution': 9},\n", " {'Website': 'hocviendoanhnhanpti.edu.vn', 'Country Distribution': 3},\n", " {'Website': 'hoclaptrinhonline.asia', 'Country Distribution': 1},\n", " {'Website': 'antdemy.vn', 'Country Distribution': 4},\n", " {'Website': 'tedu.com.vn', 'Country Distribution': 3},\n", " {'Website': 'lets.goclass.vn', 'Country Distribution': 1},\n", " {'Website': 'tailieunhanh.com', 'Country Distribution': 4},\n", " {'Website': 'khotrithucso.com', 'Country Distribution': 1},\n", " {'Website': 'xemtailieu.net', 'Country Distribution': 6},\n", " {'Website': 'tailieu.vn', 'Country Distribution': 16},\n", " {'Website': '123docz.net', 'Country Distribution': 46},\n", " {'Website': 'tailieuchung.com', 'Country Distribution': 7},\n", " {'Website': 'tailieuxanh.com', 'Country Distribution': 2},\n", " {'Website': 'olm.vn', 'Country Distribution': 46},\n", " {'Website': 'mathvn.com', 'Country Distribution': 10},\n", " {'Website': 'thuvienhoclieu.com', 'Country Distribution': 6},\n", " {'Website': 'doctailieu.com', 'Country Distribution': 10},\n", " {'Website': 'yotalk.edu.vn', 'Country Distribution': 0},\n", " {'Website': 'violet.vn', 'Country Distribution': 14},\n", " {'Website': 'hoctai.vn', 'Country Distribution': 1},\n", " {'Website': 'timdapan.com', 'Country Distribution': 4},\n", " {'Website': 'toanmath.com', 'Country Distribution': 11},\n", " {'Website': 'englishcats.com', 'Country Distribution': 10},\n", " {'Website': 'hoc360.net', 'Country Distribution': 4},\n", " {'Website': 'dvtienich.com', 'Country Distribution': 5},\n", " {'Website': 'metaisach.com', 'Country Distribution': 5},\n", " {'Website': 'hayhochoi.vn', 'Country Distribution': 4},\n", " {'Website': 'cunghocvui.com', 'Country Distribution': 5},\n", " {'Website': 'tuhoc365.vn', 'Country Distribution': 10},\n", " {'Website': 'baigiangtoanhoc.com', 'Country Distribution': 1},\n", " {'Website': 'elight.edu.vn', 'Country Distribution': 6},\n", " {'Website': 'vndoc.com', 'Country Distribution': 17},\n", " {'Website': 'cunghoctoan.com', 'Country Distribution': 5},\n", " {'Website': 'tienganhmoingay.com', 'Country Distribution': 7},\n", " {'Website': 'sachhoc.com', 'Country Distribution': 23},\n", " {'Website': 'luyenthithptquocgia.com', 'Country Distribution': 0},\n", " {'Website': 'lop12.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'luyentap247.com', 'Country Distribution': 1},\n", " {'Website': 'tracnghiemonline.vn', 'Country Distribution': 2},\n", " {'Website': 'hoctoan24h.net', 'Country Distribution': 2},\n", " {'Website': 'studytienganh.vn', 'Country Distribution': 11},\n", " {'Website': 'phuongtrinhhoahoc.com', 'Country Distribution': 1},\n", " {'Website': 'download.vn', 'Country Distribution': 18},\n", " {'Website': 'sachgiaibaitap.com', 'Country Distribution': 8},\n", " {'Website': 'engbreaking.com', 'Country Distribution': 4},\n", " {'Website': 'stepup.edu.vn', 'Country Distribution': 5},\n", " {'Website': 'thayphu.net', 'Country Distribution': 2},\n", " {'Website': 'cungthi.online', 'Country Distribution': 4},\n", " {'Website': 'giaibaitap123.com', 'Country Distribution': 4},\n", " {'Website': 'hanhtrangso.nxbgd.vn', 'Country Distribution': 4},\n", " {'Website': 'edubox.vn', 'Country Distribution': 1},\n", " {'Website': 'e-pro.vn', 'Country Distribution': 1},\n", " {'Website': 'giaibainhanh.com', 'Country Distribution': 0},\n", " {'Website': 'learn.pops.vn', 'Country Distribution': 2},\n", " {'Website': 'vted.vn', 'Country Distribution': 6},\n", " {'Website': 'i-learn.vn', 'Country Distribution': 5},\n", " {'Website': 'anylearn.vn', 'Country Distribution': 6},\n", " {'Website': 'tutoro.vn', 'Country Distribution': 0},\n", " {'Website': 'onthisinh.vn', 'Country Distribution': 0},\n", " {'Website': 'vn.got-it.ai', 'Country Distribution': 6},\n", " {'Website': 'htcon.vn', 'Country Distribution': 2},\n", " {'Website': 'eduking.vn', 'Country Distribution': 1},\n", " {'Website': 'test24h.vn', 'Country Distribution': 2},\n", " {'Website': 'blacasa.vn', 'Country Distribution': 14},\n", " {'Website': 'yolearn.vn', 'Country Distribution': 2},\n", " {'Website': 'e-learn.com.vn', 'Country Distribution': 7},\n", " {'Website': 'baivan.net', 'Country Distribution': 1},\n", " {'Website': 'etok.vn', 'Country Distribution': 0},\n", " {'Website': 'ejoy-english.com', 'Country Distribution': 105},\n", " {'Website': 'monkey.edu.vn', 'Country Distribution': 16},\n", " {'Website': 'prep.vn', 'Country Distribution': 3},\n", " {'Website': 'shub.edu.vn', 'Country Distribution': 35},\n", " {'Website': 'unitz.app', 'Country Distribution': 0},\n", " {'Website': 'vn.bitdegree.org', 'Country Distribution': 6},\n", " {'Website': 'smart-edu.vn', 'Country Distribution': 2},\n", " {'Website': 'daymai.vn', 'Country Distribution': 5},\n", " {'Website': 'khaosat.me', 'Country Distribution': 9},\n", " {'Website': 'access.flyer.vn', 'Country Distribution': 1},\n", " {'Website': 'tete.ai', 'Country Distribution': 0},\n", " {'Website': 'cga.school', 'Country Distribution': 44},\n", " {'Website': 'onetouch.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'hamchoi.vn', 'Country Distribution': 6},\n", " {'Website': 'k12lms.vn', 'Country Distribution': 1},\n", " {'Website': 'testcenter.vn', 'Country Distribution': 1},\n", " {'Website': 'teachmint.com', 'Country Distribution': 76},\n", " {'Website': 'shop.ielts-nguyenhuyen.com', 'Country Distribution': 1},\n", " {'Website': 'base.vn', 'Country Distribution': 11},\n", " {'Website': 'testing.vn', 'Country Distribution': 1},\n", " {'Website': 'ieltsmaterial.com', 'Country Distribution': 76},\n", " {'Website': 'zim.vn', 'Country Distribution': 26},\n", " {'Website': 'writing9.com', 'Country Distribution': 75},\n", " {'Website': 'azota.vn', 'Country Distribution': 11},\n", " {'Website': 'onluyen.vn', 'Country Distribution': 5},\n", " {'Website': 'dtsoft.vn', 'Country Distribution': 13},\n", " {'Website': 'k12online.vn', 'Country Distribution': 4},\n", " {'Website': 'ssstudy.vn', 'Country Distribution': 1},\n", " {'Website': 'openstudy.vn', 'Country Distribution': 1},\n", " {'Website': 'hoc24.vn', 'Country Distribution': 17},\n", " {'Website': 'hocmai.vn', 'Country Distribution': 21},\n", " {'Website': 'goingsunny.com', 'Country Distribution': 0},\n", " {'Website': 'easyedu.vn', 'Country Distribution': 2},\n", " {'Website': 'study.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'ayotree.com', 'Country Distribution': 12},\n", " {'Website': 'kienguru.vn', 'Country Distribution': 7},\n", " {'Website': 'enetviet.com', 'Country Distribution': 6},\n", " {'Website': 'luyenthipro.com', 'Country Distribution': 6},\n", " {'Website': 'smas.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'perfect.com.vn', 'Country Distribution': 1},\n", " {'Website': '789.vn', 'Country Distribution': 0},\n", " {'Website': 'eschoolvn.edu.vn', 'Country Distribution': 10},\n", " {'Website': 'one.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'emis.misa.vn', 'Country Distribution': 2},\n", " {'Website': 'study365.edu.vn', 'Country Distribution': 0},\n", " {'Website': 'datacamp.com', 'Country Distribution': 137},\n", " {'Website': 'thuhocphi.com', 'Country Distribution': 2},\n", " {'Website': 'pluralsight.com', 'Country Distribution': 129},\n", " {'Website': 'psctelecom.com.vn', 'Country Distribution': 4},\n", " {'Website': 'hellochao.vn', 'Country Distribution': 3},\n", " {'Website': 'ninoapp.vn', 'Country Distribution': 1},\n", " {'Website': 'bachkim.vn', 'Country Distribution': 1},\n", " {'Website': 'littlelives.com', 'Country Distribution': 18},\n", " {'Website': 'softenmind.com', 'Country Distribution': 4},\n", " {'Website': 'code4startup.com', 'Country Distribution': 23},\n", " {'Website': 'udacity.com', 'Country Distribution': 114},\n", " {'Website': 'edumall.vn', 'Country Distribution': 9},\n", " {'Website': 'codecombat.com', 'Country Distribution': 79},\n", " {'Website': 'freecodecamp.org', 'Country Distribution': 139},\n", " {'Website': 'wewiin.com', 'Country Distribution': 1},\n", " {'Website': 'cati.edu.vn', 'Country Distribution': 9},\n", " {'Website': 'codecademy.com', 'Country Distribution': 133},\n", " {'Website': 'qlmn.vn', 'Country Distribution': 1},\n", " {'Website': 'kc.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'vnresource.vn', 'Country Distribution': 2},\n", " {'Website': 'pointavenue.com', 'Country Distribution': 19},\n", " {'Website': 'luyenthipro.com', 'Country Distribution': 6},\n", " {'Website': 'itest.com.vn', 'Country Distribution': 6},\n", " {'Website': 'busuu.com', 'Country Distribution': 126},\n", " {'Website': 'akadon.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'thanhtoan.violet.vn', 'Country Distribution': 0},\n", " {'Website': 'phuoctienb.violet.vn', 'Country Distribution': 1},\n", " {'Website': 'tnmaker.net', 'Country Distribution': 2},\n", " {'Website': 'colearn.vn', 'Country Distribution': 1},\n", " {'Website': 'herventure.org', 'Country Distribution': 7},\n", " {'Website': 'cungthi.vn', 'Country Distribution': 0},\n", " {'Website': 'ongbut.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'helloenglish.com', 'Country Distribution': 121},\n", " {'Website': 'steprimo.com', 'Country Distribution': 92},\n", " {'Website': 'ringleplus.com', 'Country Distribution': 17},\n", " {'Website': 'testchinese.com', 'Country Distribution': 0},\n", " {'Website': 'tryhsk.com', 'Country Distribution': 11},\n", " {'Website': 'uommamhientai.edu.vn', 'Country Distribution': 0},\n", " {'Website': 'tmo.taimienphi.vn', 'Country Distribution': 3},\n", " {'Website': 'hskonline.com', 'Country Distribution': 40},\n", " {'Website': 'testpro.com.vn', 'Country Distribution': 1},\n", " {'Website': 'icorrect.vn', 'Country Distribution': 3},\n", " {'Website': 'acabiz.vn', 'Country Distribution': 1},\n", " {'Website': 'daykemtainha.vn', 'Country Distribution': 3},\n", " {'Website': 'jobway.vn', 'Country Distribution': 0},\n", " {'Website': 'onthitracnghiem.online', 'Country Distribution': 1},\n", " {'Website': 'bigschool.vn', 'Country Distribution': 1},\n", " {'Website': 'lize.vn', 'Country Distribution': 0},\n", " {'Website': 'onthi247.vn', 'Country Distribution': 6},\n", " {'Website': 'thivao10.net', 'Country Distribution': 0},\n", " {'Website': 'maxsolution.com.vn', 'Country Distribution': 4},\n", " {'Website': 'megaschool.edu.vn', 'Country Distribution': 3},\n", " {'Website': 'quangich.com', 'Country Distribution': 3},\n", " {'Website': 'tienganhk12.com', 'Country Distribution': 13},\n", " {'Website': 'edulive.net', 'Country Distribution': 1},\n", " {'Website': 'thionline.com.vn', 'Country Distribution': 0},\n", " {'Website': 'mschool.mobiedu.vn', 'Country Distribution': 0},\n", " {'Website': 'iface.vn', 'Country Distribution': 0},\n", " {'Website': 'testbank.vn', 'Country Distribution': 4},\n", " {'Website': 'vio.edu.vn', 'Country Distribution': 9},\n", " {'Website': 'itest.com.vn', 'Country Distribution': 6},\n", " {'Website': 'onthidaihoc247.com', 'Country Distribution': 0},\n", " {'Website': 'vn.elsaspeak.com', 'Country Distribution': 17},\n", " {'Website': 'classclap.vn', 'Country Distribution': 5},\n", " {'Website': 'thinkingschool.vn', 'Country Distribution': 3},\n", " {'Website': 'vuihoc.vn', 'Country Distribution': 21},\n", " {'Website': 'app.onluyen.vn', 'Country Distribution': 2},\n", " {'Website': 'hocmai247.vn', 'Country Distribution': 1},\n", " {'Website': 'lingua-attack.com', 'Country Distribution': 32},\n", " {'Website': 'schoolcento.com', 'Country Distribution': 2},\n", " {'Website': 'zunia.vn', 'Country Distribution': 2},\n", " {'Website': 'ahaslides.com', 'Country Distribution': 122},\n", " {'Website': 'asknow.vn', 'Country Distribution': 1},\n", " {'Website': 'yoot.vn', 'Country Distribution': 16},\n", " {'Website': 'jobtest.vn', 'Country Distribution': 6},\n", " {'Website': 'riki.edu.vn', 'Country Distribution': 5},\n", " {'Website': 'yaho.life', 'Country Distribution': 3},\n", " {'Website': 'ebomb.edu.vn', 'Country Distribution': 3},\n", " {'Website': 'organizations.cambly.com', 'Country Distribution': 22},\n", " {'Website': 'englishninjas.com', 'Country Distribution': 3},\n", " {'Website': 'sofiri.com', 'Country Distribution': 37},\n", " {'Website': 'schoolup.vn', 'Country Distribution': 1},\n", " {'Website': 'vtutor.com', 'Country Distribution': 27},\n", " {'Website': 'mobiedu.vn', 'Country Distribution': 3},\n", " {'Website': 'oceandemy.com', 'Country Distribution': 0},\n", " {'Website': 'schoolbird.co', 'Country Distribution': 0},\n", " {'Website': 'hoclagioi.net', 'Country Distribution': 1},\n", " {'Website': 'holospeak.co', 'Country Distribution': 2},\n", " {'Website': 'pantado.edu.vn', 'Country Distribution': 16},\n", " {'Website': 'igstutor.vn', 'Country Distribution': 1},\n", " {'Website': 'ketnoigiaoduc.vn', 'Country Distribution': 3},\n", " {'Website': 'monkey.edu.vn', 'Country Distribution': 16},\n", " {'Website': 'prep.vn', 'Country Distribution': 3},\n", " {'Website': 'shub.edu.vn', 'Country Distribution': 35},\n", " {'Website': 'unitz.app', 'Country Distribution': 0},\n", " {'Website': 'vn.bitdegree.org', 'Country Distribution': 6},\n", " {'Website': 'smart-edu.vn', 'Country Distribution': 2},\n", " {'Website': 'daymai.vn', 'Country Distribution': 5},\n", " {'Website': 'khaosat.me', 'Country Distribution': 9},\n", " {'Website': 'access.flyer.vn', 'Country Distribution': 1},\n", " {'Website': 'tete.ai', 'Country Distribution': 0},\n", " {'Website': 'cga.school', 'Country Distribution': 44},\n", " {'Website': 'onetouch.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'hamchoi.vn', 'Country Distribution': 6},\n", " {'Website': 'k12lms.vn', 'Country Distribution': 1},\n", " {'Website': 'testcenter.vn', 'Country Distribution': 1},\n", " {'Website': 'teachmint.com', 'Country Distribution': 76},\n", " {'Website': 'shop.ielts-nguyenhuyen.com', 'Country Distribution': 1},\n", " {'Website': 'base.vn', 'Country Distribution': 11},\n", " {'Website': 'testing.vn', 'Country Distribution': 1},\n", " {'Website': 'ieltsmaterial.com', 'Country Distribution': 76},\n", " {'Website': 'zim.vn', 'Country Distribution': 26},\n", " {'Website': 'writing9.com', 'Country Distribution': 75},\n", " {'Website': 'cuduapp.com', 'Country Distribution': 0},\n", " {'Website': 'lazi.vn', 'Country Distribution': 18},\n", " {'Website': 'hoc247.net', 'Country Distribution': 24},\n", " {'Website': 'schoolonline.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'tech12h.com', 'Country Distribution': 12},\n", " {'Website': 'teky.edu.vn', 'Country Distribution': 13},\n", " {'Website': 'yola.vn', 'Country Distribution': 8},\n", " {'Website': 'tuyensinh247.com', 'Country Distribution': 33},\n", " {'Website': 'thiquocgia.vn', 'Country Distribution': 6},\n", " {'Website': 'school.onluyen.vn', 'Country Distribution': 1},\n", " {'Website': 'wewiin.com', 'Country Distribution': 1},\n", " {'Website': 'mclass.vn', 'Country Distribution': 5},\n", " {'Website': 'dodaihoc.edubit.vn', 'Country Distribution': 0},\n", " {'Website': 'vietjack.com', 'Country Distribution': 40},\n", " {'Website': 'ioe.vn', 'Country Distribution': 8},\n", " {'Website': 'loigiaihay.com', 'Country Distribution': 31},\n", " {'Website': 'moon.vn', 'Country Distribution': 35},\n", " {'Website': 'mozaweb.com', 'Country Distribution': 69},\n", " {'Website': 'vungoi.vn', 'Country Distribution': 12},\n", " {'Website': 'novateen.vn', 'Country Distribution': 2},\n", " {'Website': 'tienganh123.com', 'Country Distribution': 11},\n", " {'Website': 'itutor.world', 'Country Distribution': 0},\n", " {'Website': 'luyenthi123.com', 'Country Distribution': 5},\n", " {'Website': 'giainhanh.vn', 'Country Distribution': 1},\n", " {'Website': 'matific.com', 'Country Distribution': 81},\n", " {'Website': 'fqa.vn', 'Country Distribution': 11},\n", " {'Website': 'eduquiz.vn', 'Country Distribution': 5},\n", " {'Website': 'study.hanoi.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'thithu.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'tailieunhanh.com', 'Country Distribution': 4},\n", " {'Website': 'khotrithucso.com', 'Country Distribution': 1},\n", " {'Website': 'xemtailieu.net', 'Country Distribution': 6},\n", " {'Website': 'tailieu.vn', 'Country Distribution': 16},\n", " {'Website': '123docz.net', 'Country Distribution': 46},\n", " {'Website': 'tailieuchung.com', 'Country Distribution': 7},\n", " {'Website': 'tailieuxanh.com', 'Country Distribution': 2},\n", " {'Website': 'olm.vn', 'Country Distribution': 46},\n", " {'Website': 'mathvn.com', 'Country Distribution': 10},\n", " {'Website': 'thuvienhoclieu.com', 'Country Distribution': 6},\n", " {'Website': 'doctailieu.com', 'Country Distribution': 10},\n", " {'Website': 'yotalk.edu.vn', 'Country Distribution': 0},\n", " {'Website': 'violet.vn', 'Country Distribution': 14},\n", " {'Website': 'hoctai.vn', 'Country Distribution': 1},\n", " {'Website': 'timdapan.com', 'Country Distribution': 4},\n", " {'Website': 'toanmath.com', 'Country Distribution': 11},\n", " {'Website': 'englishcats.com', 'Country Distribution': 10},\n", " {'Website': 'hoc360.net', 'Country Distribution': 4},\n", " {'Website': 'hoidap247.com', 'Country Distribution': 29},\n", " {'Website': 'click2learn.vn', 'Country Distribution': 0},\n", " {'Website': 'herapo.vn', 'Country Distribution': 1},\n", " {'Website': 'qandastudy.vn', 'Country Distribution': 1},\n", " {'Website': 'edubit.vn', 'Country Distribution': 4},\n", " {'Website': 'ohstem.vn', 'Country Distribution': 2},\n", " {'Website': 'geniebook.com', 'Country Distribution': 36},\n", " {'Website': 'omegaenglish.vn', 'Country Distribution': 1},\n", " {'Website': 'toanhoc247.com', 'Country Distribution': 1},\n", " {'Website': 'toanhoc77.wordpress.com', 'Country Distribution': 1},\n", " {'Website': 'hocthoi.net', 'Country Distribution': 1},\n", " {'Website': 'hachium.com', 'Country Distribution': 1},\n", " {'Website': 'alohoc.vn', 'Country Distribution': 2},\n", " {'Website': 'clevai.edu.vn', 'Country Distribution': 6},\n", " {'Website': 'mathx.vn', 'Country Distribution': 5},\n", " {'Website': 'trangnguyen.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'sne.vn', 'Country Distribution': 0},\n", " {'Website': 'baigiangxanh.com', 'Country Distribution': 1},\n", " {'Website': 'classin.com.vn', 'Country Distribution': 6},\n", " {'Website': 'thionline.com.vn', 'Country Distribution': 0},\n", " {'Website': 'stemhouse.edu.vn', 'Country Distribution': 7},\n", " {'Website': 'fermat.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'haylamdo.com', 'Country Distribution': 6},\n", " {'Website': 'hoola.vn', 'Country Distribution': 1},\n", " {'Website': 'edunet.vn', 'Country Distribution': 2},\n", " {'Website': 'robotstemtpa.vn', 'Country Distribution': 3},\n", " {'Website': 'trainercentral.com', 'Country Distribution': 18},\n", " {'Website': 'des.vn', 'Country Distribution': 5},\n", " {'Website': 'agilearn.vn', 'Country Distribution': 2},\n", " {'Website': 'peo.vn', 'Country Distribution': 1},\n", " {'Website': 'mvvacademy.edu.vn', 'Country Distribution': 4},\n", " {'Website': 'edutek.io', 'Country Distribution': 1},\n", " {'Website': 'pdca.vn', 'Country Distribution': 3},\n", " {'Website': 'dostem.edu.vn', 'Country Distribution': 4},\n", " {'Website': 'digiland.com.vn', 'Country Distribution': 1},\n", " {'Website': 'oes.vn', 'Country Distribution': 15},\n", " {'Website': 'elitelearning.vn', 'Country Distribution': 1},\n", " {'Website': 'edubit.vn', 'Country Distribution': 4},\n", " {'Website': 'vieted.com', 'Country Distribution': 3},\n", " {'Website': 'lms.tntalent.vn', 'Country Distribution': 1},\n", " {'Website': 'cls.vn', 'Country Distribution': 1},\n", " {'Website': 'mge.vn', 'Country Distribution': 4},\n", " {'Website': 'bitlearn.vn', 'Country Distribution': 1},\n", " {'Website': 'gitiho.com', 'Country Distribution': 13},\n", " {'Website': 'hocviensangtao.edu.vn', 'Country Distribution': 5},\n", " {'Website': 'unica.vn', 'Country Distribution': 20},\n", " {'Website': 'dvtienich.com', 'Country Distribution': 5},\n", " {'Website': 'metaisach.com', 'Country Distribution': 5},\n", " {'Website': 'hayhochoi.vn', 'Country Distribution': 4},\n", " {'Website': 'cunghocvui.com', 'Country Distribution': 5},\n", " {'Website': 'tuhoc365.vn', 'Country Distribution': 10},\n", " {'Website': 'baigiangtoanhoc.com', 'Country Distribution': 1},\n", " {'Website': 'elight.edu.vn', 'Country Distribution': 6},\n", " {'Website': 'vndoc.com', 'Country Distribution': 17},\n", " {'Website': 'cunghoctoan.com', 'Country Distribution': 5},\n", " {'Website': 'tienganhmoingay.com', 'Country Distribution': 7},\n", " {'Website': 'sachhoc.com', 'Country Distribution': 23},\n", " {'Website': 'luyenthithptquocgia.com', 'Country Distribution': 0},\n", " {'Website': 'lop12.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'luyentap247.com', 'Country Distribution': 1},\n", " {'Website': 'tracnghiemonline.vn', 'Country Distribution': 2},\n", " {'Website': 'hoctoan24h.net', 'Country Distribution': 2},\n", " {'Website': 'studytienganh.vn', 'Country Distribution': 11},\n", " {'Website': 'phuongtrinhhoahoc.com', 'Country Distribution': 1},\n", " {'Website': 'download.vn', 'Country Distribution': 18},\n", " {'Website': 'sachgiaibaitap.com', 'Country Distribution': 8},\n", " {'Website': 'engbreaking.com', 'Country Distribution': 4},\n", " {'Website': 'stepup.edu.vn', 'Country Distribution': 5},\n", " {'Website': 'thayphu.net', 'Country Distribution': 2},\n", " {'Website': 'cungthi.online', 'Country Distribution': 4},\n", " {'Website': 'giaibaitap123.com', 'Country Distribution': 4},\n", " {'Website': 'zaidap.com', 'Country Distribution': 2},\n", " {'Website': 'wetalk.school', 'Country Distribution': 0},\n", " {'Website': 'toploigiai.vn', 'Country Distribution': 9},\n", " {'Website': 'codekitten.vn', 'Country Distribution': 20},\n", " {'Website': 'gamma.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'holospeak.vn', 'Country Distribution': 0},\n", " {'Website': 'vnedu.vn', 'Country Distribution': 8},\n", " {'Website': 'voca.vn', 'Country Distribution': 2},\n", " {'Website': 'kidsup.net', 'Country Distribution': 5},\n", " {'Website': 'dungmori.com', 'Country Distribution': 6},\n", " {'Website': 'loga.vn', 'Country Distribution': 4},\n", " {'Website': 'baitapsgk.com', 'Country Distribution': 1},\n", " {'Website': 'vi.duolingo.com', 'Country Distribution': 5},\n", " {'Website': 'kidsloop.vn', 'Country Distribution': 0},\n", " {'Website': 'kids.hoc247.vn', 'Country Distribution': 2},\n", " {'Website': 'tata.edu.vn', 'Country Distribution': 3},\n", " {'Website': 'lets.goclass.vn', 'Country Distribution': 1},\n", " {'Website': 'study.hanoi.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'amazingmath.vn', 'Country Distribution': 2},\n", " {'Website': 'mycoolclass.com', 'Country Distribution': 4},\n", " {'Website': 'ekidenglish.edu.vn', 'Country Distribution': 3},\n", " {'Website': 'sachgiai.com', 'Country Distribution': 2},\n", " {'Website': 'dethikiemtra.com', 'Country Distribution': 10},\n", " {'Website': 'dethihocki.com', 'Country Distribution': 2},\n", " {'Website': 'thithu.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'thithu.vn', 'Country Distribution': 1},\n", " {'Website': 'matran.edu.vn', 'Country Distribution': 10},\n", " {'Website': 'onthidaihoc.online', 'Country Distribution': 0},\n", " {'Website': 'chiasehay.net', 'Country Distribution': 1},\n", " {'Website': 'izi.community', 'Country Distribution': 0},\n", " {'Website': 'lingobee.vn', 'Country Distribution': 2},\n", " {'Website': 'mshoagiaotiep.com', 'Country Distribution': 4},\n", " {'Website': 'lophocvui.com', 'Country Distribution': 0},\n", " {'Website': 'selfomy.com', 'Country Distribution': 2},\n", " {'Website': 'ebomb.edu.vn', 'Country Distribution': 3},\n", " {'Website': 'airclass.io', 'Country Distribution': 1},\n", " {'Website': 'dethithudaihoc.com', 'Country Distribution': 1},\n", " {'Website': 'sisap.vn', 'Country Distribution': 5},\n", " {'Website': 'lika.edu.vn', 'Country Distribution': 0},\n", " {'Website': 'thpt.iss.edu.vn', 'Country Distribution': 0},\n", " {'Website': 'luyenhoc.vn', 'Country Distribution': 1},\n", " {'Website': 'hoctot.net.vn', 'Country Distribution': 1},\n", " {'Website': 'cadasa.vn', 'Country Distribution': 5},\n", " {'Website': 'cloudclass.edu.vn', 'Country Distribution': 3},\n", " {'Website': 'hanhtrangso.nxbgd.vn', 'Country Distribution': 4},\n", " {'Website': 'edubox.vn', 'Country Distribution': 1},\n", " {'Website': 'e-pro.vn', 'Country Distribution': 1},\n", " {'Website': 'giaibainhanh.com', 'Country Distribution': 0},\n", " {'Website': 'learn.pops.vn', 'Country Distribution': 2},\n", " {'Website': 'vted.vn', 'Country Distribution': 6},\n", " {'Website': 'i-learn.vn', 'Country Distribution': 5},\n", " {'Website': 'anylearn.vn', 'Country Distribution': 6},\n", " {'Website': 'tutoro.vn', 'Country Distribution': 0},\n", " {'Website': 'onthisinh.vn', 'Country Distribution': 0},\n", " {'Website': 'vn.got-it.ai', 'Country Distribution': 6},\n", " {'Website': 'htcon.vn', 'Country Distribution': 2},\n", " {'Website': 'eduking.vn', 'Country Distribution': 1},\n", " {'Website': 'test24h.vn', 'Country Distribution': 2},\n", " {'Website': 'blacasa.vn', 'Country Distribution': 14},\n", " {'Website': 'yolearn.vn', 'Country Distribution': 2},\n", " {'Website': 'e-learn.com.vn', 'Country Distribution': 7},\n", " {'Website': 'baivan.net', 'Country Distribution': 1},\n", " {'Website': 'etok.vn', 'Country Distribution': 0},\n", " {'Website': 'ejoy-english.com', 'Country Distribution': 105},\n", " {'Website': 'tigodoo.com', 'Country Distribution': 10},\n", " {'Website': 'weupgroup.vn', 'Country Distribution': 4},\n", " {'Website': 'elearning.hikvision.com', 'Country Distribution': 34},\n", " {'Website': 'eduspace.vn', 'Country Distribution': 9},\n", " {'Website': 'funix.edu.vn', 'Country Distribution': 16},\n", " {'Website': 'engo.edu.vn', 'Country Distribution': 0},\n", " {'Website': 'monsterlab.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'amber.edu.vn', 'Country Distribution': 5},\n", " {'Website': 'empire.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'vuasoft.com', 'Country Distribution': 1},\n", " {'Website': 'codx.vn', 'Country Distribution': 3},\n", " {'Website': 'codegym.vn', 'Country Distribution': 6},\n", " {'Website': 'elearningstudio.vn', 'Country Distribution': 0},\n", " {'Website': 'omt.vn', 'Country Distribution': 11},\n", " {'Website': 'koolsoftelearning.com', 'Country Distribution': 3},\n", " {'Website': 'viindoo.com', 'Country Distribution': 28},\n", " {'Website': 'daotaonoibo.vn', 'Country Distribution': 1},\n", " {'Website': 'pma.edu.vn', 'Country Distribution': 4},\n", " {'Website': 'codelearn.io', 'Country Distribution': 13},\n", " {'Website': 'mellori.vn', 'Country Distribution': 0},\n", " {'Website': 'theenest.edu.vn', 'Country Distribution': 5},\n", " {'Website': 'viete.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'garastem.com', 'Country Distribution': 3},\n", " {'Website': 'stemsquare.vn', 'Country Distribution': 1},\n", " {'Website': 'tutoru.vn', 'Country Distribution': 1},\n", " {'Website': 'toppy.edu.vn', 'Country Distribution': 0},\n", " {'Website': 'hanoiedu.com', 'Country Distribution': 2},\n", " {'Website': 'edupay.asia', 'Country Distribution': 4},\n", " {'Website': 'tesse.io', 'Country Distribution': 32},\n", " {'Website': 'iostudy.net', 'Country Distribution': 10},\n", " {'Website': 'sunbot.vn', 'Country Distribution': 1},\n", " {'Website': 'luyenthidaihoc247.com', 'Country Distribution': 0},\n", " {'Website': 'tutorin.edu.vn', 'Country Distribution': 2},\n", " {'Website': 'vietrobot.edu.vn', 'Country Distribution': 4},\n", " {'Website': 'estudy.langmaster.vn', 'Country Distribution': 1},\n", " {'Website': 'aztest.vn', 'Country Distribution': 2},\n", " {'Website': 'lora.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'popoky.com', 'Country Distribution': 0},\n", " {'Website': 'mindspaeduvn.wordpress.com', 'Country Distribution': 0},\n", " {'Website': 'stemlab.vn', 'Country Distribution': 1},\n", " {'Website': 'online2study.com', 'Country Distribution': 0},\n", " {'Website': 'robotstemtpa.vn', 'Country Distribution': 3},\n", " {'Website': 'tiengtrungzhongruan.com', 'Country Distribution': 0},\n", " {'Website': 'nhatnghe.net', 'Country Distribution': 1},\n", " {'Website': 'cmcts.com.vn', 'Country Distribution': 4},\n", " {'Website': 'memrise.com', 'Country Distribution': 112},\n", " {'Website': 'study4.com', 'Country Distribution': 15},\n", " {'Website': 'ecourses.vn', 'Country Distribution': 2},\n", " {'Website': 'vnschool.net', 'Country Distribution': 8},\n", " {'Website': 'codecademy.com', 'Country Distribution': 133},\n", " {'Website': 'perfect.com.vn', 'Country Distribution': 1},\n", " {'Website': 'classdojo.com', 'Country Distribution': 107},\n", " {'Website': 'algo.edu.vn', 'Country Distribution': 24},\n", " {'Website': 'gln.edu.vn', 'Country Distribution': 5},\n", " {'Website': 'learnova.vn', 'Country Distribution': 1},\n", " {'Website': 'school.fts.com.vn', 'Country Distribution': 0},\n", " {'Website': 'koseionline.vn', 'Country Distribution': 4},\n", " {'Website': 'w3schools.com', 'Country Distribution': 141},\n", " {'Website': 'marcomdo.edu.vn', 'Country Distribution': 9},\n", " {'Website': 'hocviendoanhnhanpti.edu.vn', 'Country Distribution': 3},\n", " {'Website': 'hoclaptrinhonline.asia', 'Country Distribution': 1},\n", " {'Website': 'antdemy.vn', 'Country Distribution': 4},\n", " {'Website': 'tedu.com.vn', 'Country Distribution': 3},\n", " {'Website': 'lets.goclass.vn', 'Country Distribution': 1},\n", " {'Website': 'smart-edu.vn', 'Country Distribution': 2},\n", " {'Website': 'vnschool.net', 'Country Distribution': 8},\n", " {'Website': 'marugoto-online.jp', 'Country Distribution': 44},\n", " {'Website': 'edusoft.vn', 'Country Distribution': 1},\n", " {'Website': 'pro-seeds.com.vn', 'Country Distribution': 1},\n", " {'Website': 'vitanedu.com', 'Country Distribution': 2},\n", " {'Website': 'ikidz.edu.vn', 'Country Distribution': 0},\n", " {'Website': 'sc.edu.vn', 'Country Distribution': 1},\n", " {'Website': 'e-space.vn', 'Country Distribution': 3},\n", " {'Website': 'butphagioihan.ismart.edu.vn', 'Country Distribution': 0},\n", " {'Website': 'ismart.edu.vn', 'Country Distribution': 4},\n", " {'Website': 'aqtech.vn', 'Country Distribution': 2},\n", " {'Website': 'applyzones.com', 'Country Distribution': 66},\n", " {'Website': 'neo.vn', 'Country Distribution': 3},\n", " {'Website': 'unisoft.edu.vn', 'Country Distribution': 4}]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# get all the records of the data\n", "records_data = worksheet.get_all_records()\n", "# view the data\n", "records_data"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Latest value of the first column: unisoft.edu.vn\n"]}], "source": ["# Find the latest value of the first column\n", "values_first_column = worksheet.col_values(1)\n", "latest_value_first_column = values_first_column[-1]\n", "\n", "print(\"Latest value of the first column:\", latest_value_first_column)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data added successfully!\n"]}], "source": ["# Import the local Excel file into a DataFrame\n", "import pandas as pd\n", "from datetime import datetime \n", "time = datetime.today().strftime(\"%m-%d\")\n", "local_data = pd.read_excel(f'/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Ranking/CountryDistribution_{time}.xlsx')\n", "\n", "# Convert DataFrame to list of lists\n", "data_to_add = local_data.values.tolist()\n", "\n", "# Add the data to the worksheet\n", "worksheet.append_rows(data_to_add, value_input_option='USER_ENTERED')\n", "# The append_rows method is designed to add rows to the bottom of the worksheet, directly after the last existing row. \n", "\n", "print(\"Data added successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}