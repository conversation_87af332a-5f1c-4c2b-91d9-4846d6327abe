{"cells": [{"cell_type": "code", "execution_count": 249, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from datetime import datetime\n", "time = datetime.today().strftime(\"%d-%m\")\n", "df = pd.read_excel(f'/Users/<USER>/Downloads/merge_12-05.xlsx')"]}, {"cell_type": "code", "execution_count": 250, "metadata": {}, "outputs": [{"data": {"text/plain": ["(144, 18)"]}, "execution_count": 250, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": 251, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Target</th>\n", "      <th>Target Type</th>\n", "      <th>Metric</th>\n", "      <th>Database</th>\n", "      <th>Summary</th>\n", "      <th>2024-05</th>\n", "      <th>2024-06</th>\n", "      <th>2024-07</th>\n", "      <th>2024-08</th>\n", "      <th>2024-09</th>\n", "      <th>2024-10</th>\n", "      <th>2024-11</th>\n", "      <th>2024-12</th>\n", "      <th>2025-01</th>\n", "      <th>2025-02</th>\n", "      <th>2025-03</th>\n", "      <th>2025-04</th>\n", "      <th>2025-05</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>thithu.vn</td>\n", "      <td>root domain</td>\n", "      <td>Organic Traffic Cost</td>\n", "      <td>Worldwide</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>112</th>\n", "      <td>testing.vn</td>\n", "      <td>root domain</td>\n", "      <td>Paid Keywords</td>\n", "      <td>Worldwide</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>tedu.com.vn</td>\n", "      <td>root domain</td>\n", "      <td>Paid Keywords</td>\n", "      <td>Worldwide</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>134</th>\n", "      <td>thienan.vn/chitiet/san-pham/giai-phap-quan-tri...</td>\n", "      <td>subfolder</td>\n", "      <td>Organic Traffic Cost</td>\n", "      <td>vn</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>thienan.vn/chitiet/san-pham/giai-phap-dao-tao-...</td>\n", "      <td>subfolder</td>\n", "      <td>Paid Traffic</td>\n", "      <td>vn</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                Target  Target Type  \\\n", "56                                           thithu.vn  root domain   \n", "112                                         testing.vn  root domain   \n", "52                                         tedu.com.vn  root domain   \n", "134  thienan.vn/chitiet/san-pham/giai-phap-quan-tri...    subfolder   \n", "63   thienan.vn/chitiet/san-pham/giai-phap-dao-tao-...    subfolder   \n", "\n", "                   Metric   Database  Summary  2024-05  2024-06  2024-07  \\\n", "56   Organic Traffic Cost  Worldwide        0        0        0        0   \n", "112         Paid Keywords  Worldwide        0        0        0        0   \n", "52          Paid Keywords  Worldwide        0        0        0        0   \n", "134  Organic Traffic Cost         vn        5        0        0        0   \n", "63           Paid Traffic         vn        0        0        0        0   \n", "\n", "     2024-08  2024-09  2024-10  2024-11  2024-12  2025-01  2025-02  2025-03  \\\n", "56         0        0        0        0        0        0        0        0   \n", "112        0        0        0        0        0        0        0        0   \n", "52         0        0        0        0        0        0        0        0   \n", "134        0        0        4        1        0        0        0        0   \n", "63         0        0        0        0        0        0        0        0   \n", "\n", "     2025-04  2025-05  \n", "56         0      0.0  \n", "112        0      0.0  \n", "52         0      0.0  \n", "134        0      0.0  \n", "63         0      0.0  "]}, "execution_count": 251, "metadata": {}, "output_type": "execute_result"}], "source": ["df.sample(5)"]}, {"cell_type": "code", "execution_count": 252, "metadata": {}, "outputs": [], "source": ["# Function to extract values and create DataFrame\n", "def extract_and_create_df(dataframe):\n", "    # Extract the values\n", "    cell_1_1 = dataframe.iloc[0, 0]\n", "    cell_5_2 = dataframe.iloc[1, 4]\n", "    last_6_cells_first_row = dataframe.iloc[0, -13:-1].values\n", "\n", "    # Filter out columns that do not contain the extracted cells\n", "    columns_to_keep = ['Target'] + ['Summary'] + dataframe.columns[-13:-1].tolist()\n", "\n", "    # Create a DataFrame with the filtered columns\n", "    extracted_df = pd.DataFrame(columns=columns_to_keep)\n", "\n", "    # Append the extracted values to the DataFrame\n", "    extracted_df.loc[0, 'Target'] = cell_1_1\n", "    extracted_df.loc[0, 'Summary'] = cell_5_2\n", "    extracted_df.loc[0, columns_to_keep[2:]] = last_6_cells_first_row\n", "\n", "    return extracted_df\n", "\n", "# Split DataFrame into chunks of 6 rows and process each chunk\n", "chunk_size = 6\n", "num_chunks = len(df) // chunk_size\n", "remainder = len(df) % chunk_size\n", "\n", "# Initialize an empty list to store individual DataFrames\n", "all_dfs = []\n", "\n", "for i in range(num_chunks):\n", "    start_idx = i * chunk_size\n", "    end_idx = start_idx + chunk_size\n", "    chunk_df = df.iloc[start_idx:end_idx]\n", "    \n", "    # Perform extraction and DataFrame creation\n", "    extracted_df = extract_and_create_df(chunk_df)\n", "    \n", "    # Append the extracted DataFrame to the list\n", "    all_dfs.append(extracted_df)\n", "    \n", "# Process the remaining rows (if any)\n", "if remainder > 0:\n", "    remaining_df = df.iloc[num_chunks * chunk_size:]\n", "    extracted_df = extract_and_create_df(remaining_df)\n", "    all_dfs.append(extracted_df)\n", "\n", "# Concatenate all individual DataFrames into one DataFrame\n", "final_df = pd.concat(all_dfs, ignore_index=True)\n", "final_df = final_df.rename(columns={'Target':'Website','Summary':'Keywords'})"]}, {"cell_type": "code", "execution_count": 253, "metadata": {}, "outputs": [], "source": ["# final_df.head(20)"]}, {"cell_type": "code", "execution_count": 254, "metadata": {}, "outputs": [], "source": ["from datetime import datetime \n", "time = datetime.today().strftime(\"%m-%d\")\n", "final_df.to_excel(f'/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Ranking/Semrush_{time}.xlsx',index=None)"]}, {"cell_type": "code", "execution_count": 255, "metadata": {}, "outputs": [], "source": ["# !pip install gspread\n", "# !pip install oauth2client\n", "# !pip3 install --upgrade google-api-python-client "]}, {"cell_type": "code", "execution_count": 256, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Worksheet headers: [' Source', 'No', 'Tên Edtech', 'Edtech URL ', 'Website domain', '<PERSON>ản phẩm quốc tế', 'Segment', 'Category', '<PERSON><PERSON> khúc', '<PERSON><PERSON> loại', 'Thiếu data\\n(set formula)', '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05', '2024-06', '2024-07', '2024-08', '2024-09', '2024-10', '2024-11', '2024-12', '2025-01', '2025-02', '2025-03', '2025-04', 'Keywords', 'Backlink', 'Referring Domain', 'Backlink Quality (Domain authority)', 'Category rank\\nThe higher, the worse\\n-> cannot contain duplicate', 'Domain/Organic keyword - OLD', 'Branding keyword (thương hiệu - không thương hiệu)\\n-> value: brand > non-brand', 'Keyword Difficulty - OLD', 'Unique Visitor', 'Pages per visit', 'Average Visit Duration', 'Pages per visit - OLD', 'Bounce Rate (%)', 'Unique Visitor - OLD', 'Category rank\\nThe higher, the worse - OLD', 'Country Distribution\\n(Simiilarweb)', '', 'Keyword Difficulty\\n(https://ahrefs.com/keyword-difficulty/)\\nThe lower the score, the more difficult the keyword to get into top 10', 'Category rank', 'Website speed (%)', 'Website speed (%)\\nUpdate\\n(https://pagespeed.web.dev/)', 'Website authority\\n(https://ahrefs.com/website-authority-checker)', 'Website Security/Privacy\\n(https://sitecheck.sucuri.net/)', 'Accessibility compliance\\n(https://aeldata.com/accessibility-checker/)', 'Navigation & Readability\\n(https://www.webfx.com/tools/read-able/)\\nThe lower the score, the higher the age group to understand web content']\n", "Found 'Edtech' column at index 4\n", "Excel columns: ['Website', 'Keywords', '2024-05', '2024-06', '2024-07', '2024-08', '2024-09', '2024-10', '2024-11', '2024-12', '2025-01', '2025-02', '2025-03', '2025-04']\n", "Found 'Website' column: Website\n", "Match found: tailieunhanh.com (worksheet) matches tailieunhanh.com (Excel)\n", "  Will update column 'Keywords' with value '103953'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '567'\n", "  Will update column '2024-06' with value '656'\n", "  Will update column '2024-07' with value '956'\n", "  Will update column '2024-08' with value '1162'\n", "  Will update column '2024-09' with value '232'\n", "  Will update column '2024-10' with value '54'\n", "  Will update column '2024-11' with value '123'\n", "  Will update column '2024-12' with value '8692'\n", "  Will update column '2025-01' with value '309'\n", "  Will update column '2025-02' with value '2384'\n", "  Will update column '2025-03' with value '3575'\n", "  Will update column '2025-04' with value '574'\n", "Match found: tailieuxanh.com (worksheet) matches tailieuxanh.com (Excel)\n", "  Will update column 'Keywords' with value '133384'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '1091'\n", "  Will update column '2024-06' with value '858'\n", "  Will update column '2024-07' with value '740'\n", "  Will update column '2024-08' with value '724'\n", "  Will update column '2024-09' with value '304'\n", "  Will update column '2024-10' with value '30'\n", "  Will update column '2024-11' with value '136'\n", "  Will update column '2024-12' with value '1070'\n", "  Will update column '2025-01' with value '1283'\n", "  Will update column '2025-02' with value '2703'\n", "  Will update column '2025-03' with value '2320'\n", "  Will update column '2025-04' with value '4133'\n", "Match found: tata.edu.vn (worksheet) matches tata.edu.vn (Excel)\n", "  Will update column 'Keywords' with value '2957'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '2967'\n", "  Will update column '2024-06' with value '4321'\n", "  Will update column '2024-07' with value '2160'\n", "  Will update column '2024-08' with value '1858'\n", "  Will update column '2024-09' with value '10520'\n", "  Will update column '2024-10' with value '11126'\n", "  Will update column '2024-11' with value '11532'\n", "  Will update column '2024-12' with value '12921'\n", "  Will update column '2025-01' with value '11290'\n", "  Will update column '2025-02' with value '11110'\n", "  Will update column '2025-03' with value '9472'\n", "  Will update column '2025-04' with value '9397'\n", "Match found: teachmint.com (worksheet) matches teachmint.com (Excel)\n", "  Will update column 'Keywords' with value '2304727'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '769625'\n", "  Will update column '2024-06' with value '663317'\n", "  Will update column '2024-07' with value '654797'\n", "  Will update column '2024-08' with value '649996'\n", "  Will update column '2024-09' with value '606505'\n", "  Will update column '2024-10' with value '559151'\n", "  Will update column '2024-11' with value '502791'\n", "  Will update column '2024-12' with value '570117'\n", "  Will update column '2025-01' with value '533761'\n", "  Will update column '2025-02' with value '525687'\n", "  Will update column '2025-03' with value '411856'\n", "  Will update column '2025-04' with value '390242'\n", "Match found: tech12h.com (worksheet) matches tech12h.com (Excel)\n", "  Will update column 'Keywords' with value '1992686'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '334309'\n", "  Will update column '2024-06' with value '333530'\n", "  Will update column '2024-07' with value '315081'\n", "  Will update column '2024-08' with value '302645'\n", "  Will update column '2024-09' with value '290940'\n", "  Will update column '2024-10' with value '274405'\n", "  Will update column '2024-11' with value '240965'\n", "  Will update column '2024-12' with value '189274'\n", "  Will update column '2025-01' with value '170412'\n", "  Will update column '2025-02' with value '173649'\n", "  Will update column '2025-03' with value '136280'\n", "  Will update column '2025-04' with value '118492'\n", "Match found: tedu.com.vn (worksheet) matches tedu.com.vn (Excel)\n", "  Will update column 'Keywords' with value '66584'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '7000'\n", "  Will update column '2024-06' with value '6997'\n", "  Will update column '2024-07' with value '7131'\n", "  Will update column '2024-08' with value '8525'\n", "  Will update column '2024-09' with value '8416'\n", "  Will update column '2024-10' with value '7609'\n", "  Will update column '2024-11' with value '7549'\n", "  Will update column '2024-12' with value '7010'\n", "  Will update column '2025-01' with value '7363'\n", "  Will update column '2025-02' with value '7042'\n", "  Will update column '2025-03' with value '6734'\n", "  Will update column '2025-04' with value '6854'\n", "Match found: teky.edu.vn (worksheet) matches teky.edu.vn (Excel)\n", "  Will update column 'Keywords' with value '1010081'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '113130'\n", "  Will update column '2024-06' with value '178929'\n", "  Will update column '2024-07' with value '172757'\n", "  Will update column '2024-08' with value '217793'\n", "  Will update column '2024-09' with value '217676'\n", "  Will update column '2024-10' with value '209596'\n", "  Will update column '2024-11' with value '245257'\n", "  Will update column '2024-12' with value '239939'\n", "  Will update column '2025-01' with value '224597'\n", "  Will update column '2025-02' with value '230763'\n", "  Will update column '2025-03' with value '209132'\n", "  Will update column '2025-04' with value '175544'\n", "Match found: tesse.io (worksheet) matches tesse.io (Excel)\n", "  Will update column 'Keywords' with value '7285'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '1809'\n", "  Will update column '2024-06' with value '31'\n", "  Will update column '2024-07' with value '691'\n", "  Will update column '2024-08' with value '1131'\n", "  Will update column '2024-09' with value '1557'\n", "  Will update column '2024-10' with value '1037'\n", "  Will update column '2024-11' with value '146'\n", "  Will update column '2024-12' with value '0'\n", "  Will update column '2025-01' with value '0'\n", "  Will update column '2025-02' with value '0'\n", "  Will update column '2025-03' with value '0'\n", "  Will update column '2025-04' with value '0'\n", "Match found: test24h.vn (worksheet) matches test24h.vn (Excel)\n", "  Will update column 'Keywords' with value '520'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '0'\n", "  Will update column '2024-06' with value '0'\n", "  Will update column '2024-07' with value '0'\n", "  Will update column '2024-08' with value '1'\n", "  Will update column '2024-09' with value '2'\n", "  Will update column '2024-10' with value '11'\n", "  Will update column '2024-11' with value '13'\n", "  Will update column '2024-12' with value '12'\n", "  Will update column '2025-01' with value '5'\n", "  Will update column '2025-02' with value '0'\n", "  Will update column '2025-03' with value '0'\n", "  Will update column '2025-04' with value '6'\n", "Match found: testbank.vn (worksheet) matches testbank.vn (Excel)\n", "  Will update column 'Keywords' with value '3089'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '1267'\n", "  Will update column '2024-06' with value '1424'\n", "  Will update column '2024-07' with value '1557'\n", "  Will update column '2024-08' with value '2213'\n", "  Will update column '2024-09' with value '1723'\n", "  Will update column '2024-10' with value '1651'\n", "  Will update column '2024-11' with value '1432'\n", "  Will update column '2024-12' with value '1398'\n", "  Will update column '2025-01' with value '1564'\n", "  Will update column '2025-02' with value '1573'\n", "  Will update column '2025-03' with value '1615'\n", "  Will update column '2025-04' with value '1010'\n", "Match found: testchinese.com (worksheet) matches testchinese.com (Excel)\n", "  Will update column 'Keywords' with value '4'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '0'\n", "  Will update column '2024-06' with value '0'\n", "  Will update column '2024-07' with value '0'\n", "  Will update column '2024-08' with value '0'\n", "  Will update column '2024-09' with value '0'\n", "  Will update column '2024-10' with value '0'\n", "  Will update column '2024-11' with value '0'\n", "  Will update column '2024-12' with value '0'\n", "  Will update column '2025-01' with value '0'\n", "  Will update column '2025-02' with value '0'\n", "  Will update column '2025-03' with value '0'\n", "  Will update column '2025-04' with value '0'\n", "Match found: testing.vn (worksheet) matches testing.vn (Excel)\n", "  Will update column 'Keywords' with value '15619'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '4947'\n", "  Will update column '2024-06' with value '4447'\n", "  Will update column '2024-07' with value '4249'\n", "  Will update column '2024-08' with value '4234'\n", "  Will update column '2024-09' with value '12453'\n", "  Will update column '2024-10' with value '5891'\n", "  Will update column '2024-11' with value '5476'\n", "  Will update column '2024-12' with value '4583'\n", "  Will update column '2025-01' with value '5740'\n", "  Will update column '2025-02' with value '4264'\n", "  Will update column '2025-03' with value '4760'\n", "  Will update column '2025-04' with value '5444'\n", "Match found: thayphu.net (worksheet) matches thayphu.net (Excel)\n", "  Will update column 'Keywords' with value '56580'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '12059'\n", "  Will update column '2024-06' with value '9312'\n", "  Will update column '2024-07' with value '7254'\n", "  Will update column '2024-08' with value '6504'\n", "  Will update column '2024-09' with value '6540'\n", "  Will update column '2024-10' with value '7251'\n", "  Will update column '2024-11' with value '5955'\n", "  Will update column '2024-12' with value '6353'\n", "  Will update column '2025-01' with value '5888'\n", "  Will update column '2025-02' with value '7416'\n", "  Will update column '2025-03' with value '9774'\n", "  Will update column '2025-04' with value '4877'\n", "Match found: theenest.edu.vn (worksheet) matches theenest.edu.vn (Excel)\n", "  Will update column 'Keywords' with value '3902'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '0'\n", "  Will update column '2024-06' with value '0'\n", "  Will update column '2024-07' with value '0'\n", "  Will update column '2024-08' with value '0'\n", "  Will update column '2024-09' with value '115'\n", "  Will update column '2024-10' with value '585'\n", "  Will update column '2024-11' with value '427'\n", "  Will update column '2024-12' with value '429'\n", "  Will update column '2025-01' with value '588'\n", "  Will update column '2025-02' with value '603'\n", "  Will update column '2025-03' with value '712'\n", "  Will update column '2025-04' with value '761'\n", "Match found: thienan.vn/chitiet/san-pham/giai-phap-dao-tao-truc-tuyen-elearning (worksheet) matches thienan.vn/chitiet/san-pham/giai-phap-dao-tao-truc-tuyen-elearning (Excel)\n", "  Will update column 'Keywords' with value '93'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '0'\n", "  Will update column '2024-06' with value '0'\n", "  Will update column '2024-07' with value '0'\n", "  Will update column '2024-08' with value '0'\n", "  Will update column '2024-09' with value '0'\n", "  Will update column '2024-10' with value '2'\n", "  Will update column '2024-11' with value '2'\n", "  Will update column '2024-12' with value '2'\n", "  Will update column '2025-01' with value '2'\n", "  Will update column '2025-02' with value '1'\n", "  Will update column '2025-03' with value '1'\n", "  Will update column '2025-04' with value '1'\n", "Match found: thienan.vn/chitiet/san-pham/giai-phap-quan-tri-truong-hoc-tong-the-unisoft-erp (worksheet) matches thienan.vn/chitiet/san-pham/giai-phap-quan-tri-truong-hoc-tong-the-unisoft-erp (Excel)\n", "  Will update column 'Keywords' with value '28'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '0'\n", "  Will update column '2024-06' with value '0'\n", "  Will update column '2024-07' with value '0'\n", "  Will update column '2024-08' with value '3'\n", "  Will update column '2024-09' with value '0'\n", "  Will update column '2024-10' with value '17'\n", "  Will update column '2024-11' with value '13'\n", "  Will update column '2024-12' with value '0'\n", "  Will update column '2025-01' with value '0'\n", "  Will update column '2025-02' with value '32'\n", "  Will update column '2025-03' with value '4'\n", "  Will update column '2025-04' with value '14'\n", "Match found: thinkingschool.vn (worksheet) matches thinkingschool.vn (Excel)\n", "  Will update column 'Keywords' with value '20835'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '9446'\n", "  Will update column '2024-06' with value '11142'\n", "  Will update column '2024-07' with value '10605'\n", "  Will update column '2024-08' with value '13701'\n", "  Will update column '2024-09' with value '10224'\n", "  Will update column '2024-10' with value '9026'\n", "  Will update column '2024-11' with value '6787'\n", "  Will update column '2024-12' with value '7572'\n", "  Will update column '2025-01' with value '7395'\n", "  Will update column '2025-02' with value '7296'\n", "  Will update column '2025-03' with value '6229'\n", "  Will update column '2025-04' with value '7360'\n", "Match found: thiquocgia.vn (worksheet) matches thiquocgia.vn (Excel)\n", "  Will update column 'Keywords' with value '84259'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '1842'\n", "  Will update column '2024-06' with value '1577'\n", "  Will update column '2024-07' with value '1589'\n", "  Will update column '2024-08' with value '1460'\n", "  Will update column '2024-09' with value '1211'\n", "  Will update column '2024-10' with value '1988'\n", "  Will update column '2024-11' with value '2114'\n", "  Will update column '2024-12' with value '2328'\n", "  Will update column '2025-01' with value '3142'\n", "  Will update column '2025-02' with value '2933'\n", "  Will update column '2025-03' with value '3659'\n", "  Will update column '2025-04' with value '2954'\n", "Match found: thithu.edu.vn (worksheet) matches thithu.edu.vn (Excel)\n", "  Will update column 'Keywords' with value '5190'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '5562'\n", "  Will update column '2024-06' with value '5030'\n", "  Will update column '2024-07' with value '4984'\n", "  Will update column '2024-08' with value '4067'\n", "  Will update column '2024-09' with value '4283'\n", "  Will update column '2024-10' with value '3606'\n", "  Will update column '2024-11' with value '4191'\n", "  Will update column '2024-12' with value '3066'\n", "  Will update column '2025-01' with value '2895'\n", "  Will update column '2025-02' with value '3193'\n", "  Will update column '2025-03' with value '3138'\n", "  Will update column '2025-04' with value '2766'\n", "Match found: thithu.vn (worksheet) matches thithu.vn (Excel)\n", "  Will update column 'Keywords' with value '53'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '10'\n", "  Will update column '2024-06' with value '53'\n", "  Will update column '2024-07' with value '19'\n", "  Will update column '2024-08' with value '1'\n", "  Will update column '2024-09' with value '19'\n", "  Will update column '2024-10' with value '19'\n", "  Will update column '2024-11' with value '19'\n", "  Will update column '2024-12' with value '19'\n", "  Will update column '2025-01' with value '17'\n", "  Will update column '2025-02' with value '2'\n", "  Will update column '2025-03' with value '4'\n", "  Will update column '2025-04' with value '37'\n", "Match found: thivao10.net (worksheet) matches thivao10.net (Excel)\n", "  Will update column 'Keywords' with value '8'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '0'\n", "  Will update column '2024-06' with value '0'\n", "  Will update column '2024-07' with value '0'\n", "  Will update column '2024-08' with value '0'\n", "  Will update column '2024-09' with value '0'\n", "  Will update column '2024-10' with value '0'\n", "  Will update column '2024-11' with value '0'\n", "  Will update column '2024-12' with value '2'\n", "  Will update column '2025-01' with value '0'\n", "  Will update column '2025-02' with value '0'\n", "  Will update column '2025-03' with value '0'\n", "  Will update column '2025-04' with value '0'\n", "Match found: vn.bitdegree.org (worksheet) matches vn.bitdegree.org (Excel)\n", "  Will update column 'Keywords' with value '81141'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '11998'\n", "  Will update column '2024-06' with value '10314'\n", "  Will update column '2024-07' with value '10850'\n", "  Will update column '2024-08' with value '12479'\n", "  Will update column '2024-09' with value '12776'\n", "  Will update column '2024-10' with value '13147'\n", "  Will update column '2024-11' with value '13935'\n", "  Will update column '2024-12' with value '8573'\n", "  Will update column '2025-01' with value '6137'\n", "  Will update column '2025-02' with value '5424'\n", "  Will update column '2025-03' with value '3483'\n", "  Will update column '2025-04' with value '2553'\n", "Match found: shop.visedu.vn (worksheet) matches shop.visedu.vn (Excel)\n", "  Will update column 'Keywords' with value '260'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '0'\n", "  Will update column '2024-06' with value '0'\n", "  Will update column '2024-07' with value '0'\n", "  Will update column '2024-08' with value '0'\n", "  Will update column '2024-09' with value '0'\n", "  Will update column '2024-10' with value '0'\n", "  Will update column '2024-11' with value '0'\n", "  Will update column '2024-12' with value '38'\n", "  Will update column '2025-01' with value '35'\n", "  Will update column '2025-02' with value '22'\n", "  Will update column '2025-03' with value '8'\n", "  Will update column '2025-04' with value '8'\n", "Match found: visedu.vn (worksheet) matches visedu.vn (Excel)\n", "  Will update column 'Keywords' with value '812'\n", "  Will update column '<PERSON><PERSON><PERSON> cập theo tháng từ T5/2024 - T4/2025 - 2024-05' with value '4'\n", "  Will update column '2024-06' with value '4'\n", "  Will update column '2024-07' with value '1'\n", "  Will update column '2024-08' with value '1'\n", "  Will update column '2024-09' with value '4'\n", "  Will update column '2024-10' with value '52'\n", "  Will update column '2024-11' with value '31'\n", "  Will update column '2024-12' with value '96'\n", "  Will update column '2025-01' with value '42'\n", "  Will update column '2025-02' with value '25'\n", "  Will update column '2025-03' with value '12'\n", "  Will update column '2025-04' with value '10'\n", "Updating 312 cells for 24 matched websites...\n", "Successfully updated 312 cells\n", "Summary: Found 24 matching websites, updated 312 cells\n"]}], "source": ["import gspread\n", "import pandas as pd\n", "from datetime import datetime\n", "from google.oauth2.service_account import Credentials\n", "\n", "# Set up authentication\n", "SCOPES = ['https://www.googleapis.com/auth/spreadsheets']\n", "SERVICE_ACCOUNT_FILE = '/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Ranking/2025/Criteria-Scrapers/credentials.json'\n", "credentials = Credentials.from_service_account_file(SERVICE_ACCOUNT_FILE, scopes=SCOPES)\n", "gc = gspread.authorize(credentials)\n", "\n", "# Open spreadsheet and worksheet\n", "spreadsheet_url = \"https://docs.google.com/spreadsheets/d/15Eboneu5_6UfUNymCU_Dz1ZrhPCsoKECXY2MsUYBOP8\"\n", "spreadsheet = gc.open_by_url(spreadsheet_url)\n", "worksheet = spreadsheet.worksheet('WEB')\n", "\n", "# Get header from the first and second row (for merged headers)\n", "first_row = worksheet.row_values(1)\n", "second_row = worksheet.row_values(2)\n", "\n", "# Make sure both rows have the same length by extending the shorter one with empty values\n", "max_length = max(len(first_row), len(second_row))\n", "first_row = first_row + [''] * (max_length - len(first_row))\n", "second_row = second_row + [''] * (max_length - len(second_row))\n", "\n", "# Combine headers where they are merged\n", "worksheet_header = []\n", "for i in range(max_length):\n", "    if first_row[i] and not second_row[i]:\n", "        # Header is merged across rows\n", "        worksheet_header.append(first_row[i])\n", "    elif not first_row[i] and second_row[i]:\n", "        # Only second row has value\n", "        worksheet_header.append(second_row[i])\n", "    elif first_row[i] and second_row[i]:\n", "        # Both rows have values, use a combined notation\n", "        worksheet_header.append(f\"{first_row[i]} - {second_row[i]}\")\n", "    else:\n", "        # Both empty, use empty header\n", "        worksheet_header.append(\"\")\n", "\n", "print(f\"Worksheet headers: {worksheet_header}\")\n", "\n", "# Find the column index for 'Edtech' in the worksheet headers\n", "edtech_col_index = None\n", "for i, header in enumerate(worksheet_header):\n", "    # if 'Edtech URL ' in header: \n", "    if 'Website domain' in header:\n", "        edtech_col_index = i\n", "        break\n", "\n", "if edtech_col_index is None:\n", "    print(\"Error: Could not find 'Edtech' column in worksheet headers\")\n", "    print(f\"Available columns: {worksheet_header}\")\n", "    exit(1)\n", "\n", "print(f\"Found 'Edtech' column at index {edtech_col_index}\")\n", "\n", "# Get current date for file naming\n", "time = datetime.today().strftime(\"%m-%d\")\n", "\n", "# Read local Excel file\n", "local_excel_path = f'/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Ranking/Semrush_{time}.xlsx'\n", "try:\n", "    # Read local Excel file\n", "    excel_df = pd.read_excel(local_excel_path)\n", "    print(f\"Excel columns: {list(excel_df.columns)}\")\n", "    \n", "    # Find the Website column in the Excel file\n", "    website_col = None\n", "    for col in excel_df.columns:\n", "        if 'Website' in col:\n", "            website_col = col\n", "            break\n", "    \n", "    if website_col is None:\n", "        print(\"Error: Could not find 'Website' column in Excel file\")\n", "        print(f\"Available columns: {list(excel_df.columns)}\")\n", "        exit(1)\n", "    \n", "    print(f\"Found 'Website' column: {website_col}\")\n", "    \n", "    # Get all values from the worksheet\n", "    all_values = worksheet.get_all_values()\n", "    \n", "    # Convert worksheet data to DataFrame for easier manipulation\n", "    ws_data = all_values[2:]  # Skip header rows\n", "    if len(ws_data) == 0:\n", "        print(\"No data rows found in worksheet\")\n", "        exit(1)\n", "    \n", "    worksheet_df = pd.DataFrame(ws_data, columns=worksheet_header)\n", "    \n", "    # Initialize a list to keep track of cells to update\n", "    cells_to_update = []\n", "    \n", "    # Keep track of matches for reporting\n", "    matched_count = 0\n", "    updated_cells_count = 0\n", "    \n", "    # For each row in the worksheet\n", "    for ws_row_idx, ws_row in worksheet_df.iterrows():\n", "        actual_row = ws_row_idx + 3  # +3 because we have 2 header rows and 0-based indexing\n", "        edtech_value = str(ws_row.iloc[edtech_col_index]).lower() if not pd.isna(ws_row.iloc[edtech_col_index]) else \"\"\n", "        \n", "        if not edtech_value:\n", "            continue  # Skip rows with empty Edtech values\n", "        \n", "        # Look for matching website in Excel file\n", "        for excel_idx, excel_row in excel_df.iterrows():\n", "            excel_website = str(excel_row[website_col]).lower() if not pd.isna(excel_row[website_col]) else \"\"\n", "            \n", "            if not excel_website:\n", "                continue\n", "            \n", "            # Check for exact match or first 3 letters match\n", "            if excel_website == edtech_value:\n", "            # if (excel_website == edtech_value or \n", "            #     (len(excel_website) >= 5 and len(edtech_value) >= 5 and \n", "            #      excel_website[:5] == edtech_value[:5])):\n", "                \n", "                matched_count += 1\n", "                print(f\"Match found: {edtech_value} (worksheet) matches {excel_website} (Excel)\")\n", "                \n", "                # Update each column from Excel to Worksheet\n", "                for col_idx, excel_col in enumerate(excel_df.columns):\n", "                    # Skip the website column as we don't want to overwrite it\n", "                    if excel_col == website_col:\n", "                        continue\n", "                    \n", "                    excel_value = excel_row[excel_col]\n", "                    \n", "                    # Skip NaN/empty values\n", "                    if pd.isna(excel_value) or excel_value == \"\":\n", "                        continue\n", "                    \n", "                    # Find the matching column in worksheet (case-insensitive)\n", "                    ws_col_idx = None\n", "                    for i, ws_col in enumerate(worksheet_header):\n", "                        if excel_col.lower() in ws_col.lower():\n", "                            ws_col_idx = i\n", "                            break\n", "                    \n", "                    # If we found a matching column\n", "                    if ws_col_idx is not None:\n", "                        # Check if the cell in worksheet is empty and needs updating\n", "                        ws_value = ws_row.iloc[ws_col_idx]\n", "                        if pd.isna(ws_value) or ws_value == \"\":\n", "                            # Column index is 1-based for gspread\n", "                            cells_to_update.append(gspread.Cell(actual_row, ws_col_idx + 1, excel_value))\n", "                            updated_cells_count += 1\n", "                            print(f\"  Will update column '{worksheet_header[ws_col_idx]}' with value '{excel_value}'\")\n", "                \n", "                # We found a match, no need to check other Excel rows\n", "                break\n", "    \n", "    # Update all cells in batch\n", "    if cells_to_update:\n", "        print(f\"Updating {len(cells_to_update)} cells for {matched_count} matched websites...\")\n", "        worksheet.update_cells(cells_to_update)\n", "        print(f\"Successfully updated {len(cells_to_update)} cells\")\n", "    else:\n", "        print(\"No cells need updating. All matched cells already have values.\")\n", "    \n", "    print(f\"Summary: Found {matched_count} matching websites, updated {updated_cells_count} cells\")\n", "    \n", "except FileNotFoundError:\n", "    print(f\"Error: Excel file not found at {local_excel_path}\")\n", "except Exception as e:\n", "    print(f\"Error occurred: {e}\")\n", "    import traceback\n", "    traceback.print_exc()"]}, {"cell_type": "code", "execution_count": 257, "metadata": {}, "outputs": [], "source": ["# import gspread\n", "# from oauth2client.service_account import ServiceAccountCredentials\n", "\n", "# # Define the scope and credentials to access Google Sheets\n", "# scope = ['https://spreadsheets.google.com/feeds',\n", "#          'https://www.googleapis.com/auth/drive']\n", "\n", "# # add credentials to the account\n", "# creds = ServiceAccountCredentials.from_json_keyfile_name('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Ranking/2025/Criteria-Scrapers/credentials.json', scope)\n", "\n", "# # authorize the clientsheet \n", "# client = gspread.authorize(creds)\n", "\n", "# # get the instance of the Spreadsheet\n", "# sheet = client.open('Raw_Data')\n", "\n", "# # get the 2nd sheet of the Spreadsheet\n", "# worksheet = sheet.get_worksheet(2)"]}, {"cell_type": "code", "execution_count": 258, "metadata": {}, "outputs": [], "source": ["# # get all the records of the data\n", "# records_data = worksheet.get_all_records()\n", "# # view the data\n", "# records_data"]}, {"cell_type": "code", "execution_count": 259, "metadata": {}, "outputs": [], "source": ["# # Find the latest value of the first column\n", "# values_first_column = worksheet.col_values(1)\n", "# latest_value_first_column = values_first_column[-1]\n", "\n", "# print(\"Latest value of the first column:\", latest_value_first_column)"]}, {"cell_type": "code", "execution_count": 260, "metadata": {}, "outputs": [], "source": ["# # Import the local Excel file into a DataFrame\n", "# import pandas as pd\n", "# from datetime import datetime \n", "# time = datetime.today().strftime(\"%m-%d\")\n", "# local_data = pd.read_excel(f'/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Ranking/Semrush_{time}.xlsx')\n", "\n", "# # Convert DataFrame to list of lists\n", "# data_to_add = local_data.values.tolist()\n", "\n", "# # Add the data to the worksheet\n", "# worksheet.append_rows(data_to_add, value_input_option='USER_ENTERED')\n", "# # The append_rows method is designed to add rows to the bottom of the worksheet, directly after the last existing row. \n", "\n", "# print(\"Data added successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}