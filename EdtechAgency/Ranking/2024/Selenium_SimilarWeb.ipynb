{"cells": [{"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By \n", "import pandas as pd\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC # https://www.selenium.dev/selenium/docs/api/py/webdriver_support/selenium.webdriver.support.expected_conditions.html\n", "from time import sleep\n", "from bs4 import BeautifulSoup\n", "from datetime import datetime, timedelta\n", "import random\n", "from selenium.webdriver.common.keys import Keys\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "\n", "chrome_options = Options()\n", "# chrome_options.add_argument(\"--incognito\")\n", "# chrome_options.add_argument('--headless') \n", "driver = webdriver.Chrome(options=chrome_options)\n", "actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "\n", "url = \"https://pro.similarweb.com/#/digitalsuite/home\"\n", "driver.get(url)\n", "wait = WebDriverWait(driver, 10)"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [], "source": ["# # AUTHENTICATE LOG IN\n", "# driver.find_element(By.ID, \"input-email\").send_keys('<EMAIL>')\n", "# driver.find_element(By.ID, \"input-password\").send_keys('HnAm2002!')\n", "# driver.find_element(By.XPATH, \"//button[@class='sc-bcXHqe iMwoDS accessible']/p[@class='sc-dkrFOg bMvWRq']\").click()"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [], "source": ["driver.find_element(By.XPATH, \"//div[@class='sc-hiDMwi hcLvNY']/div//button[1][@class='sc-laZRCg kdfVOn accessible']\").click()"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [], "source": ["sleep(2)"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [], "source": ["driver.find_element(By.ID, \"identifierId\").send_keys('<EMAIL>')"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [], "source": ["driver.find_element(By.XPATH, \"//button[@class='VfPpkd-LgbsSe VfPpkd-LgbsSe-OWXEXe-k8QpJ VfPpkd-LgbsSe-OWXEXe-dgl2Hf nCP5yc AjY5Oe DuMIQc LQeN7 BqKGqe Jskylb TrZEUc lw1w4b']\").click()"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [], "source": ["sleep(5)"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [], "source": ["wait.until(EC.element_to_be_clickable((By.XPATH, \"//div[@id='password']/div/div/div/input[@class='whsOnd zHQkBf']\"))).send_keys('HnAm2002#@!')"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [], "source": ["sleep(2)"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [], "source": ["driver.find_element(By.XPATH, \"//button[@class='VfPpkd-LgbsSe VfPpkd-LgbsSe-OWXEXe-k8QpJ VfPpkd-LgbsSe-OWXEXe-dgl2Hf nCP5yc AjY5Oe DuMIQc LQeN7 BqKGqe Jskylb TrZEUc lw1w4b']\").click()"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [], "source": ["sleep(5)"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [], "source": ["website = 'tesso.io'\n", "input_website = wait.until(EC.element_to_be_clickable((By.XPATH, \"//div[@class='sc-kDgGX ehrztW AutoCompleteContainer']/div/div/input[@class='sc-jkCMRl kmQaWd']\")))\n", "input_website.send_keys(website)"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [], "source": ["# driver.find_element(By.XPATH,\"//div[@class='ScrollArea']/div/div[1][@class='sc-gLdKKF GGLqc ListItem ListItemWebsite']\").click()    #send_keys(Keys.RETURN)\n", "actions.send_keys(Keys.ENTER)\n", "actions.perform()"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [], "source": ["sleep(2)"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [], "source": ["# attribute = 'innerHTML'  \n", "# expected_text = \"Try broadening your parameters or searching for something else.\"\n", "\n", "# try:\n", "#     status = WebDriverWait(driver, 10).until(EC.text_to_be_present_in_element_attribute((By.XPATH, \"//div[@class='DefaultNoDataLayoutContainer-deqEGN eiPwGV']/div/div[2][@class='NoDataSubTitle-fgITWd kWhuVz']\"), attribute, expected_text))\n", "#     if status:\n", "#         print(f'Website {website} does not have the country distribution data')\n", "#     else:\n", "#         pass\n", "# except Exception:\n", "#     print(\"Element not found\")"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Element found\n"]}], "source": ["try:\n", "    # status = WebDriverWait(driver, 10).until(EC.text_to_be_present_in_element_attribute((By.XPATH, \"//div[@class='DefaultNoDataLayoutContainer-deqEGN eiPwGV']/div/div[2][@class='NoDataSubTitle-fgITWd kWhuVz']\")))\n", "    status = driver.find_element(By.XPATH, \"//div[@class='swReactTableCell swTable-cell resizeableCell-hover']//div[@class='swTable-countryCell']\")\n", "    if status is not None:\n", "        print(\"Element found\")\n", "except Exception:\n", "    print(\"Element not found\")"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [], "source": ["driver.quit()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}