{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## GOOGLE PLAY"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By \n", "import pandas as pd\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC # https://www.selenium.dev/selenium/docs/api/py/webdriver_support/selenium.webdriver.support.expected_conditions.html\n", "from time import sleep\n", "from bs4 import BeautifulSoup\n", "from datetime import datetime, timedelta\n", "import random\n", "from selenium.webdriver.common.keys import Keys\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "\n", "chrome_options = Options()\n", "# chrome_options.add_argument(\"--incognito\")\n", "# chrome_options.add_argument('--headless') \n", "driver = webdriver.Chrome(options=chrome_options)\n", "actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "\n", "url = \"https://play.google.com/store/games?hl=vi&gl=US\"\n", "driver.get(url)\n", "wait = WebDriverWait(driver, 10)\n", "sleep(2)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["search_button = wait.until(EC.presence_of_element_located((By.XPATH, \"//button[@class='VfPpkd-Bz112c-LgbsSe yHy1rc eT1oJ mN1ivc']//i[@class='google-material-icons r9optf']\")))\n", "search_button.click()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["keyword_input = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.XPATH, \"//input[@class='HWAcU']\")))\n", "keyword_input.send_keys('công nghệ giáo dục')\n", "keyword_input.send_keys(Keys.RETURN)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# def scroll(driver):\n", "#     # Scroll down\n", "#     # driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.END)\n", "#     driver.execute_script(\"window.scrollTo(0, document.body.scrollHeight);\")\n", "#     sleep(2) "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_app_info(index, wait):\n", "    data = {}\n", "    try:\n", "        # Check if the parent element exists\n", "        parent_element = wait.until(EC.presence_of_element_located((By.XPATH, f\"//div[{index}][@class='ULeU3b']\")))\n", "        \n", "        # NAME\n", "        data['App'] = wait.until(EC.presence_of_element_located((By.XPATH, f\"//div[{index}][@class='ULeU3b']//span[@class='wMUdtb']\"))).text\n", "        \n", "        # BRIEF DESCRIPTION\n", "        data['Title'] = wait.until(EC.presence_of_element_located((By.XPATH, f\"//div[{index}][@class='ULeU3b']//span[@class='DdYX5']\"))).text\n", "        \n", "        # STAR RATE\n", "        try:\n", "            data['Rate'] = wait.until(EC.presence_of_element_located((By.XPATH, f\"//div[{index}][@class='ULeU3b']//span[@class='w2kbF']\"))).text\n", "        except Exception:\n", "            data['Rate'] = None\n", "        \n", "        # BRAND IMAGE\n", "        data['Brand Image'] = wait.until(EC.presence_of_element_located((By.XPATH, f\"//div[{index}][@class='ULeU3b']//img[@class='T75of stzEZd']\"))).get_attribute('src')\n", "\n", "        # # OPEN APP PAGE\n", "        # wait.until(EC.element_to_be_clickable((By.XPATH, f\"//div[{index}][@class='ULeU3b']//span[@class='wMUdtb']\"))).click()\n", "        # sleep(2)\n", "        app_page_element = wait.until(EC.element_to_be_clickable((By.XPATH, f\"//div[{index}][@class='ULeU3b']//span[@class='wMUdtb']\")))\n", "        driver.execute_script(\"arguments[0].scrollIntoView(true);\", app_page_element)\n", "        sleep(1)\n", "        driver.execute_script(\"arguments[0].click();\", app_page_element)\n", "        sleep(1)\n", "\n", "        # Get the current URL\n", "        data['URL'] = driver.current_url\n", "\n", "        # DESCRIPTION\n", "        description_elements = wait.until(EC.presence_of_all_elements_located((By.XPATH, \"//div[@class='bARER' and @data-g-id='description']\")))\n", "        data['Description'] = \" \".join([elem.text for elem in description_elements])\n", "\n", "        # LATEST UPDATE TIME\n", "        data['Latest update'] = wait.until(EC.presence_of_element_located((By.XPATH, \"//div[@class='xg1aie']\"))).text\n", "\n", "        # INDUSTRY\n", "        industry_elements = wait.until(EC.presence_of_all_elements_located((By.XPATH, \"//div[@class='Uc6QCc']//span\")))\n", "        data['Industry'] = \" \".join([elem.text for elem in industry_elements])\n", "\n", "        # <PERSON>AVIG<PERSON><PERSON> BACK TO MAIN PAGE\n", "        driver.back()\n", "        sleep(2)\n", "\n", "    except Exception as e:\n", "        print(f\"An error occurred at index {index}: {e}\")\n", "        return None\n", "\n", "    return data\n", "\n", "\n", "data_list = []\n", "\n", "# Loop through each index to extract data\n", "for i in range(1, 51):\n", "    data = extract_app_info(i, wait)\n", "    if data is None:\n", "        break\n", "    data_list.append(data)\n", "\n", "# Close the WebDriver\n", "driver.quit()\n", "\n", "# Convert the data to a DataFrame\n", "df = pd.DataFrame(data_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["previous_data = pd.read_excel('/Users/<USER>/Downloads/Crawl_App.xlsx')\n", "merge = pd.concat([previous_data,df])\n", "merge.to_excel('/Users/<USER>/Downloads/Crawl_App.xlsx',index=None)\n", "# merge = previous_data.append(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from datetime import datetime\n", "# df.to_excel(f'/Users/<USER>/Downloads/crawl_app_{datetime.now()}.xlsx')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["driver.quit()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["App data has been extracted and saved to 'app_data.csv'.\n"]}], "source": ["import time\n", "import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "chrome_options = Options()\n", "chrome_options.add_argument(\"--incognito\")\n", "chrome_options.add_argument('--headless') \n", "driver = webdriver.Chrome(options=chrome_options)\n", "\n", "# List of app links\n", "df = pd.read_excel('/Users/<USER>/Downloads/Link.xlsx')\n", "app_links = df['Link'].tolist()  \n", "\n", "def extract_app_info(url, wait):\n", "    data = {}\n", "    try:\n", "        # Open the app link\n", "        driver.get(url)\n", "\n", "        # STAR RATE\n", "        try:\n", "            data['Rate'] = wait.until(EC.presence_of_element_located((By.XPATH, \"//*[@id='yDmH0d']/c-wiz[2]/div/div/div[1]/div/div[1]/div/div/c-wiz/div[2]/div[2]/div/div/div[1]/div[1]/div/div\"))).text\n", "        except Exception:\n", "            data['Rate'] = None\n", "        \n", "        # DOWNLOAD\n", "        try:\n", "            data['Download'] = wait.until(EC.presence_of_element_located((By.XPATH, \"//*[@id='yDmH0d']/c-wiz[2]/div/div/div[1]/div/div[1]/div/div/c-wiz/div[2]/div[2]/div/div/div[2]/div[1]\"))).text\n", "        except Exception:\n", "            data['Download'] = None\n", "\n", "        # RATE NUMBER\n", "        try:\n", "            data['Rate_Number'] = wait.until(EC.presence_of_element_located((By.XPATH, \"//*[@id='yDmH0d']/c-wiz[2]/div/div/div[1]/div/div[1]/div/div/c-wiz/div[2]/div[2]/div/div/div[1]/div[2]\"))).text\n", "        except Exception:\n", "            data['Rate_Number'] = None\n", "\n", "    except Exception as e:\n", "        print(f\"An error occurred while processing URL {url}: {e}\")\n", "        return None\n", "\n", "    return data\n", "\n", "# Initialize WebDriverWait\n", "wait = WebDriverWait(driver, 10)\n", "\n", "# List to store app data\n", "data_list = []\n", "\n", "# Loop through each app link to extract data\n", "for url in app_links:\n", "    data = extract_app_info(url, wait)\n", "    if data is not None:\n", "        data_list.append(data)\n", "\n", "# Close the WebDriver\n", "driver.quit()\n", "\n", "# Convert the data to a DataFrame\n", "df = pd.DataFrame(data_list)\n", "\n", "# Save the DataFrame to a CSV file\n", "df.to_csv('app_data.csv', index=False)\n", "\n", "print(\"App data has been extracted and saved to 'app_data.csv'.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## APP STORE"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["An error occurred at index 2: Message: \n", "Stacktrace:\n", "0   chromedriver                        0x0000000102ff82d4 cxxbridge1$str$ptr + 2739836\n", "1   chromedriver                        0x0000000102ff0934 cxxbridge1$str$ptr + 2708700\n", "2   chromedriver                        0x0000000102b51f90 cxxbridge1$string$len + 93360\n", "3   chromedriver                        0x0000000102b98de4 cxxbridge1$string$len + 383748\n", "4   chromedriver                        0x0000000102bd9e80 cxxbridge1$string$len + 650144\n", "5   chromedriver                        0x0000000102b8d060 cxxbridge1$string$len + 335232\n", "6   chromedriver                        0x0000000102fc0c38 cxxbridge1$str$ptr + 2512864\n", "7   chromedriver                        0x0000000102fc3f58 cxxbridge1$str$ptr + 2525952\n", "8   chromedriver                        0x0000000102fa6578 cxxbridge1$str$ptr + 2404640\n", "9   chromedriver                        0x0000000102fc4818 cxxbridge1$str$ptr + 2528192\n", "10  chromedriver                        0x0000000102f96f2c cxxbridge1$str$ptr + 2341588\n", "11  chromedriver                        0x0000000102fe0a60 cxxbridge1$str$ptr + 2643464\n", "12  chromedriver                        0x0000000102fe0be8 cxxbridge1$str$ptr + 2643856\n", "13  chromedriver                        0x0000000102ff05a8 cxxbridge1$str$ptr + 2707792\n", "14  libsystem_pthread.dylib             0x00000001913be06c _pthread_start + 320\n", "15  libsystem_pthread.dylib             0x00000001913b8da0 thread_start + 8\n", "\n"]}], "source": ["from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By \n", "import pandas as pd\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC # https://www.selenium.dev/selenium/docs/api/py/webdriver_support/selenium.webdriver.support.expected_conditions.html\n", "from time import sleep\n", "from bs4 import BeautifulSoup\n", "from datetime import datetime, timedelta\n", "import random\n", "from selenium.webdriver.common.keys import Keys\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "\n", "chrome_options = Options()\n", "# chrome_options.add_argument(\"--incognito\")\n", "# chrome_options.add_argument('--headless') \n", "driver = webdriver.Chrome(options=chrome_options)\n", "actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "\n", "url = \"https://apps.apple.com/vn/charts/ipad/gi%C3%A1o-d%E1%BB%A5c-apps/6017?l=vi&chart=top-free\"\n", "driver.get(url)\n", "wait = WebDriverWait(driver, 10)\n", "sleep(2)\n", "\n", "def extract_app_info(index, wait):\n", "    data = {}\n", "    try:\n", "        # Check if the parent element exists\n", "        wait.until(EC.presence_of_element_located((By.XPATH, f\"//*[@id='charts-content-section']/ol/li[{index}]\")))\n", "        \n", "        # NAME\n", "        data['App'] = wait.until(EC.presence_of_element_located((By.XPATH, f\"//*[@id='charts-content-section']/ol/li[{index}]/a/div[2]/div/div[1]/div/div\"))).text\n", "\n", "        # Get the current URL\n", "        data['URL'] = wait.until(EC.presence_of_element_located((By.XPATH, f\"//*[@id='charts-content-section']/ol/li[{index}]/a\"))).get_attribute('href')\n", "\n", "        # <PERSON>AVIG<PERSON><PERSON> BACK TO MAIN PAGE\n", "        driver.back()\n", "        sleep(2)\n", "\n", "    except Exception as e:\n", "        print(f\"An error occurred at index {index}: {e}\")\n", "        return None\n", "\n", "    return data\n", "\n", "\n", "data_list = []\n", "\n", "# Loop through each index to extract data\n", "for i in range(1, 100):\n", "    data = extract_app_info(i, wait)\n", "    if data is None:\n", "        break\n", "    data_list.append(data)\n", "\n", "# Close the WebDriver\n", "driver.quit()\n", "\n", "# Convert the data to a DataFrame\n", "df = pd.DataFrame(data_list)\n", "\n", "# Save the DataFrame to a CSV file\n", "df.to_excel('app_data.xlsx', index=False)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "chrome_options = Options()\n", "chrome_options.add_argument(\"--incognito\")\n", "chrome_options.add_argument('--headless') \n", "driver = webdriver.Chrome(options=chrome_options)\n", "\n", "# List of app links\n", "df = pd.read_excel('/Users/<USER>/Downloads/Link.xlsx')\n", "app_links = df['Link'].tolist()  \n", "\n", "def extract_app_info(url, wait):\n", "    data = {}\n", "    try:\n", "        # Open the app link\n", "        driver.get(url)\n", "\n", "        # DATA\n", "        try:\n", "            data['Data'] = wait.until(EC.presence_of_element_located((By.XPATH, \"/html/body/div[3]/main/div[2]/section[1]/div/div[2]/header/ul[1]/li/ul/li/figure/figcaption\"))).text\n", "        except Exception:\n", "            data['Data'] = None\n", "\n", "    except Exception as e:\n", "        print(f\"An error occurred while processing URL {url}: {e}\")\n", "        return None\n", "\n", "    return data\n", "\n", "# Initialize WebDriverWait\n", "wait = WebDriverWait(driver, 10)\n", "\n", "# List to store app data\n", "data_list = []\n", "\n", "# Loop through each app link to extract data\n", "for url in app_links:\n", "    data = extract_app_info(url, wait)\n", "    if data is not None:\n", "        data_list.append(data)\n", "\n", "# Close the WebDriver\n", "driver.quit()\n", "\n", "# Convert the data to a DataFrame\n", "df = pd.DataFrame(data_list)\n", "\n", "# Save the DataFrame to a CSV file\n", "df.to_csv('ios_app_data.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}