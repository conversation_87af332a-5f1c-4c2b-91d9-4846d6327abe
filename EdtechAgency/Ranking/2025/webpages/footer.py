import streamlit as st

def footer():
    pass
    # footer = """
    #     <style>
    #     a:link , a:visited{
    #     color: #BFBFBF;  /* theme's text color hex code at 75 percent brightness*/
    #     background-color: transparent;
    #     text-decoration: none;
    #     }

    #     a:hover,  a:active {
    #     color: #0283C3; /* theme's primary color*/
    #     background-color: transparent;
    #     text-decoration: underline;
    #     }

    #     footer{
    #         visibility:hidden;
    #     }

    #     .footer {
    #     position: relative;
    #     left: 0;
    #     top:10px;
    #     bottom: 0;
    #     width: 100%;
    #     background-color: transparent;
    #     color: #808080; /* theme's text color hex code at 50 percent brightness*/
    #     text-align: center; 
    #     }
    #     </style>

    #     <div id="page-container">
    #         <div class="footer">
    #             <p style='font-size: 1em;'>Made with <a style='display: inline; text-align: left;' href="https://streamlit.io/" target="_blank">Streamlit</a><br 'style= top:3px;'>
    #             with <img src="https://em-content.zobj.net/source/skype/289/red-heart_2764-fe0f.png" alt="heart" height= "10"/><a style='display: inline; text-align: left;' href="https://www.linkedin.com/in/hector2909/" target="_blank"> by Hector Vu</a></p>
    #         </div>
    #     </div>
    #     """
    # st.write(footer, unsafe_allow_html=True)
            