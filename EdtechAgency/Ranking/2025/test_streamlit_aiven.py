import streamlit as st
import pymysql
import pandas as pd

# -----------------------------
# Streamlit App Title
# st.title("Aiven MySQL Test Connection")
st.write("This app connects to a MySQL database hosted on <PERSON><PERSON> and displays data from a test table.")

# -----------------------------
# Define connection settings
db_config = {
    'host': 'mysql-293df364-hector-project1.j.aivencloud.com',
    'port': 20439,  
    'user': 'avnadmin',
    'password': 'AVNS_fVJJb1mBTQ2g5yKBq4c',
    'database': 'Ranking',
    'ssl': {
        'ca': '/Users/<USER>/Downloads/ca.pem'  # Use Aiven's CA certificate if required
    }
}

# -----------------------------
# Optional: Upload CA Certificate file
uploaded_cert = st.file_uploader("Upload Aiven CA Certificate (ca.pem)", type=['pem'])

if uploaded_cert:
    with open("ca.pem", "wb") as f:
        f.write(uploaded_cert.read())
    db_config['ssl']['ca'] = "ca.pem"

    try:
        # -----------------------------
        # Connect to the database
        conn = pymysql.connect(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['user'],
            password=db_config['password'],
            database=db_config['database'],
            ssl=db_config['ssl']
        )
        
        # -----------------------------
        # query = "SHOW TABLES;"
        # df = pd.read_sql(query, conn)
        # st.success("Connection successful!")
        # st.write("Available tables:")
        # st.dataframe(df)

        query = """
        SELECT d.`web name` AS Name, f.`backlink`, f.`referring domain`, f.`domain keyword`, f.`bounce rate`, f.`website authority`, f.`website speed`
        FROM fact_ranking_web AS f 
        INNER JOIN dim_ranking_web AS d 
        ON f.`web url` = d.`web url`
        """
        df = pd.read_sql(query, conn)
        st.dataframe(df)

        selected_name = st.selectbox("Choose an EdTech Web", df["Name"])
        selected_app = df[df["Name"] == selected_name].iloc[0]

        metrics = df.columns[1:]
        max_values = df[metrics].max()

        # Normalize selected app
        normalized_values = selected_app[metrics] / max_values

        import plotly.graph_objects as go
        # Create radar chart
        fig = go.Figure()

        fig.add_trace(go.Scatterpolar(
            r=normalized_values.tolist(),
            theta=metrics.tolist(),
            fill='toself',
            name=selected_name,
            line=dict(color='blue')
        ))

        fig.add_trace(go.Scatterpolar(
            r=[1] * len(metrics),
            theta=metrics.tolist(),
            fill='toself',
            name='Max (Benchmark)',
            line=dict(color='gray', dash='dash')
        ))

        fig.update_layout(
            polar=dict(radialaxis=dict(visible=True, range=[0, 1])),
            showlegend=True,
            title="Comparison with Market Benchmark"
        )

        # Display chart
        st.plotly_chart(fig)


        conn.close()

    except Exception as e:
        st.error(f"Connection failed: {e}")
else:
    st.warning("Please upload your `ca.pem` file to enable secure connection.")






# import streamlit as st
# import pandas as pd
# import plotly.graph_objects as go

# # Sample Data
# data = {
#     'Name': ['SelectedApp', 'CompetitorA', 'CompetitorB'],
#     'Monthly Access': [80000, 100000, 220000],
#     'Organic Keywords': [350, 500, 400],
#     'Authority Score': [60, 90, 75],
#     'Accessibility': [85, 95, 90],
#     'App Downloads': [500000, 1000000, 750000],
#     'Sentiment Score': [0.75, 0.85, 0.80],
#     'Star Rating': [4.2, 4.8, 4.5],
# }

# df = pd.DataFrame(data)

# # User selects app
# selected_name = st.selectbox("Choose an EdTech Web/App", df["Name"])
# selected_app = df[df["Name"] == selected_name].iloc[0]

# # Metrics to compare
# metrics = df.columns[1:]
# max_values = df[metrics].max()

# # Normalize selected app
# normalized_values = selected_app[metrics] / max_values

# # Create radar chart
# fig = go.Figure()

# fig.add_trace(go.Scatterpolar(
#     r=normalized_values.tolist(),
#     theta=metrics.tolist(),
#     fill='toself',
#     name=selected_name,
#     line=dict(color='blue')
# ))

# fig.add_trace(go.Scatterpolar(
#     r=[1] * len(metrics),
#     theta=metrics.tolist(),
#     fill='toself',
#     name='Max (Benchmark)',
#     line=dict(color='gray', dash='dash')
# ))

# fig.update_layout(
#     polar=dict(radialaxis=dict(visible=True, range=[0, 1])),
#     showlegend=True,
#     title="Comparison with Market Benchmark"
# )

# # Display chart
# st.plotly_chart(fig)
