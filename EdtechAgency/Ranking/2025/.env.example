# Database Configuration Template
# Copy this file to .env and fill in your actual credentials

# MySQL Database Configuration
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_HOST=your_database_host
DB_DATABASE=your_database_name
DB_PORT=your_database_port

# SSL Certificate Path (adjust path as needed)
SSL_CA_PATH=/path/to/your/ca.pem

# SSH Configuration (if needed)
SSH_HOST = 
SSH_PORT = 
SSH_USERNAME = 
SSH_KEY_PATH = 
