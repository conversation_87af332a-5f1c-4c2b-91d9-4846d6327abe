{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By \n", "import pandas as pd\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC # https://www.selenium.dev/selenium/docs/api/py/webdriver_support/selenium.webdriver.support.expected_conditions.html\n", "from time import sleep\n", "from bs4 import BeautifulSoup\n", "from datetime import datetime, timedelta\n", "import random\n", "from selenium.webdriver.common.keys import Keys\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "\n", "chrome_options = Options()\n", "# chrome_options.add_argument(\"--incognito\")\n", "# chrome_options.add_argument('--headless') \n", "driver = webdriver.Chrome(options=chrome_options)\n", "actions = <PERSON><PERSON><PERSON><PERSON>(driver)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Function to scroll down and click the \"load more\" button if it exists\n", "def scroll_and_load_more(driver):\n", "    try:\n", "        # Scroll down\n", "        driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.END)\n", "        # driver.execute_script(\"window.scrollTo(0, document.body.scrollHeight);\")\n", "        sleep(2)  # Adjust the sleep time if necessary\n", "\n", "        # Try to click the \"load more\" button if it exists\n", "        try:\n", "            load_more_button = driver.find_element(By.XPATH, \"//div[@class='GNJvt ipz2Oe']\")\n", "            if load_more_button.is_displayed():\n", "                load_more_button.click()\n", "                sleep(2)  # Allow time for new content to load\n", "                driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.END)\n", "                sleep(2)  # Scroll down again after clicking load more\n", "        except Exception:\n", "            # If no \"load more\" button is found, continue\n", "            pass\n", "\n", "        return True\n", "    except Exception:\n", "        return False"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["An error occurred while extracting data from a parent element: Message: no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\".//cite[@class='qLRx3b tjvcx GvPZzd cHaqb']\"}\n", "  (Session info: chrome=125.0.6422.142); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n", "Stacktrace:\n", "0   chromedriver                        0x00000001033864c8 chromedriver + 4302024\n", "1   chromedriver                        0x000000010337ee10 chromedriver + 4271632\n", "2   chromedriver                        0x0000000102fb019c chromedriver + 278940\n", "3   chromedriver                        0x0000000102ff22c4 chromedriver + 549572\n", "4   chromedriver                        0x0000000102fe885c chromedriver + 510044\n", "5   chromedriver                        0x000000010302ac5c chromedriver + 781404\n", "6   chromedriver                        0x0000000102fe7004 chromedriver + 503812\n", "7   chromedriver                        0x0000000102fe79ec chromedriver + 506348\n", "8   chromedriver                        0x000000010334e510 chromedriver + 4072720\n", "9   chromedriver                        0x0000000103352fbc chromedriver + 4091836\n", "10  chromedriver                        0x0000000103335754 chromedriver + 3970900\n", "11  chromedriver                        0x00000001033538a4 chromedriver + 4094116\n", "12  chromedriver                        0x00000001033286d4 chromedriver + 3917524\n", "13  chromedriver                        0x0000000103370b08 chromedriver + 4213512\n", "14  chromedriver                        0x0000000103370c84 chromedriver + 4213892\n", "15  chromedriver                        0x000000010337ea08 chromedriver + 4270600\n", "16  libsystem_pthread.dylib             0x000000019882206c _pthread_start + 320\n", "17  libsystem_pthread.dylib             0x000000019881cda0 thread_start + 8\n", "\n", "An error occurred while extracting data from a parent element: Message: element click intercepted: Element <cite class=\"qLRx3b tjvcx GvPZzd cHaqb\" role=\"text\">...</cite> is not clickable at point (226, 9). Other element would receive the click: <div style=\"margin-top:-20px\" class=\"sfbg\"></div>\n", "  (Session info: chrome=125.0.6422.142)\n", "Stacktrace:\n", "0   chromedriver                        0x00000001033864c8 chromedriver + 4302024\n", "1   chromedriver                        0x000000010337ee10 chromedriver + 4271632\n", "2   chromedriver                        0x0000000102fb019c chromedriver + 278940\n", "3   chromedriver                        0x0000000102ff7d48 chromedriver + 572744\n", "4   chromedriver                        0x0000000102ff62ec chromedriver + 565996\n", "5   chromedriver                        0x0000000102ff447c chromedriver + 558204\n", "6   chromedriver                        0x0000000102ff39cc chromedriver + 555468\n", "7   chromedriver                        0x0000000102fe8e24 chromedriver + 511524\n", "8   chromedriver                        0x0000000102fe885c chromedriver + 510044\n", "9   chromedriver                        0x000000010302ac5c chromedriver + 781404\n", "10  chromedriver                        0x0000000102fe7004 chromedriver + 503812\n", "11  chromedriver                        0x0000000102fe79ec chromedriver + 506348\n", "12  chromedriver                        0x000000010334e510 chromedriver + 4072720\n", "13  chromedriver                        0x0000000103352fbc chromedriver + 4091836\n", "14  chromedriver                        0x0000000103335754 chromedriver + 3970900\n", "15  chromedriver                        0x00000001033538a4 chromedriver + 4094116\n", "16  chromedriver                        0x00000001033286d4 chromedriver + 3917524\n", "17  chromedriver                        0x0000000103370b08 chromedriver + 4213512\n", "18  chromedriver                        0x0000000103370c84 chromedriver + 4213892\n", "19  chromedriver                        0x000000010337ea08 chromedriver + 4270600\n", "20  libsystem_pthread.dylib             0x000000019882206c _pthread_start + 320\n", "21  libsystem_pthread.dylib             0x000000019881cda0 thread_start + 8\n", "\n", "An error occurred while extracting data from a parent element: Message: no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\".//cite[@class='qLRx3b tjvcx GvPZzd cHaqb']\"}\n", "  (Session info: chrome=125.0.6422.142); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n", "Stacktrace:\n", "0   chromedriver                        0x00000001033864c8 chromedriver + 4302024\n", "1   chromedriver                        0x000000010337ee10 chromedriver + 4271632\n", "2   chromedriver                        0x0000000102fb019c chromedriver + 278940\n", "3   chromedriver                        0x0000000102ff22c4 chromedriver + 549572\n", "4   chromedriver                        0x0000000102fe885c chromedriver + 510044\n", "5   chromedriver                        0x000000010302ac5c chromedriver + 781404\n", "6   chromedriver                        0x0000000102fe7004 chromedriver + 503812\n", "7   chromedriver                        0x0000000102fe79ec chromedriver + 506348\n", "8   chromedriver                        0x000000010334e510 chromedriver + 4072720\n", "9   chromedriver                        0x0000000103352fbc chromedriver + 4091836\n", "10  chromedriver                        0x0000000103335754 chromedriver + 3970900\n", "11  chromedriver                        0x00000001033538a4 chromedriver + 4094116\n", "12  chromedriver                        0x00000001033286d4 chromedriver + 3917524\n", "13  chromedriver                        0x0000000103370b08 chromedriver + 4213512\n", "14  chromedriver                        0x0000000103370c84 chromedriver + 4213892\n", "15  chromedriver                        0x000000010337ea08 chromedriver + 4270600\n", "16  libsystem_pthread.dylib             0x000000019882206c _pthread_start + 320\n", "17  libsystem_pthread.dylib             0x000000019881cda0 thread_start + 8\n", "\n", "An error occurred while extracting data from a parent element: Message: element click intercepted: Element <cite class=\"qLRx3b tjvcx GvPZzd cHaqb\" role=\"text\">...</cite> is not clickable at point (226, 9). Other element would receive the click: <div style=\"margin-top:-20px\" class=\"sfbg\"></div>\n", "  (Session info: chrome=125.0.6422.142)\n", "Stacktrace:\n", "0   chromedriver                        0x00000001033864c8 chromedriver + 4302024\n", "1   chromedriver                        0x000000010337ee10 chromedriver + 4271632\n", "2   chromedriver                        0x0000000102fb019c chromedriver + 278940\n", "3   chromedriver                        0x0000000102ff7d48 chromedriver + 572744\n", "4   chromedriver                        0x0000000102ff62ec chromedriver + 565996\n", "5   chromedriver                        0x0000000102ff447c chromedriver + 558204\n", "6   chromedriver                        0x0000000102ff39cc chromedriver + 555468\n", "7   chromedriver                        0x0000000102fe8e24 chromedriver + 511524\n", "8   chromedriver                        0x0000000102fe885c chromedriver + 510044\n", "9   chromedriver                        0x000000010302ac5c chromedriver + 781404\n", "10  chromedriver                        0x0000000102fe7004 chromedriver + 503812\n", "11  chromedriver                        0x0000000102fe79ec chromedriver + 506348\n", "12  chromedriver                        0x000000010334e510 chromedriver + 4072720\n", "13  chromedriver                        0x0000000103352fbc chromedriver + 4091836\n", "14  chromedriver                        0x0000000103335754 chromedriver + 3970900\n", "15  chromedriver                        0x00000001033538a4 chromedriver + 4094116\n", "16  chromedriver                        0x00000001033286d4 chromedriver + 3917524\n", "17  chromedriver                        0x0000000103370b08 chromedriver + 4213512\n", "18  chromedriver                        0x0000000103370c84 chromedriver + 4213892\n", "19  chromedriver                        0x000000010337ea08 chromedriver + 4270600\n", "20  libsystem_pthread.dylib             0x000000019882206c _pthread_start + 320\n", "21  libsystem_pthread.dylib             0x000000019881cda0 thread_start + 8\n", "\n", "An error occurred while extracting data from a parent element: Message: no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\".//cite[@class='qLRx3b tjvcx GvPZzd cHaqb']\"}\n", "  (Session info: chrome=125.0.6422.142); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n", "Stacktrace:\n", "0   chromedriver                        0x00000001033864c8 chromedriver + 4302024\n", "1   chromedriver                        0x000000010337ee10 chromedriver + 4271632\n", "2   chromedriver                        0x0000000102fb019c chromedriver + 278940\n", "3   chromedriver                        0x0000000102ff22c4 chromedriver + 549572\n", "4   chromedriver                        0x0000000102fe885c chromedriver + 510044\n", "5   chromedriver                        0x000000010302ac5c chromedriver + 781404\n", "6   chromedriver                        0x0000000102fe7004 chromedriver + 503812\n", "7   chromedriver                        0x0000000102fe79ec chromedriver + 506348\n", "8   chromedriver                        0x000000010334e510 chromedriver + 4072720\n", "9   chromedriver                        0x0000000103352fbc chromedriver + 4091836\n", "10  chromedriver                        0x0000000103335754 chromedriver + 3970900\n", "11  chromedriver                        0x00000001033538a4 chromedriver + 4094116\n", "12  chromedriver                        0x00000001033286d4 chromedriver + 3917524\n", "13  chromedriver                        0x0000000103370b08 chromedriver + 4213512\n", "14  chromedriver                        0x0000000103370c84 chromedriver + 4213892\n", "15  chromedriver                        0x000000010337ea08 chromedriver + 4270600\n", "16  libsystem_pthread.dylib             0x000000019882206c _pthread_start + 320\n", "17  libsystem_pthread.dylib             0x000000019881cda0 thread_start + 8\n", "\n", "An error occurred while extracting data from a parent element: Message: no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\".//cite[@class='qLRx3b tjvcx GvPZzd cHaqb']\"}\n", "  (Session info: chrome=125.0.6422.142); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n", "Stacktrace:\n", "0   chromedriver                        0x00000001033864c8 chromedriver + 4302024\n", "1   chromedriver                        0x000000010337ee10 chromedriver + 4271632\n", "2   chromedriver                        0x0000000102fb019c chromedriver + 278940\n", "3   chromedriver                        0x0000000102ff22c4 chromedriver + 549572\n", "4   chromedriver                        0x0000000102fe885c chromedriver + 510044\n", "5   chromedriver                        0x000000010302ac5c chromedriver + 781404\n", "6   chromedriver                        0x0000000102fe7004 chromedriver + 503812\n", "7   chromedriver                        0x0000000102fe79ec chromedriver + 506348\n", "8   chromedriver                        0x000000010334e510 chromedriver + 4072720\n", "9   chromedriver                        0x0000000103352fbc chromedriver + 4091836\n", "10  chromedriver                        0x0000000103335754 chromedriver + 3970900\n", "11  chromedriver                        0x00000001033538a4 chromedriver + 4094116\n", "12  chromedriver                        0x00000001033286d4 chromedriver + 3917524\n", "13  chromedriver                        0x0000000103370b08 chromedriver + 4213512\n", "14  chromedriver                        0x0000000103370c84 chromedriver + 4213892\n", "15  chromedriver                        0x000000010337ea08 chromedriver + 4270600\n", "16  libsystem_pthread.dylib             0x000000019882206c _pthread_start + 320\n", "17  libsystem_pthread.dylib             0x000000019881cda0 thread_start + 8\n", "\n", "An error occurred while extracting data from a parent element: Message: no such element: Unable to locate element: {\"method\":\"xpath\",\"selector\":\".//cite[@class='qLRx3b tjvcx GvPZzd cHaqb']\"}\n", "  (Session info: chrome=125.0.6422.142); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n", "Stacktrace:\n", "0   chromedriver                        0x00000001033864c8 chromedriver + 4302024\n", "1   chromedriver                        0x000000010337ee10 chromedriver + 4271632\n", "2   chromedriver                        0x0000000102fb019c chromedriver + 278940\n", "3   chromedriver                        0x0000000102ff22c4 chromedriver + 549572\n", "4   chromedriver                        0x0000000102fe885c chromedriver + 510044\n", "5   chromedriver                        0x000000010302ac5c chromedriver + 781404\n", "6   chromedriver                        0x0000000102fe7004 chromedriver + 503812\n", "7   chromedriver                        0x0000000102fe79ec chromedriver + 506348\n", "8   chromedriver                        0x000000010334e510 chromedriver + 4072720\n", "9   chromedriver                        0x0000000103352fbc chromedriver + 4091836\n", "10  chromedriver                        0x0000000103335754 chromedriver + 3970900\n", "11  chromedriver                        0x00000001033538a4 chromedriver + 4094116\n", "12  chromedriver                        0x00000001033286d4 chromedriver + 3917524\n", "13  chromedriver                        0x0000000103370b08 chromedriver + 4213512\n", "14  chromedriver                        0x0000000103370c84 chromedriver + 4213892\n", "15  chromedriver                        0x000000010337ea08 chromedriver + 4270600\n", "16  libsystem_pthread.dylib             0x000000019882206c _pthread_start + 320\n", "17  libsystem_pthread.dylib             0x000000019881cda0 thread_start + 8\n", "\n", "An error occurred while extracting data from a parent element: Message: unknown error: session deleted because of page crash\n", "from unknown error: cannot determine loading status\n", "from tab crashed\n", "  (Session info: chrome=125.0.6422.142)\n", "Stacktrace:\n", "0   chromedriver                        0x00000001033864c8 chromedriver + 4302024\n", "1   chromedriver                        0x000000010337ee10 chromedriver + 4271632\n", "2   chromedriver                        0x0000000102fb0000 chromedriver + 278528\n", "3   chromedriver                        0x0000000102f9bb30 chromedriver + 195376\n", "4   chromedriver                        0x0000000102f9b474 chromedriver + 193652\n", "5   chromedriver                        0x0000000102f9aad0 chromedriver + 191184\n", "6   chromedriver                        0x0000000102f9aa7c chromedriver + 191100\n", "7   chromedriver                        0x0000000102f9885c chromedriver + 182364\n", "8   chromedriver                        0x0000000102f99310 chromedriver + 185104\n", "9   chromedriver                        0x0000000102fa7620 chromedriver + 243232\n", "10  chromedriver                        0x0000000102fba55c chromedriver + 320860\n", "11  chromedriver                        0x0000000102f99958 chromedriver + 186712\n", "12  chromedriver                        0x0000000102fba1e0 chromedriver + 319968\n", "13  chromedriver                        0x000000010302ade4 chromedriver + 781796\n", "14  chromedriver                        0x0000000102fe7004 chromedriver + 503812\n", "15  chromedriver                        0x0000000102fe79ec chromedriver + 506348\n", "16  chromedriver                        0x000000010334e510 chromedriver + 4072720\n", "17  chromedriver                        0x0000000103352fbc chromedriver + 4091836\n", "18  chromedriver                        0x0000000103335754 chromedriver + 3970900\n", "19  chromedriver                        0x00000001033538a4 chromedriver + 4094116\n", "20  chromedriver                        0x00000001033286d4 chromedriver + 3917524\n", "21  chromedriver                        0x0000000103370b08 chromedriver + 4213512\n", "22  chromedriver                        0x0000000103370c84 chromedriver + 4213892\n", "23  chromedriver                        0x000000010337ea08 chromedriver + 4270600\n", "24  libsystem_pthread.dylib             0x000000019882206c _pthread_start + 320\n", "25  libsystem_pthread.dylib             0x000000019881cda0 thread_start + 8\n", "\n", "An error occurred while extracting data from a parent element: Message: invalid session id\n", "Stacktrace:\n", "0   chromedriver                        0x00000001033864c8 chromedriver + 4302024\n", "1   chromedriver                        0x000000010337ee10 chromedriver + 4271632\n", "2   chromedriver                        0x0000000102fb0000 chromedriver + 278528\n", "3   chromedriver                        0x0000000102fe6eb8 chromedriver + 503480\n", "4   chromedriver                        0x0000000102fe79ec chromedriver + 506348\n", "5   chromedriver                        0x000000010334e510 chromedriver + 4072720\n", "6   chromedriver                        0x0000000103352fbc chromedriver + 4091836\n", "7   chromedriver                        0x0000000103335754 chromedriver + 3970900\n", "8   chromedriver                        0x00000001033538a4 chromedriver + 4094116\n", "9   chromedriver                        0x00000001033286d4 chromedriver + 3917524\n", "10  chromedriver                        0x0000000103370b08 chromedriver + 4213512\n", "11  chromedriver                        0x0000000103370c84 chromedriver + 4213892\n", "12  chromedriver                        0x000000010337ea08 chromedriver + 4270600\n", "13  libsystem_pthread.dylib             0x000000019882206c _pthread_start + 320\n", "14  libsystem_pthread.dylib             0x000000019881cda0 thread_start + 8\n", "\n", "An error occurred while extracting data from a parent element: Message: invalid session id\n", "Stacktrace:\n", "0   chromedriver                        0x00000001033864c8 chromedriver + 4302024\n", "1   chromedriver                        0x000000010337ee10 chromedriver + 4271632\n", "2   chromedriver                        0x0000000102fb0000 chromedriver + 278528\n", "3   chromedriver                        0x0000000102fe66b4 chromedriver + 501428\n", "4   chromedriver                        0x000000010300fb44 chromedriver + 670532\n", "5   chromedriver                        0x000000010300afac chromedriver + 651180\n", "6   chromedriver                        0x000000010300a6b4 chromedriver + 648884\n", "7   chromedriver                        0x0000000102f82184 chromedriver + 90500\n", "8   chromedriver                        0x000000010334e510 chromedriver + 4072720\n", "9   chromedriver                        0x0000000103352fbc chromedriver + 4091836\n", "10  chromedriver                        0x0000000103335754 chromedriver + 3970900\n", "11  chromedriver                        0x00000001033538a4 chromedriver + 4094116\n", "12  chromedriver                        0x00000001033286d4 chromedriver + 3917524\n", "13  chromedriver                        0x0000000102f80f80 chromedriver + 85888\n", "14  libdyld.dylib                       0x000000019883df34 start + 4\n", "\n", "An error occurred while extracting data from a parent element: Message: invalid session id\n", "Stacktrace:\n", "0   chromedriver                        0x00000001033864c8 chromedriver + 4302024\n", "1   chromedriver                        0x000000010337ee10 chromedriver + 4271632\n", "2   chromedriver                        0x0000000102fb0000 chromedriver + 278528\n", "3   chromedriver                        0x0000000102fe66b4 chromedriver + 501428\n", "4   chromedriver                        0x000000010300fb44 chromedriver + 670532\n", "5   chromedriver                        0x000000010300afac chromedriver + 651180\n", "6   chromedriver                        0x000000010300a6b4 chromedriver + 648884\n", "7   chromedriver                        0x0000000102f82184 chromedriver + 90500\n", "8   chromedriver                        0x000000010334e510 chromedriver + 4072720\n", "9   chromedriver                        0x0000000103352fbc chromedriver + 4091836\n", "10  chromedriver                        0x0000000103335754 chromedriver + 3970900\n", "11  chromedriver                        0x00000001033538a4 chromedriver + 4094116\n", "12  chromedriver                        0x00000001033286d4 chromedriver + 3917524\n", "13  chromedriver                        0x0000000102f80f80 chromedriver + 85888\n", "14  libdyld.dylib                       0x000000019883df34 start + 4\n", "\n", "An error occurred while extracting data from a parent element: Message: invalid session id\n", "Stacktrace:\n", "0   chromedriver                        0x00000001033864c8 chromedriver + 4302024\n", "1   chromedriver                        0x000000010337ee10 chromedriver + 4271632\n", "2   chromedriver                        0x0000000102fb0000 chromedriver + 278528\n", "3   chromedriver                        0x0000000102fe66b4 chromedriver + 501428\n", "4   chromedriver                        0x000000010300fb44 chromedriver + 670532\n", "5   chromedriver                        0x000000010300afac chromedriver + 651180\n", "6   chromedriver                        0x000000010300a6b4 chromedriver + 648884\n", "7   chromedriver                        0x0000000102f82184 chromedriver + 90500\n", "8   chromedriver                        0x000000010334e510 chromedriver + 4072720\n", "9   chromedriver                        0x0000000103352fbc chromedriver + 4091836\n", "10  chromedriver                        0x0000000103335754 chromedriver + 3970900\n", "11  chromedriver                        0x00000001033538a4 chromedriver + 4094116\n", "12  chromedriver                        0x00000001033286d4 chromedriver + 3917524\n", "13  chromedriver                        0x0000000102f80f80 chromedriver + 85888\n", "14  libdyld.dylib                       0x000000019883df34 start + 4\n", "\n", "An error occurred while extracting data from a parent element: Message: invalid session id\n", "Stacktrace:\n", "0   chromedriver                        0x00000001033864c8 chromedriver + 4302024\n", "1   chromedriver                        0x000000010337ee10 chromedriver + 4271632\n", "2   chromedriver                        0x0000000102fb0000 chromedriver + 278528\n", "3   chromedriver                        0x0000000102fe66b4 chromedriver + 501428\n", "4   chromedriver                        0x000000010300fb44 chromedriver + 670532\n", "5   chromedriver                        0x000000010300afac chromedriver + 651180\n", "6   chromedriver                        0x000000010300a6b4 chromedriver + 648884\n", "7   chromedriver                        0x0000000102f82184 chromedriver + 90500\n", "8   chromedriver                        0x000000010334e510 chromedriver + 4072720\n", "9   chromedriver                        0x0000000103352fbc chromedriver + 4091836\n", "10  chromedriver                        0x0000000103335754 chromedriver + 3970900\n", "11  chromedriver                        0x00000001033538a4 chromedriver + 4094116\n", "12  chromedriver                        0x00000001033286d4 chromedriver + 3917524\n", "13  chromedriver                        0x0000000102f80f80 chromedriver + 85888\n", "14  libdyld.dylib                       0x000000019883df34 start + 4\n", "\n", "An error occurred while extracting data from a parent element: Message: invalid session id\n", "Stacktrace:\n", "0   chromedriver                        0x00000001033864c8 chromedriver + 4302024\n", "1   chromedriver                        0x000000010337ee10 chromedriver + 4271632\n", "2   chromedriver                        0x0000000102fb0000 chromedriver + 278528\n", "3   chromedriver                        0x0000000102fe66b4 chromedriver + 501428\n", "4   chromedriver                        0x000000010300fb44 chromedriver + 670532\n", "5   chromedriver                        0x000000010300afac chromedriver + 651180\n", "6   chromedriver                        0x000000010300a6b4 chromedriver + 648884\n", "7   chromedriver                        0x0000000102f82184 chromedriver + 90500\n", "8   chromedriver                        0x000000010334e510 chromedriver + 4072720\n", "9   chromedriver                        0x0000000103352fbc chromedriver + 4091836\n", "10  chromedriver                        0x0000000103335754 chromedriver + 3970900\n", "11  chromedriver                        0x00000001033538a4 chromedriver + 4094116\n", "12  chromedriver                        0x00000001033286d4 chromedriver + 3917524\n", "13  chromedriver                        0x0000000102f80f80 chromedriver + 85888\n", "14  libdyld.dylib                       0x000000019883df34 start + 4\n", "\n", "An error occurred while extracting data from a parent element: Message: invalid session id\n", "Stacktrace:\n", "0   chromedriver                        0x00000001033864c8 chromedriver + 4302024\n", "1   chromedriver                        0x000000010337ee10 chromedriver + 4271632\n", "2   chromedriver                        0x0000000102fb0000 chromedriver + 278528\n", "3   chromedriver                        0x0000000102fe66b4 chromedriver + 501428\n", "4   chromedriver                        0x000000010300fb44 chromedriver + 670532\n", "5   chromedriver                        0x000000010300afac chromedriver + 651180\n", "6   chromedriver                        0x000000010300a6b4 chromedriver + 648884\n", "7   chromedriver                        0x0000000102f82184 chromedriver + 90500\n", "8   chromedriver                        0x000000010334e510 chromedriver + 4072720\n", "9   chromedriver                        0x0000000103352fbc chromedriver + 4091836\n", "10  chromedriver                        0x0000000103335754 chromedriver + 3970900\n", "11  chromedriver                        0x00000001033538a4 chromedriver + 4094116\n", "12  chromedriver                        0x00000001033286d4 chromedriver + 3917524\n", "13  chromedriver                        0x0000000102f80f80 chromedriver + 85888\n", "14  libdyld.dylib                       0x000000019883df34 start + 4\n", "\n", "An error occurred while extracting data from a parent element: Message: invalid session id\n", "Stacktrace:\n", "0   chromedriver                        0x00000001033864c8 chromedriver + 4302024\n", "1   chromedriver                        0x000000010337ee10 chromedriver + 4271632\n", "2   chromedriver                        0x0000000102fb0000 chromedriver + 278528\n", "3   chromedriver                        0x0000000102fe66b4 chromedriver + 501428\n", "4   chromedriver                        0x000000010300fb44 chromedriver + 670532\n", "5   chromedriver                        0x000000010300afac chromedriver + 651180\n", "6   chromedriver                        0x000000010300a6b4 chromedriver + 648884\n", "7   chromedriver                        0x0000000102f82184 chromedriver + 90500\n", "8   chromedriver                        0x000000010334e510 chromedriver + 4072720\n", "9   chromedriver                        0x0000000103352fbc chromedriver + 4091836\n", "10  chromedriver                        0x0000000103335754 chromedriver + 3970900\n", "11  chromedriver                        0x00000001033538a4 chromedriver + 4094116\n", "12  chromedriver                        0x00000001033286d4 chromedriver + 3917524\n", "13  chromedriver                        0x0000000102f80f80 chromedriver + 85888\n", "14  libdyld.dylib                       0x000000019883df34 start + 4\n", "\n", "An error occurred while extracting data from a parent element: Message: invalid session id\n", "Stacktrace:\n", "0   chromedriver                        0x00000001033864c8 chromedriver + 4302024\n", "1   chromedriver                        0x000000010337ee10 chromedriver + 4271632\n", "2   chromedriver                        0x0000000102fb0000 chromedriver + 278528\n", "3   chromedriver                        0x0000000102fe66b4 chromedriver + 501428\n", "4   chromedriver                        0x000000010300fb44 chromedriver + 670532\n", "5   chromedriver                        0x000000010300afac chromedriver + 651180\n", "6   chromedriver                        0x000000010300a6b4 chromedriver + 648884\n", "7   chromedriver                        0x0000000102f82184 chromedriver + 90500\n", "8   chromedriver                        0x000000010334e510 chromedriver + 4072720\n", "9   chromedriver                        0x0000000103352fbc chromedriver + 4091836\n", "10  chromedriver                        0x0000000103335754 chromedriver + 3970900\n", "11  chromedriver                        0x00000001033538a4 chromedriver + 4094116\n", "12  chromedriver                        0x00000001033286d4 chromedriver + 3917524\n", "13  chromedriver                        0x0000000102f80f80 chromedriver + 85888\n", "14  libdyld.dylib                       0x000000019883df34 start + 4\n", "\n", "An error occurred while extracting data from a parent element: Message: invalid session id\n", "Stacktrace:\n", "0   chromedriver                        0x00000001033864c8 chromedriver + 4302024\n", "1   chromedriver                        0x000000010337ee10 chromedriver + 4271632\n", "2   chromedriver                        0x0000000102fb0000 chromedriver + 278528\n", "3   chromedriver                        0x0000000102fe66b4 chromedriver + 501428\n", "4   chromedriver                        0x000000010300fb44 chromedriver + 670532\n", "5   chromedriver                        0x000000010300afac chromedriver + 651180\n", "6   chromedriver                        0x000000010300a6b4 chromedriver + 648884\n", "7   chromedriver                        0x0000000102f82184 chromedriver + 90500\n", "8   chromedriver                        0x000000010334e510 chromedriver + 4072720\n", "9   chromedriver                        0x0000000103352fbc chromedriver + 4091836\n", "10  chromedriver                        0x0000000103335754 chromedriver + 3970900\n", "11  chromedriver                        0x00000001033538a4 chromedriver + 4094116\n", "12  chromedriver                        0x00000001033286d4 chromedriver + 3917524\n", "13  chromedriver                        0x0000000102f80f80 chromedriver + 85888\n", "14  libdyld.dylib                       0x000000019883df34 start + 4\n", "\n"]}], "source": ["url = \"https://www.google.com/\"\n", "driver.get(url)\n", "wait = WebDriverWait(driver, 10)\n", "\n", "# Locate the textarea using the corrected XPath\n", "textarea = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.XPATH, \"//textarea[@id='APjFqb' and @class='gLFyf']\")))\n", "\n", "# Send keys to the textarea\n", "textarea.send_keys('kho<PERSON> học data ngắn hạn')\n", "textarea.send_keys(Keys.RETURN)  # Press Enter to search\n", "\n", "# List to store the extracted information\n", "data = []\n", "\n", "try:\n", "    # Initial wait for elements to load\n", "    WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located((By.XPATH, \"//div[@class='MjjYud']\"))\n", "    )\n", "\n", "    previous_len = 0\n", "\n", "    while True:\n", "        # Locate parent elements\n", "        parent_elements = driver.find_elements(By.XPATH, \"//div[@class='MjjYud']\")\n", "\n", "        if len(parent_elements) == previous_len:\n", "            # No new elements were loaded\n", "            break\n", "        \n", "        previous_len = len(parent_elements)\n", "\n", "        # Extract information from each parent element\n", "        for parent in parent_elements:\n", "            try:\n", "                # Get the URL from the cite element\n", "                cite_element = parent.find_element(By.XPATH, \".//cite[@class='qLRx3b tjvcx GvPZzd cHaqb']\")\n", "                url = cite_element.text if cite_element else ''\n", "\n", "                # Get the title from the h3 element\n", "                h3_element = parent.find_element(By.XPATH, \".//h3[@class='LC20lb MBeuO DKV0Md']\")\n", "                title = h3_element.text if h3_element else ''\n", "\n", "                # Get all text from span elements within the parent\n", "                span_elements = parent.find_elements(By.XPATH, \".//div[@class='VwiC3b yXK7lf lVm3ye r025kc hJNv6b Hdw6tb']//span\")\n", "                span_texts = [span.text for span in span_elements]\n", "                all_span_text = \" \".join(span_texts)\n", "\n", "                # Click on the cite element to open the page\n", "                cite_element.click()\n", "                sleep(2)  # Allow time for the new page to load\n", "\n", "                # Extract all h1 and h2 elements from the new page\n", "                h1_elements = driver.find_elements(By.XPATH, \"//h1\")\n", "                h2_elements = driver.find_elements(By.XPATH, \"//h2\")\n", "                h1_texts = [h1.text for h1 in h1_elements]\n", "                h2_texts = [h2.text for h2 in h2_elements]\n", "\n", "                # Navigate back to the original list\n", "                driver.back()\n", "                sleep(2)  # Allow time to navigate back\n", "\n", "                # Add the data to the list\n", "                data.append({\n", "                    'URL': url,\n", "                    'Title': title,\n", "                    'SpanText': all_span_text,\n", "                    'H1Texts': h1_texts,\n", "                    'H2Texts': h2_texts\n", "                })\n", "\n", "            except Exception as inner_e:\n", "                print(f\"An error occurred while extracting data from a parent element: {inner_e}\")\n", "\n", "        # Try to scroll and load more content\n", "        if not scroll_and_load_more(driver):\n", "            break\n", "\n", "except Exception as e:\n", "    print(f\"An error occurred: {e}\")\n", "\n", "finally:\n", "    df = pd.DataFrame(data)\n", "    df.to_excel(f'Scraped_Data-{datetime.now()}.xlsx',index=None)\n", "    driver.quit()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                                 URL  \\\n", "0       https://glints.com › Home › Thế Giới Công Sở   \n", "1  https://som.edu.vn › hoc-data-analyst-o-dau-go...   \n", "2  https://csc.edu.vn › lich-khai-giang › data-sc...   \n", "3                    https://www.facebook.com › dtnh   \n", "4            https://fsoft-academy.edu.vn › <PERSON><PERSON><PERSON> học   \n", "\n", "                                               Title  \\\n", "0     13 Khóa Học Data Science Cho Người Mới Bắt Đầu   \n", "1  Học data analyst ở đâu? Top 8 khóa học data an...   \n", "2  Kh<PERSON>a học Machine Learning - Data Science - Dat...   \n", "3                 <PERSON><PERSON><PERSON> t<PERSON><PERSON> ng<PERSON>n hạn AI - Data Science   \n", "4                                     Data Analytics   \n", "\n", "                                            SpanText  \\\n", "0  14 thg 4, 2022 — 14 thg 4, 2022 <PERSON><PERSON><PERSON> h<PERSON> n...   \n", "1  28 thg 4, 2023 — 28 thg 4, 2023 Phân tích dữ l...   \n", "2  ƯU ĐÃI CÁC LỚP CHUYÊN ĐỀ NGẮN HẠN 5 TUẦN. Tặng...   \n", "3  <PERSON><PERSON><PERSON> t<PERSON><PERSON> ng<PERSON>n hạn AI - Data Science, Hà Nội. 59...   \n", "4  Với khóa học Data Analytics, học viên sẽ được ...   \n", "\n", "                                             H1Texts  \\\n", "0   [13 <PERSON><PERSON><PERSON>a <PERSON>c Data Science Cho Ng<PERSON>ời Mới <PERSON>]   \n", "1  [Học data analyst ở đâu? Top 8 khóa học data a...   \n", "2        [<PERSON><PERSON><PERSON><PERSON> tin ưu đã<PERSON> h<PERSON>, <PERSON><PERSON><PERSON>]   \n", "3              [<PERSON><PERSON><PERSON> t<PERSON><PERSON> ng<PERSON>n hạn AI - Data Science ]   \n", "4                                   [Data Analytics]   \n", "\n", "                                             H2Texts  \n", "0  [<PERSON><PERSON><PERSON> kh<PERSON> học data science mi<PERSON><PERSON> ph<PERSON> , <PERSON><PERSON><PERSON> kh<PERSON>...  \n", "1  [T<PERSON> học data analysis c<PERSON> bản cho ng<PERSON><PERSON> mới, Kh...  \n", "2                                                 []  \n", "3  [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>n hạn AI - Data Sci...  \n", "4                                                 []  \n"]}], "source": ["print(df.head())"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}