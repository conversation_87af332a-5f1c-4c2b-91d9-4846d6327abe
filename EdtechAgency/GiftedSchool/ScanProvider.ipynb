{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.common.exceptions import TimeoutException, StaleElementReferenceException\n", "import openpyxl\n", "import logging\n", "import time\n", "\n", "# Setup logging\n", "logging.basicConfig(filename='scraping_process.log', level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "\n", "# Load the Excel file into a DataFrame\n", "df = pd.read_excel('/Users/<USER>/Downloads/List-Data.xlsx', sheet_name='Providers')\n", "df = df[df['Nguồn trong datawarehouse'] == 'EDVN']\n", "df = df.iloc[178:]\n", "\n", "# Initialize the WebDriver\n", "chrome_options = Options()\n", "# chrome_options.add_argument(\"--incognito\")\n", "chrome_options.add_argument('--headless')\n", "chrome_options.add_argument('--disable-gpu')  # Recommended for headless mode\n", "# chrome_options.add_argument('--no-sandbox')  # May be necessary in some environments\n", "driver = webdriver.Chrome(options=chrome_options)\n", "\n", "# Output file path\n", "output_path = '/Users/<USER>/Downloads/List-Data-Info.xlsx'\n", "\n", "# Initialize the Excel file for writing (create it if not exists)\n", "with pd.ExcelWriter(output_path, engine='openpyxl', mode='w') as writer:\n", "    df.to_excel(writer, sheet_name='Info', index=False)\n", "\n", "# Function to perform the search and scrape the required information\n", "def search_and_scrape(query):\n", "    try:\n", "        # Open the search engine page\n", "        driver.get(\"https://www.google.com\")\n", "        logging.info(f\"Searching for: {query}\")\n", "\n", "        # Locate the search box, enter the query, and submit\n", "        search_box = driver.find_element(By.NAME, \"q\")\n", "        search_box.send_keys(query)\n", "        search_box.submit()\n", "\n", "        # Wait for the page to load and attempt to scrape the required info\n", "        WebDriverWait(driver, 3).until(EC.presence_of_element_located((By.XPATH, \"//span[@class='hgKElc']\")))\n", "        scraped_text = driver.find_element(By.XPATH, \"//div[@class='yp1CPe wDYxhc NFQFxe viOShc LKPcQc']//div//div//div//span//span[@class='hgKElc']\").text\n", "        logging.info(f\"Scraped text: {scraped_text}\")\n", "        return scraped_text\n", "\n", "    except TimeoutException:\n", "        logging.error(f\"Timeout occurred for query: {query}\")\n", "        return None\n", "    except StaleElementReferenceException:\n", "        logging.warning(f\"StaleElementReferenceException for query: {query}. Retrying...\")\n", "        time.sleep(1)\n", "        return search_and_scrape(query)\n", "    except Exception as e:\n", "        logging.error(f\"Error during scraping for query: {query}, Error: {e}\")\n", "        return None\n", "\n", "# Loop through each row in the DataFrame and scrape the information\n", "for index, row in df.iterrows():\n", "    search_query = f\"{row[0]} chư<PERSON>ng trình đào tạo\"\n", "    result = search_and_scrape(search_query)\n", "\n", "    # Write the scraped result back to the DataFrame\n", "    df.loc[index, 'Scraped_Result'] = result\n", "\n", "    # Save the updated DataFrame to the Excel file immediately\n", "    with pd.ExcelWriter(output_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:\n", "        df.to_excel(writer, sheet_name='Info', index=False)\n", "\n", "    logging.info(f\"Result for row {index + 1} written to the Excel file.\")\n", "\n", "    time.sleep(1)  # Small delay between requests\n", "\n", "# Quit the WebDriver\n", "driver.quit()\n", "logging.info(\"WebDriver quit successfully.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[2], line 76\u001b[0m\n\u001b[1;32m     74\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m index, row \u001b[38;5;129;01min\u001b[39;00m df\u001b[38;5;241m.\u001b[39miterrows():\n\u001b[1;32m     75\u001b[0m     search_query \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrow[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m tuyển dụng gi<PERSON><PERSON> viên\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m---> 76\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[43msearch_and_scrape\u001b[49m\u001b[43m(\u001b[49m\u001b[43msearch_query\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     78\u001b[0m     \u001b[38;5;66;03m# Write the scraped result back to the DataFrame\u001b[39;00m\n\u001b[1;32m     79\u001b[0m     df\u001b[38;5;241m.\u001b[39mloc[index, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mScraped_Result\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m result\n", "Cell \u001b[0;32mIn[2], line 47\u001b[0m, in \u001b[0;36msearch_and_scrape\u001b[0;34m(query)\u001b[0m\n\u001b[1;32m     44\u001b[0m search_box\u001b[38;5;241m.\u001b[39msubmit()\n\u001b[1;32m     46\u001b[0m \u001b[38;5;66;03m# Wait for the search results to load\u001b[39;00m\n\u001b[0;32m---> 47\u001b[0m \u001b[43mWebDriverWait\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdriver\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m10\u001b[39;49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43muntil\u001b[49m\u001b[43m(\u001b[49m\u001b[43mEC\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpresence_of_all_elements_located\u001b[49m\u001b[43m(\u001b[49m\u001b[43m(\u001b[49m\u001b[43mBy\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mXPATH\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m//div[@class=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mg\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m]\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     49\u001b[0m \u001b[38;5;66;03m# Scrape the first 5 links\u001b[39;00m\n\u001b[1;32m     50\u001b[0m links \u001b[38;5;241m=\u001b[39m []\n", "File \u001b[0;32m/opt/homebrew/lib/python3.11/site-packages/selenium/webdriver/support/wait.py:86\u001b[0m, in \u001b[0;36mWebDriverWait.until\u001b[0;34m(self, method, message)\u001b[0m\n\u001b[1;32m     84\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[1;32m     85\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 86\u001b[0m         value \u001b[38;5;241m=\u001b[39m \u001b[43mmethod\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_driver\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     87\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m value:\n\u001b[1;32m     88\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m value\n", "File \u001b[0;32m/opt/homebrew/lib/python3.11/site-packages/selenium/webdriver/support/expected_conditions.py:187\u001b[0m, in \u001b[0;36mpresence_of_all_elements_located.<locals>._predicate\u001b[0;34m(driver)\u001b[0m\n\u001b[1;32m    186\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_predicate\u001b[39m(driver):\n\u001b[0;32m--> 187\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[43mdriver\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfind_elements\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mlocator\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/homebrew/lib/python3.11/site-packages/selenium/webdriver/remote/webdriver.py:769\u001b[0m, in \u001b[0;36mWebDriver.find_elements\u001b[0;34m(self, by, value)\u001b[0m\n\u001b[1;32m    765\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m[name=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mvalue\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m]\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[1;32m    767\u001b[0m \u001b[38;5;66;03m# Return empty list if driver returns null\u001b[39;00m\n\u001b[1;32m    768\u001b[0m \u001b[38;5;66;03m# See https://github.com/SeleniumHQ/selenium/issues/4555\u001b[39;00m\n\u001b[0;32m--> 769\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mCommand\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mFIND_ELEMENTS\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m{\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43musing\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mby\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mvalue\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m}\u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalue\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;129;01mor\u001b[39;00m []\n", "File \u001b[0;32m/opt/homebrew/lib/python3.11/site-packages/selenium/webdriver/remote/webdriver.py:343\u001b[0m, in \u001b[0;36mWebDriver.execute\u001b[0;34m(self, driver_command, params)\u001b[0m\n\u001b[1;32m    340\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msessionId\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m params:\n\u001b[1;32m    341\u001b[0m         params[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msessionId\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msession_id\n\u001b[0;32m--> 343\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcommand_executor\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdriver_command\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    344\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m response:\n\u001b[1;32m    345\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39merror_handler\u001b[38;5;241m.\u001b[39mcheck_response(response)\n", "File \u001b[0;32m/opt/homebrew/lib/python3.11/site-packages/selenium/webdriver/remote/remote_connection.py:291\u001b[0m, in \u001b[0;36mRemoteConnection.execute\u001b[0;34m(self, command, params)\u001b[0m\n\u001b[1;32m    289\u001b[0m data \u001b[38;5;241m=\u001b[39m utils\u001b[38;5;241m.\u001b[39mdump_json(params)\n\u001b[1;32m    290\u001b[0m url \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_url\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00mpath\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m--> 291\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcommand_info\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/homebrew/lib/python3.11/site-packages/selenium/webdriver/remote/remote_connection.py:312\u001b[0m, in \u001b[0;36mRemoteConnection._request\u001b[0;34m(self, method, url, body)\u001b[0m\n\u001b[1;32m    309\u001b[0m     body \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    311\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mkeep_alive:\n\u001b[0;32m--> 312\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_conn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    313\u001b[0m     statuscode \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mstatus\n\u001b[1;32m    314\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[0;32m/opt/homebrew/lib/python3.11/site-packages/urllib3/request.py:78\u001b[0m, in \u001b[0;36mRequestMethods.request\u001b[0;34m(self, method, url, fields, headers, **urlopen_kw)\u001b[0m\n\u001b[1;32m     74\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrequest_encode_url(\n\u001b[1;32m     75\u001b[0m         method, url, fields\u001b[38;5;241m=\u001b[39mfields, headers\u001b[38;5;241m=\u001b[39mheaders, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39murlopen_kw\n\u001b[1;32m     76\u001b[0m     )\n\u001b[1;32m     77\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m---> 78\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest_encode_body\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     79\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfields\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfields\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43murlopen_kw\u001b[49m\n\u001b[1;32m     80\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/homebrew/lib/python3.11/site-packages/urllib3/request.py:170\u001b[0m, in \u001b[0;36mRequestMethods.request_encode_body\u001b[0;34m(self, method, url, fields, headers, encode_multipart, multipart_boundary, **urlopen_kw)\u001b[0m\n\u001b[1;32m    167\u001b[0m extra_kw[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mheaders\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39mupdate(headers)\n\u001b[1;32m    168\u001b[0m extra_kw\u001b[38;5;241m.\u001b[39mupdate(urlopen_kw)\n\u001b[0;32m--> 170\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mextra_kw\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/homebrew/lib/python3.11/site-packages/urllib3/poolmanager.py:376\u001b[0m, in \u001b[0;36mPoolManager.urlopen\u001b[0;34m(self, method, url, redirect, **kw)\u001b[0m\n\u001b[1;32m    374\u001b[0m     response \u001b[38;5;241m=\u001b[39m conn\u001b[38;5;241m.\u001b[39murlopen(method, url, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkw)\n\u001b[1;32m    375\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 376\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest_uri\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkw\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    378\u001b[0m redirect_location \u001b[38;5;241m=\u001b[39m redirect \u001b[38;5;129;01mand\u001b[39;00m response\u001b[38;5;241m.\u001b[39mget_redirect_location()\n\u001b[1;32m    379\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m redirect_location:\n", "File \u001b[0;32m/opt/homebrew/lib/python3.11/site-packages/urllib3/connectionpool.py:714\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[1;32m    711\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_prepare_proxy(conn)\n\u001b[1;32m    713\u001b[0m \u001b[38;5;66;03m# Make the request on the httplib connection object.\u001b[39;00m\n\u001b[0;32m--> 714\u001b[0m httplib_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    715\u001b[0m \u001b[43m    \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    716\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    717\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    718\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    719\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    720\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    721\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    722\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    724\u001b[0m \u001b[38;5;66;03m# If we're going to release the connection in ``finally:``, then\u001b[39;00m\n\u001b[1;32m    725\u001b[0m \u001b[38;5;66;03m# the response doesn't need to know about the connection. Otherwise\u001b[39;00m\n\u001b[1;32m    726\u001b[0m \u001b[38;5;66;03m# it will also try to release it and we'll have a double-release\u001b[39;00m\n\u001b[1;32m    727\u001b[0m \u001b[38;5;66;03m# mess.\u001b[39;00m\n\u001b[1;32m    728\u001b[0m response_conn \u001b[38;5;241m=\u001b[39m conn \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m release_conn \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m/opt/homebrew/lib/python3.11/site-packages/urllib3/connectionpool.py:466\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, timeout, chunked, **httplib_request_kw)\u001b[0m\n\u001b[1;32m    461\u001b[0m             httplib_response \u001b[38;5;241m=\u001b[39m conn\u001b[38;5;241m.\u001b[39mgetresponse()\n\u001b[1;32m    462\u001b[0m         \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    463\u001b[0m             \u001b[38;5;66;03m# Remove the TypeError from the exception chain in\u001b[39;00m\n\u001b[1;32m    464\u001b[0m             \u001b[38;5;66;03m# Python 3 (including for exceptions like SystemExit).\u001b[39;00m\n\u001b[1;32m    465\u001b[0m             \u001b[38;5;66;03m# Otherwise it looks like a bug in the code.\u001b[39;00m\n\u001b[0;32m--> 466\u001b[0m             \u001b[43msix\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mraise_from\u001b[49m\u001b[43m(\u001b[49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m    467\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (SocketTimeout, BaseSSLError, SocketError) \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    468\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_raise_timeout(err\u001b[38;5;241m=\u001b[39me, url\u001b[38;5;241m=\u001b[39murl, timeout_value\u001b[38;5;241m=\u001b[39mread_timeout)\n", "File \u001b[0;32m<string>:3\u001b[0m, in \u001b[0;36mraise_from\u001b[0;34m(value, from_value)\u001b[0m\n", "File \u001b[0;32m/opt/homebrew/lib/python3.11/site-packages/urllib3/connectionpool.py:461\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, timeout, chunked, **httplib_request_kw)\u001b[0m\n\u001b[1;32m    458\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[1;32m    459\u001b[0m     \u001b[38;5;66;03m# Python 3\u001b[39;00m\n\u001b[1;32m    460\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 461\u001b[0m         httplib_response \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgetresponse\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    462\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    463\u001b[0m         \u001b[38;5;66;03m# Remove the TypeError from the exception chain in\u001b[39;00m\n\u001b[1;32m    464\u001b[0m         \u001b[38;5;66;03m# Python 3 (including for exceptions like SystemExit).\u001b[39;00m\n\u001b[1;32m    465\u001b[0m         \u001b[38;5;66;03m# Otherwise it looks like a bug in the code.\u001b[39;00m\n\u001b[1;32m    466\u001b[0m         six\u001b[38;5;241m.\u001b[39mraise_from(e, \u001b[38;5;28;01mNone\u001b[39;00m)\n", "File \u001b[0;32m/opt/homebrew/Cellar/python@3.11/3.11.4_1/Frameworks/Python.framework/Versions/3.11/lib/python3.11/http/client.py:1378\u001b[0m, in \u001b[0;36mHTTPConnection.getresponse\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1376\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1377\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1378\u001b[0m         \u001b[43mresponse\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbegin\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1379\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m:\n\u001b[1;32m   1380\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mclose()\n", "File \u001b[0;32m/opt/homebrew/Cellar/python@3.11/3.11.4_1/Frameworks/Python.framework/Versions/3.11/lib/python3.11/http/client.py:318\u001b[0m, in \u001b[0;36mHTTPResponse.begin\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    316\u001b[0m \u001b[38;5;66;03m# read until we get a non-100 response\u001b[39;00m\n\u001b[1;32m    317\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m--> 318\u001b[0m     version, status, reason \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_read_status\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    319\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m status \u001b[38;5;241m!=\u001b[39m CONTINUE:\n\u001b[1;32m    320\u001b[0m         \u001b[38;5;28;01<PERSON>ak\u001b[39;00m\n", "File \u001b[0;32m/opt/homebrew/Cellar/python@3.11/3.11.4_1/Frameworks/Python.framework/Versions/3.11/lib/python3.11/http/client.py:279\u001b[0m, in \u001b[0;36mHTTPResponse._read_status\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    278\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_read_status\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m--> 279\u001b[0m     line \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mstr\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfp\u001b[38;5;241m.\u001b[39mreadline(_MAXLINE \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1\u001b[39m), \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124miso-8859-1\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    280\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(line) \u001b[38;5;241m>\u001b[39m _MAXLINE:\n\u001b[1;32m    281\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m LineToo<PERSON>ong(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstatus line\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m/opt/homebrew/Cellar/python@3.11/3.11.4_1/Frameworks/Python.framework/Versions/3.11/lib/python3.11/socket.py:706\u001b[0m, in \u001b[0;36mSocketIO.readinto\u001b[0;34m(self, b)\u001b[0m\n\u001b[1;32m    704\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[1;32m    705\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 706\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sock\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrecv_into\u001b[49m\u001b[43m(\u001b[49m\u001b[43mb\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    707\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m timeout:\n\u001b[1;32m    708\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_timeout_occurred \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.common.exceptions import TimeoutException, StaleElementReferenceException\n", "import openpyxl\n", "import logging\n", "import time\n", "\n", "# Setup logging\n", "logging.basicConfig(filename='scraping_process2.log', level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "\n", "# Load the Excel file into a DataFrame\n", "df = pd.read_excel('/Users/<USER>/Downloads/Data.xlsx')\n", "\n", "# Initialize the WebDriver\n", "chrome_options = Options()\n", "chrome_options.add_argument('--headless')\n", "chrome_options.add_argument('--disable-gpu')\n", "chrome_options.add_argument('--disable-blink-features=AutomationControlled')  # Helps avoid detection\n", "chrome_options.add_argument('--window-size=1920,1080')  # Mimic a real user's browser size\n", "chrome_options.add_argument(\n", "    \"user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\"\n", ")\n", "driver = webdriver.Chrome(options=chrome_options)\n", "\n", "# Output file path\n", "output_path = '/Users/<USER>/Downloads/Data-Info.xlsx'\n", "\n", "# Initialize the Excel file for writing (create it if not exists)\n", "with pd.ExcelWriter(output_path, engine='openpyxl', mode='w') as writer:\n", "    df.to_excel(writer, sheet_name='Info', index=False)\n", "\n", "def search_and_scrape(query):\n", "    try:\n", "        # Open the search engine page\n", "        driver.get(\"https://www.google.com\")\n", "        logging.info(f\"Searching for: {query}\")\n", "\n", "        # Locate the search box, enter the query, and submit\n", "        search_box = WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located((By.NAME, \"q\"))\n", "        )\n", "        search_box.send_keys(query)\n", "        search_box.submit()\n", "\n", "        # Simulate scrolling to load content\n", "        time.sleep(1)  # Wait before scrolling\n", "        driver.execute_script(\"window.scrollTo(0, document.body.scrollHeight);\")\n", "\n", "        # Wait for the search results to load\n", "        WebDriverWait(driver, 10).until(EC.presence_of_all_elements_located((By.XPATH, \"//div[@class='g']\")))\n", "\n", "        # Scrape the first 5 links\n", "        links = []\n", "        search_results = driver.find_elements(By.XPATH, \"//div[@class='g']//a[@href]\")[:5]  # Get first 5 links\n", "        for result in search_results:\n", "            link = result.get_attribute(\"href\")\n", "            if link and \"google\" not in link:  # Avoid Google-related links\n", "                links.append(link)\n", "\n", "        # Join links with newline and return as single string\n", "        links_text = \"\\n\".join(links)\n", "        logging.info(f\"Scraped links for query '{query}':\\n{links_text}\")\n", "        return links_text\n", "\n", "    except TimeoutException:\n", "        logging.error(f\"Timeout occurred for query: {query}\")\n", "        return \"Timeout Error\"\n", "    except StaleElementReferenceException:\n", "        logging.warning(f\"StaleElementReferenceException for query: {query}. Retrying...\")\n", "        time.sleep(1)\n", "        return search_and_scrape(query)\n", "    except Exception as e:\n", "        logging.error(f\"Error during scraping for query: {query}, Error: {e}\")\n", "        return f\"Error: {e}\"\n", "\n", "# Loop through each row in the DataFrame and scrape the information\n", "for index, row in df.iterrows():\n", "    search_query = f\"{row[0]} tuyển dụng giáo viên\"\n", "    result = search_and_scrape(search_query)\n", "\n", "    # Write the scraped result back to the DataFrame\n", "    df.loc[index, 'Scraped_Result'] = result\n", "\n", "    # Save the updated DataFrame to the Excel file immediately\n", "    with pd.ExcelWriter(output_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:\n", "        df.to_excel(writer, sheet_name='Info', index=False)\n", "\n", "    logging.info(f\"Result for row {index + 1} written to the Excel file.\")\n", "\n", "    time.sleep(2)  # Small delay between requests\n", "\n", "# Quit the WebDriver\n", "driver.quit()\n", "logging.info(\"WebDriver quit successfully.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}