import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, StaleElementReferenceException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from time import sleep

# Initialize the WebDriver with options
chrome_options = Options()
chrome_options.add_argument("--incognito")
# chrome_options.add_argument('--headless')
chrome_options.add_argument('--window-size=1920,1080')
chrome_options.add_argument('--disable-gpu')
chrome_options.add_argument('--disable-dev-shm-usage')
chrome_options.add_argument('--no-sandbox')
chrome_options.add_argument('user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3')
driver = webdriver.Chrome(options=chrome_options)
actions = ActionChains(driver)

url = "https://zunia.vn/tra-cuu/diem-chuan-dai-hoc.html"
driver.get(url)
wait = WebDriverWait(driver, 10)
keywords = ['Quản trị kinh doanh', 'Kinh doanh quốc tế', 'Kế toán', 'Kinh tế', 'Luật', 'Quan hệ quốc tế']

# List to store the data
data = []

try:
    major_elements = WebDriverWait(driver, 10).until(
        EC.presence_of_all_elements_located((By.XPATH, "//div[2][@class='col-12 col-md-6 h2a']//div[@class='school-list bg-white mt-3 p-3']//ul/li"))
    )
    
    for major in major_elements:
        for keyword in keywords:
            if keyword.lower() in major.text.lower():
                major_link = major.find_element(By.TAG_NAME, "a")
                major_link.click()
                sleep(1)
                
                unis = WebDriverWait(driver, 10).until(
                    EC.presence_of_all_elements_located((By.XPATH, "//div[@class='table-industry table-responsive mt-2 d-none d-lg-block']//tr//td[3]"))
                )
                list_unis = [uni.text for uni in unis]
                
                data.append({"major": keyword, "list_of_universities": list_unis})
                
                driver.back()
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, "//div[2][@class='col-12 col-md-6 h2a']//div[@class='school-list bg-white mt-3 p-3']//ul/li"))
                )
                sleep(1)
                
                # Re-locate the major elements after navigating back
                major_elements = WebDriverWait(driver, 10).until(
                    EC.presence_of_all_elements_located((By.XPATH, "//div[2][@class='col-12 col-md-6 h2a']//div[@class='school-list bg-white mt-3 p-3']//ul/li"))
                )
except Exception as e:
    print(f"An error occurred: {e}")
finally:
    driver.quit()

# Create a DataFrame from the collected data
df = pd.DataFrame(data)

# Display the DataFrame
print(df)