{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# import pandas as pd\n", "# from selenium import webdriver\n", "# from selenium.webdriver.common.keys import Keys\n", "# from selenium.webdriver.support.ui import WebDriverWait\n", "# from selenium.webdriver.support import expected_conditions as EC\n", "# from selenium.webdriver.common.by import By\n", "# import time\n", "\n", "# df = pd.read_excel('/Users/<USER>/Downloads/Uni.xlsx')\n", "\n", "# # Keywords to search for\n", "# keywords = ['artificial intelligence', 'trí tuệ nhân tạo', 'học máy']\n", "\n", "# def contains_keywords(text, keywords):\n", "#     \"\"\"Check if any of the keywords are present in the text.\"\"\"\n", "#     return any(keyword.lower() in text.lower() for keyword in keywords)\n", "\n", "# # Initialize WebDriver\n", "# driver = webdriver.Chrome()\n", "# wait = WebDriverWait(driver, 10)\n", "\n", "# # DataFrame to save the labeled data\n", "# df_labeled = pd.DataFrame(columns=['Name', 'URL', 'Label'])\n", "\n", "# try:\n", "#     for index, row in df.iterrows():\n", "#         name = row['Name']\n", "#         try:\n", "#             # Open Google and search for the name\n", "#             driver.get('https://www.google.com')\n", "#             search_box = wait.until(EC.presence_of_element_located((By.XPATH, \"//textarea[@name='q']\")))\n", "#             search_box.send_keys(name)\n", "#             search_box.send_keys(Keys.RETURN)\n", "\n", "#             # Wait for search results to load\n", "#             wait.until(EC.presence_of_element_located((By.XPATH, \"//div[@class='MjjYud']\")))\n", "\n", "#             # Click on the first search result\n", "#             first_result = driver.find_element(By.XPATH, \"(//div[@class='MjjYud']//a)[1]\")\n", "#             first_result_url = first_result.get_attribute('href')\n", "#             first_result.click()\n", "\n", "#             # Wait for the page to load\n", "#             time.sleep(2)\n", "\n", "#             # Extract the entire page source text\n", "#             page_source = driver.page_source\n", "\n", "#             # Check if the page contains any of the keywords\n", "#             label = 1 if contains_keywords(page_source, keywords) else 0\n", "\n", "#             # Add the data to the labeled DataFrame\n", "#             df_labeled = df_labeled.append({'Name': name, 'URL': first_result_url, 'Label': label}, ignore_index=True)\n", "\n", "#             # Go back to the Google search results\n", "#             driver.back()\n", "#             time.sleep(2)\n", "\n", "#         except Exception as e:\n", "#             # If an error occurs, continue to the next name\n", "#             print(f\"Error processing {name}: {e}\")\n", "#             continue\n", "\n", "# finally:\n", "#     # Save the DataFrame to a CSV file\n", "#     df_labeled.to_csv('labeled_Uni.csv', index=False)\n", "#     driver.quit()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.common.keys import Keys\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.by import By\n", "import time\n", "from time import sleep\n", "\n", "# Function to scroll down and click the \"load more\" button if it exists\n", "def scroll_and_load_more(driver):\n", "    try:\n", "        # Scroll down\n", "        driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.END)\n", "        # driver.execute_script(\"window.scrollTo(0, document.body.scrollHeight);\")\n", "        sleep(2)  # Adjust the sleep time if necessary\n", "\n", "        # Try to click the \"load more\" button if it exists\n", "        try:\n", "            load_more_button = driver.find_element(By.XPATH, \"//div[@class='GNJvt ipz2Oe']\")\n", "            if load_more_button.is_displayed():\n", "                load_more_button.click()\n", "                sleep(2)  # Allow time for new content to load\n", "                driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.END)\n", "                sleep(2)  # Scroll down again after clicking load more\n", "        except Exception:\n", "            # If no \"load more\" button is found, continue\n", "            pass\n", "\n", "        return True\n", "    except Exception:\n", "        return False\n", "    \n", "df = pd.read_excel('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/GIZ/Uni.xlsx')\n", "\n", "# Keywords to search for\n", "keywords = ['artificial intelligence', 'machine learning', 'deep learning']\n", "\n", "def contains_keywords(text, keywords):\n", "    \"\"\"Check if any of the keywords are present in the text.\"\"\"\n", "    return any(keyword.lower() in text.lower() for keyword in keywords)\n", "\n", "# Initialize WebDriver\n", "driver = webdriver.Chrome()\n", "wait = WebDriverWait(driver, 10)\n", "\n", "# DataFrame to save the labeled data\n", "df_labeled = pd.DataFrame(columns=['Name', 'URL', 'Label'])\n", "\n", "try:\n", "    for index, row in df.iterrows():\n", "        name = row['Name']\n", "        try:\n", "            # Open Google and search for the name\n", "            driver.get('https://www.google.com')\n", "            search_box = wait.until(EC.presence_of_element_located((By.XPATH, \"//textarea[@name='q']\")))\n", "            search_box.send_keys(name)\n", "            search_box.send_keys(Keys.RETURN)\n", "\n", "            # Wait for search results to load\n", "            wait.until(EC.presence_of_element_located((By.XPATH, \"//div[@class='MjjYud']\")))\n", "\n", "            found_edu = False\n", "\n", "            # Iterate through the search results to find a result with 'edu' in the URL\n", "            while not found_edu:\n", "                search_results = driver.find_elements(By.XPATH, \"//div[@class='MjjYud']//a\")\n", "                for result in search_results:\n", "                    try:\n", "                        url_element = result.find_element(By.XPATH, \".//cite[@class='tjvcx GvPZzd cHaqb']\")\n", "                        if 'edu' in url_element.text:\n", "                            first_result_url = result.get_attribute('href')\n", "                            result.click()\n", "                            found_edu = True\n", "                            break\n", "                    except Exception:\n", "                        continue\n", "\n", "                if not found_edu:\n", "                    # Try to scroll and load more content\n", "                    if not scroll_and_load_more(driver):\n", "                        break\n", "\n", "            if not found_edu:\n", "                continue\n", "\n", "            # Wait for the page to load\n", "            time.sleep(2)\n", "\n", "            # Extract the entire page source text\n", "            page_source = driver.page_source\n", "\n", "            # Check if the page contains any of the keywords\n", "            label = 1 if contains_keywords(page_source, keywords) else 0\n", "\n", "            # Add the data to the labeled DataFrame\n", "            df_labeled = df_labeled.append({'Name': name, 'URL': first_result_url, 'Label': label}, ignore_index=True)\n", "\n", "            # Go back to the Google search results\n", "            driver.back()\n", "            time.sleep(2)\n", "\n", "        except Exception as e:\n", "            # If an error occurs, continue to the next name\n", "            print(f\"Error processing {name}: {e}\")\n", "            continue\n", "\n", "finally:\n", "    # Save the DataFrame to a CSV file\n", "    df_labeled.to_csv('labeled_data.csv', index=False)\n", "    driver.quit()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# SCRAPE URL OF UNIS"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error processing TRƯỜNG ĐẠI HỌC KINH BẮC: Message: \n", "Stacktrace:\n", "0   chromedriver                        0x0000000100b224c8 chromedriver + 4302024\n", "1   chromedriver                        0x0000000100b1ae10 chromedriver + 4271632\n", "2   chromedriver                        0x000000010074c19c chromedriver + 278940\n", "3   chromedriver                        0x000000010078e2c4 chromedriver + 549572\n", "4   chromedriver                        0x00000001007c6c5c chromedriver + 781404\n", "5   chromedriver                        0x0000000100783004 chromedriver + 503812\n", "6   chromedriver                        0x00000001007839ec chromedriver + 506348\n", "7   chromedriver                        0x0000000100aea510 chromedriver + 4072720\n", "8   chromedriver                        0x0000000100aeefbc chromedriver + 4091836\n", "9   chromedriver                        0x0000000100ad1754 chromedriver + 3970900\n", "10  chromedriver                        0x0000000100aef8a4 chromedriver + 4094116\n", "11  chromedriver                        0x0000000100ac46d4 chromedriver + 3917524\n", "12  chromedriver                        0x0000000100b0cb08 chromedriver + 4213512\n", "13  chromedriver                        0x0000000100b0cc84 chromedriver + 4213892\n", "14  chromedriver                        0x0000000100b1aa08 chromedriver + 4270600\n", "15  libsystem_pthread.dylib             0x0000000185db206c _pthread_start + 320\n", "16  libsystem_pthread.dylib             0x0000000185dacda0 thread_start + 8\n", "\n", "Error processing TRƯỜNG ĐẠI HỌC SƯ PHẠM, ĐẠI HỌC THÁI NGUYÊN: Message: \n", "Stacktrace:\n", "0   chromedriver                        0x0000000100b224c8 chromedriver + 4302024\n", "1   chromedriver                        0x0000000100b1ae10 chromedriver + 4271632\n", "2   chromedriver                        0x000000010074c19c chromedriver + 278940\n", "3   chromedriver                        0x000000010078e2c4 chromedriver + 549572\n", "4   chromedriver                        0x00000001007c6c5c chromedriver + 781404\n", "5   chromedriver                        0x0000000100783004 chromedriver + 503812\n", "6   chromedriver                        0x00000001007839ec chromedriver + 506348\n", "7   chromedriver                        0x0000000100aea510 chromedriver + 4072720\n", "8   chromedriver                        0x0000000100aeefbc chromedriver + 4091836\n", "9   chromedriver                        0x0000000100ad1754 chromedriver + 3970900\n", "10  chromedriver                        0x0000000100aef8a4 chromedriver + 4094116\n", "11  chromedriver                        0x0000000100ac46d4 chromedriver + 3917524\n", "12  chromedriver                        0x0000000100b0cb08 chromedriver + 4213512\n", "13  chromedriver                        0x0000000100b0cc84 chromedriver + 4213892\n", "14  chromedriver                        0x0000000100b1aa08 chromedriver + 4270600\n", "15  libsystem_pthread.dylib             0x0000000185db206c _pthread_start + 320\n", "16  libsystem_pthread.dylib             0x0000000185dacda0 thread_start + 8\n", "\n", "Error processing TRƯỜNG ĐẠI HỌC TÀI CHÍNH - KẾ TOÁN: Message: \n", "Stacktrace:\n", "0   chromedriver                        0x0000000100b224c8 chromedriver + 4302024\n", "1   chromedriver                        0x0000000100b1ae10 chromedriver + 4271632\n", "2   chromedriver                        0x000000010074c19c chromedriver + 278940\n", "3   chromedriver                        0x000000010078e2c4 chromedriver + 549572\n", "4   chromedriver                        0x00000001007c6c5c chromedriver + 781404\n", "5   chromedriver                        0x0000000100783004 chromedriver + 503812\n", "6   chromedriver                        0x00000001007839ec chromedriver + 506348\n", "7   chromedriver                        0x0000000100aea510 chromedriver + 4072720\n", "8   chromedriver                        0x0000000100aeefbc chromedriver + 4091836\n", "9   chromedriver                        0x0000000100ad1754 chromedriver + 3970900\n", "10  chromedriver                        0x0000000100aef8a4 chromedriver + 4094116\n", "11  chromedriver                        0x0000000100ac46d4 chromedriver + 3917524\n", "12  chromedriver                        0x0000000100b0cb08 chromedriver + 4213512\n", "13  chromedriver                        0x0000000100b0cc84 chromedriver + 4213892\n", "14  chromedriver                        0x0000000100b1aa08 chromedriver + 4270600\n", "15  libsystem_pthread.dylib             0x0000000185db206c _pthread_start + 320\n", "16  libsystem_pthread.dylib             0x0000000185dacda0 thread_start + 8\n", "\n", "Error processing TRƯỜNG ĐẠI HỌC ĐIỆN LỰC: Message: \n", "Stacktrace:\n", "0   chromedriver                        0x0000000100b224c8 chromedriver + 4302024\n", "1   chromedriver                        0x0000000100b1ae10 chromedriver + 4271632\n", "2   chromedriver                        0x000000010074c19c chromedriver + 278940\n", "3   chromedriver                        0x000000010078e2c4 chromedriver + 549572\n", "4   chromedriver                        0x00000001007c6c5c chromedriver + 781404\n", "5   chromedriver                        0x0000000100783004 chromedriver + 503812\n", "6   chromedriver                        0x00000001007839ec chromedriver + 506348\n", "7   chromedriver                        0x0000000100aea510 chromedriver + 4072720\n", "8   chromedriver                        0x0000000100aeefbc chromedriver + 4091836\n", "9   chromedriver                        0x0000000100ad1754 chromedriver + 3970900\n", "10  chromedriver                        0x0000000100aef8a4 chromedriver + 4094116\n", "11  chromedriver                        0x0000000100ac46d4 chromedriver + 3917524\n", "12  chromedriver                        0x0000000100b0cb08 chromedriver + 4213512\n", "13  chromedriver                        0x0000000100b0cc84 chromedriver + 4213892\n", "14  chromedriver                        0x0000000100b1aa08 chromedriver + 4270600\n", "15  libsystem_pthread.dylib             0x0000000185db206c _pthread_start + 320\n", "16  libsystem_pthread.dylib             0x0000000185dacda0 thread_start + 8\n", "\n"]}], "source": ["import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.common.keys import Keys\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.by import By\n", "import time\n", "\n", "df = pd.read_excel('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/GIZ/Data-Clean.xlsx',sheet_name='University')\n", "\n", "# Initialize WebDriver\n", "driver = webdriver.Chrome()\n", "wait = WebDriverWait(driver, 10)\n", "\n", "# DataFrame to save the URLs\n", "df_urls = pd.DataFrame(columns=['School Name', 'Result 1 URL', 'Result 2 URL'])\n", "\n", "try:\n", "    for index, row in df.iterrows():\n", "        school_name = row['Name']\n", "        try:\n", "            # Open Google and search for the school name\n", "            driver.get('https://www.google.com')\n", "            search_box = wait.until(EC.presence_of_element_located((By.XPATH, \"//textarea[@name='q']\")))\n", "            search_box.send_keys(school_name)\n", "            search_box.send_keys(Keys.RETURN)\n", "            sleep(0.5)\n", "\n", "            # Wait for search results to load\n", "            wait.until(EC.presence_of_element_located((By.XPATH, \"//div[@class='MjjYud']\")))\n", "\n", "            # Extract the URLs from the top 2 search results\n", "            urls = []\n", "            search_results = driver.find_elements(By.XPATH, \"//div[@class='MjjYud']//cite[@class='tjvcx GvPZzd cHaqb']\")\n", "            for result in search_results[:2]:  # Get the top 2 results\n", "                try:\n", "                    url_text = result.text\n", "                    urls.append(url_text)\n", "                except Exception as e:\n", "                    print(f\"Error processing result for {school_name}: {e}\")\n", "                    urls.append(None)\n", "            \n", "            # Ensure we always have 2 URLs (None if not found)\n", "            while len(urls) < 2:\n", "                urls.append(None)\n", "\n", "            # Add the data to the DataFrame\n", "            df_urls = pd.concat([df_urls, pd.DataFrame([{'School Name': school_name, 'Result 1 URL': urls[0], 'Result 2 URL': urls[1]}])], ignore_index=True)\n", "\n", "        except Exception as e:\n", "            print(f\"Error processing {school_name}: {e}\")\n", "            continue\n", "\n", "finally:\n", "    # Save the DataFrame to a CSV file\n", "    df_urls.to_csv('school_urls.csv', index=False)\n", "    driver.quit()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>School Name</th>\n", "      <th>Result 1 URL</th>\n", "      <th>Result 2 URL</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ĐẠI HỌC ĐÀ NẴNG</td>\n", "      <td>https://www.udn.vn</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ĐẠI HỌC HUẾ</td>\n", "      <td>https://hueuni.edu.vn</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ĐẠI HỌC QUỐC GIA TP. HỒ CHÍ MINH</td>\n", "      <td>https://vnuhcm.edu.vn</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ĐẠI HỌC THÁI NGUYÊN</td>\n", "      <td>https://www.tnu.edu.vn</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ĐẠI HỌC Y DƯỢC TP. HỒ CHÍ MINH</td>\n", "      <td>https://ump.edu.vn</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        School Name            Result 1 URL Result 2 URL\n", "0                   ĐẠI HỌC ĐÀ NẴNG      https://www.udn.vn             \n", "1                       ĐẠI HỌC HUẾ   https://hueuni.edu.vn             \n", "2  ĐẠI HỌC QUỐC GIA TP. HỒ CHÍ MINH   https://vnuhcm.edu.vn             \n", "3               ĐẠI HỌC THÁI NGUYÊN  https://www.tnu.edu.vn             \n", "4    ĐẠI HỌC Y DƯỢC TP. HỒ CHÍ MINH      https://ump.edu.vn             "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df_urls.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}