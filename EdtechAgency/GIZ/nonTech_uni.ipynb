{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service as ChromeService\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.common.exceptions import TimeoutException, StaleElementReferenceException, NoSuchElementException, ElementClickInterceptedException\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "from time import sleep\n", "\n", "# Initialize the WebDriver with options\n", "chrome_options = Options()\n", "chrome_options.add_argument(\"--incognito\")\n", "# chrome_options.add_argument('--headless')\n", "\n", "driver = webdriver.Chrome(options=chrome_options)\n", "actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "\n", "url = \"https://zunia.vn/tra-cuu/diem-chuan-dai-hoc.html\"\n", "driver.get(url)\n", "wait = WebDriverWait(driver, 10)\n", "keywords = ['<PERSON><PERSON> tế quốc tế'] \n", "\n", "# List to store the data\n", "data = []\n", "\n", "# Function to scroll into view before clicking\n", "def scroll_into_view(element):\n", "    driver.execute_script(\"arguments[0].scrollIntoView();\", element)\n", "\n", "try:\n", "    while keywords:\n", "        major_elements = WebDriverWait(driver, 10).until(\n", "            EC.presence_of_all_elements_located((By.XPATH, \"//div[2][@class='col-12 col-md-6 h2a']//div[@class='school-list bg-white mt-3 p-3']//ul/li\"))\n", "        )\n", "        \n", "        matched_keywords = []\n", "        \n", "        for major in major_elements:\n", "            for keyword in keywords:\n", "                if keyword.lower() in major.text.lower():\n", "                    try:\n", "                        major_link = major.find_element(By.TAG_NAME, \"a\")\n", "                        scroll_into_view(major_link)\n", "                        sleep(1)\n", "                        major_link.click()\n", "                        sleep(1)\n", "                        \n", "                        unis = WebDriverWait(driver, 10).until(\n", "                            EC.presence_of_all_elements_located((By.XPATH, \"//div[@class='table-industry mt-2 d-none d-lg-block']//tr//td[3]//a\"))\n", "                        )\n", "                        list_unis = [uni.text for uni in unis]\n", "                        \n", "                        data.append({\"major\": keyword, \"list_of_universities\": list_unis})\n", "                        matched_keywords.append(keyword)\n", "                        \n", "                        driver.back()\n", "                        WebDriverWait(driver, 10).until(\n", "                            EC.presence_of_element_located((By.XPATH, \"//div[2][@class='col-12 col-md-6 h2a']//div[@class='school-list bg-white mt-3 p-3']//ul/li\"))\n", "                        )\n", "                        sleep(1)\n", "                        \n", "                        # Re-locate the major elements after navigating back\n", "                        major_elements = WebDriverWait(driver, 10).until(\n", "                            EC.presence_of_all_elements_located((By.XPATH, \"//div[2][@class='col-12 col-md-6 h2a']//div[@class='school-list bg-white mt-3 p-3']//ul/li\"))\n", "                        )\n", "                    except StaleElementReferenceException:\n", "                        print(\"Stale element reference encountered. Trying to find elements again...\")\n", "                        break\n", "                    except ElementClickInterceptedException:\n", "                        print(\"Element click intercepted. Trying to click again after a delay...\")\n", "                        sleep(2)\n", "                        major_link.click()\n", "        \n", "        # Remove matched keywords from the list\n", "        for keyword in matched_keywords:\n", "            keywords.remove(keyword)\n", "        \n", "except Exception as e:\n", "    print(f\"An error occurred: {e}\")\n", "finally:\n", "    driver.quit()\n", "\n", "# Create a DataFrame from the collected data\n", "df = pd.DataFrame(data)\n", "df.to_excel('NoTechMajor.xlsx')"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Expanded DataFrame:\n", "                   major                 university\n", "0    Quản tr<PERSON> <PERSON>h do<PERSON>h  <PERSON><PERSON><PERSON> học <PERSON> tế Hồng Bàng\n", "1    Quản trị <PERSON>h do<PERSON>h       <PERSON><PERSON><PERSON>\n", "2    Quản trị <PERSON>h do<PERSON>h   <PERSON><PERSON><PERSON><PERSON>\n", "3    Quản tr<PERSON> <PERSON>h do<PERSON>h            <PERSON><PERSON><PERSON>\n", "4    Quản tr<PERSON> <PERSON>h do<PERSON>h         <PERSON><PERSON><PERSON>\n", "..                   ...                        ...\n", "511              Kinh tế        Đại học Kinh tế HCM\n", "512              Kinh tế        Đại học Kinh tế HCM\n", "513              Kinh tế   Đạ<PERSON> học Kinh tế Luật HCM\n", "514              Kinh tế       Đại học Nông Lâm HCM\n", "515              Kinh tế   Đạ<PERSON> học Kinh tế Quốc dân\n", "\n", "[516 rows x 2 columns]\n"]}], "source": ["import pandas as pd\n", "\n", "df = pd.read_excel('/Users/<USER>/Downloads/NontechMajor-Uni.xlsx')\n", "expanded_data = []\n", "\n", "# Iterate through each row in the original DataFrame\n", "for index, row in df.iterrows():\n", "    major = row['major']\n", "    universities_str = row['list_of_universities']\n", "    \n", "    # Convert string representation of list into an actual list\n", "    universities = eval(universities_str)\n", "    \n", "    # Iterate through each university in the list and create new rows\n", "    for university in universities:\n", "        expanded_data.append({'major': major, 'university': university})\n", "\n", "# Create a new DataFrame from the expanded_data list\n", "expanded_df = pd.DataFrame(expanded_data)\n", "\n", "print(\"Expanded DataFrame:\")\n", "print(expanded_df)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["expanded_df.to_excel('/Users/<USER>/Downloads/uni_no_tech.xlsx')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}