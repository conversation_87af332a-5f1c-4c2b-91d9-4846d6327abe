{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Unique lowercase values in column 'Course's name':\n", "['công nghệ thông tin', 'k<PERSON> thuật không gian', 'khoa học dữ liệu', 'tài chính ngân hàng', 'k<PERSON> thuật hệ thống công nghiệp', 'quản trị kinh doanh', 'logistics và quản lý chuỗi cung ứng', 'khoa học máy tính', 'quản trị thương mại điện tử', 'quản trị doanh nghiệp', 'quản trị chuỗi cung ứng – logistics', 'công nghệ phần mềm', 'hệ thống thông tin', 'digital marketing', 'thương mại điện từ', 'an toàn thông tin', 'kinh doanh và quản lý', 'kinh tế học và khoa học dữ liệu', 'mạng máy tính và truyền thông dữ liệu', 'k<PERSON> thuật phần mềm', 'k<PERSON> thuật cơ điện tử', 'bussiness', 'information technology', 'applies science aviation', 'artifical intelligence', 'tr<PERSON> tuệ nhân tạo', 'kinh doanh quốc tế', 'toán ứng dụng', 'sư phạm công nghệ', 'robot và trí tuệ nhân tạo', 'kỹ thuật dữ liệu', 'ngân hàng', 'tài chính doanh nghiệp', 'hệ thống thông tin kế toán', 'tài chính-ngân hàng', 'công nghệ tài chính', 'tài chính – ngân hàng', 'kinh doanh thương mại', 'công nghê giáo dục', 'mạng máy tính & an ninh thông tin', 'hệ thống dữ liệu lớn', 'kinh doanh số', 'công nghêệ thông tin', 'kinh tế ', 'công nghệ thông tin ứng dụng', 'hệ thống thông tin quản lý', 'global finance and economic', 'kế toán tài chính ngân hàng', 'marketing số', 'khoa học máy tính và thông tin', 'phân tích dữ liệu kinh doanh', 'công nghệ tài chính và kinh doanh số', 'quản trị doanh nghiệp và công nghệ', 'quản trị và an ninh', 'kinh tế số', 'quản trị marketing', 'kinh tế quốc tế', 'khoa học dữ liệu và trí tuệ nhân tạo', 'phân tích tài chính', 'dđầu tư tài chính', 'tài chính', 'công nghệ giáo dục', 'tài chính - ngân hàng', 'phân tích kinh doanh', 'điện toán đám mây', 'tài chính kế toán', 'quản trị tài chính và đầu tư', 'quản lý công nghiệp', '\\t\\nan toàn thông tin', 'quản trị công nghệ giáo dục', 'cntt', 'quản trị dịch vụ du lịch và lữ hành', 'kỹ thuật robot và trí tuệ nhân tạo', 'hệ thống giao thông thông minh', 'quản lý xây dựng', 'truyền thông đa phương tiện', 'truyền thông doanh nghiệp', 'nghiên cứu phát triển', 'thương mại điện tử', 'ngành kinh doanh thương mại', 'cnkt máy tính', 'kinh tế phát triển - chuyên ngành phân tích dữ liệu kinh tế và chính sách', 'marketing', 'kinh doanh thương\\nmại', 'quản lý dự án', 'kinh tế và quản lý nguồn nhân lực', 'thống kê kinh tế', 'logistic', 'ngành khoa học dữ liệu', 'ngành công nghệ thông tin – truyền thông', 'quản lý thông tin', 'luật kinh doanh', 'luật thương mại quốc tế', 'luật thương mại quốc tế ', 'thiết kế đồ hoạ', 'thương mại quốc tế', 'kinh tế phát triển quốc têd', 'quản trị kinh doanh quốc tế', 'tài chính quốc tế', 'phân tích và đầu tư tài chính', 'ngân hàng số', 'trí tuệ nhân tạo và khoa học dữ liệu', 'vật liệu thông minh và trí tuệ nhân tạo', 'công nghệ thông tin ', 'an ninh mạng', 'công nghệ tài chính ngân hàng', 'hệ thống thông tin quản lý ', 'khoa học và kỹ thuật máy tính', 'kinh tế', 'quản lý doanh nghiệp', 'lập trình máy tính', 'công nghẹ thông tin', 'tin học ứng dụng', 'đồ hoạ đa phương tiện', 'quản trị mạng máy tính', 'marketing thương mại', 'quản trị doanh nghiệp vừa và nhỏ', 'xử lý dữ liệu', 'ứng dụng phần mềm', 'phát triển phần mềm', 'logistic ', 'quản trị cơ sở dữ liệu', 'đầu tư tài chính', 'bussines analysis', 'khóa đào tạo chuyển đổi số doanh nghiệp cho quản lý', 'quản lý an toàn thông tin', 'tài chính-ngân hàng-công nghệ tài chính', 'công nghệ và phát triển doanh nghiệp', 'quản trị an ninh ', 'kỹ thuật máy tính', 'công nghệ thông tin (đặc thù - hợp tác doanh nghiệp) chuyên ngành khoa học dữ liệu và trí tuệ nhân tạo', 'công nghệ thông tin (đặc thù - hợp tác doanh nghiệp)', 'robotics và trí tuệ nhân tạo', 'mạng máy tính và an toàn thông tin', 'khoa học máy tính, công nghệ thông tin, hệ thống thông tin', 'marketing kỹ thuật số, quản trị dịch vụ du lịch & lữ hành số, quản trị logistics & chuỗi cung ứng số, quản trị tài chính số', 'toán kinh tế', 'khoa học dữ liệu/phân tích dữ liệu', 'trí tuệ nhân tạo và thị giác máy tính', 'truyền thông số và thiết kế đa phương tiện', 'hệ thống thông tin kinh doanh', 'công nghệ marketing ', 'công nghệ và đổi mới sáng tạo', 'quản trị và phân tích dữ liệu', '\\nhệ thống thông tin kinh doanh và chuyển đổi số', 'khoa học dữ liệu trong kinh doanh', 'marketing truyền thông tích hợp', 'quản trị kinh doanh – marketing']\n"]}], "source": ["import pandas as pd \n", "\n", "def preprocess_column(df, column_name):\n", "    # Convert all values in the column to lowercase\n", "    df[column_name] = df[column_name].str.lower()\n", "    \n", "    # Remove duplicates and convert to list\n", "    unique_values = df[column_name].drop_duplicates().tolist()\n", "    \n", "    return unique_values\n", "\n", "df = pd.read_excel('/Users/<USER>/Downloads/Data-MC.xlsx',sheet_name='Universities')\n", "\n", "column_name = \"Course's name\"\n", "\n", "keywords = preprocess_column(df, column_name)\n", "\n", "print(f\"\\nUnique lowercase values in column '{column_name}':\")\n", "print(keywords)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON><PERSON> interrupted by user.\n", "<PERSON><PERSON> sent successfully!\n"]}], "source": ["import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.common.exceptions import TimeoutException, StaleElementReferenceException, NoSuchElementException, ElementClickInterceptedException\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from time import sleep\n", "import logging\n", "import smtplib\n", "from email.mime.multipart import MIMEMultipart\n", "from email.mime.text import MIMEText\n", "\n", "# Configure logging\n", "logging.basicConfig(filename='output_log.txt', level=logging.INFO,\n", "                    format='%(asctime)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')\n", "\n", "# Initialize the WebDriver with options\n", "chrome_options = Options()\n", "chrome_options.add_argument(\"--incognito\")\n", "chrome_options.add_argument('--headless')\n", "chrome_options.add_argument('--window-size=1920,1080')\n", "chrome_options.add_argument('--disable-gpu')\n", "chrome_options.add_argument('--disable-dev-shm-usage')\n", "chrome_options.add_argument('--no-sandbox')\n", "chrome_options.add_argument('user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3')\n", "driver = webdriver.Chrome(options=chrome_options)\n", "actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "\n", "url = \"https://zunia.vn/tra-cuu/diem-chuan-dai-hoc.html\"\n", "driver.get(url)\n", "wait = WebDriverWait(driver, 10)\n", "data = pd.DataFrame(columns=['Uni', 'Title', 'Majors', 'Links', 'EnrollNos', 'Fees', 'Contents']) \n", "\n", "\n", "def send_email(subject, body, to_email, from_email, app_password):\n", "    msg = MIMEMultipart()\n", "    msg['From'] = from_email\n", "    msg['To'] = to_email\n", "    msg['Subject'] = subject\n", "\n", "    msg.attach(MIMEText(body, 'plain'))\n", "\n", "    try:\n", "        server = smtplib.SMTP_SSL('smtp.gmail.com', 465)\n", "        server.login(from_email, app_password)\n", "        server.sendmail(from_email, to_email, msg.as_string())\n", "        server.close()\n", "        print(\"<PERSON><PERSON> sent successfully!\")\n", "    except Exception as e:\n", "        print(f\"Failed to send email. Error: {e}\")\n", "\n", "def close_overlays():\n", "    try:\n", "        close_button = driver.find_element(By.XPATH, \"//button[@class='close']\")\n", "        if close_button.is_displayed():\n", "            close_button.click()\n", "            print(\"Closed an overlay.\")\n", "    except NoSuchElementException:\n", "        pass  # No overlay to close\n", "\n", "def scrape_major_info(index):\n", "    try:\n", "        # Locate all the major elements\n", "        majors = driver.find_elements(By.XPATH, \"//div[@class='table-industry table-responsive mt-2 d-none d-lg-block']//tr//td[3]\")\n", "        matched_majors = []  # List to store matched majors\n", "        \n", "        for major in majors:\n", "            for keyword in keywords:\n", "                if keyword.lower() in major.text.lower():\n", "                    logging.info(f\"Found matching major: {major.text} for keyword: {keyword}\")\n", "                    matched_majors.append(major.text)\n", "                    major_link = major.find_element(By.TAG_NAME, \"a\")\n", "                    \n", "                    try:\n", "                        # Scroll into view and click the major link\n", "                        driver.execute_script(\"arguments[0].scrollIntoView(true);\", major_link)\n", "                        close_overlays()\n", "                        WebDriverWait(driver, 10).until(EC.element_to_be_clickable(major_link)).click()\n", "                    except ElementClickInterceptedException as e:\n", "                        logging.info(f\"ElementClickInterceptedException: {e}\")\n", "                        # Handle the interception, e.g., close any overlay or wait and retry\n", "                        sleep(2)\n", "                        close_overlays()\n", "                        driver.execute_script(\"arguments[0].scrollIntoView(true);\", major_link)\n", "                        driver.execute_script(\"arguments[0].click();\", major_link)\n", "                    \n", "                    sleep(2)\n", "                    \n", "                    # Handle the new window/tab\n", "                    window_handles = driver.window_handles\n", "                    new_window_handle = window_handles[-1]\n", "                    driver.switch_to.window(new_window_handle)\n", "                    \n", "                    # Perform scraping in the new window/tab\n", "                    links = driver.current_url\n", "                    enroll_nos = 'N/A'\n", "                    fees = 'N/A'\n", "                    contents = 'N/A'\n", "                    \n", "                    try:\n", "                        student_number = driver.find_element(By.XPATH, \"//div[@class='col-12 col-md-9']//div[@class='num']//span[@class='chitieu']\")\n", "                        enroll_nos = student_number.text\n", "                    except NoSuchElementException:\n", "                        pass\n", "                    \n", "                    try:\n", "                        fee = driver.find_element(By.XPATH, \"//div[@class='col-12 col-md-9']//div[@class='num']//span[@class='price']\")\n", "                        fees = fee.text\n", "                    except NoSuchElementException:\n", "                        pass\n", "                    \n", "                    try:\n", "                        content = driver.find_element(By.XPATH, \"//div[@class='intro']//div[@class='inf']\")\n", "                        contents = content.text\n", "                    except NoSuchElementException:\n", "                        pass\n", "                    \n", "                    data.at[index, 'Links'].append(links)\n", "                    data.at[index, 'EnrollNos'].append(enroll_nos)\n", "                    data.at[index, 'Fees'].append(fees)\n", "                    data.at[index, 'Contents'].append(contents)\n", "                    \n", "                    driver.close()  # Close the new window/tab\n", "                    driver.switch_to.window(window_handles[0])  # Switch back to the main window\n", "        \n", "        if matched_majors:\n", "            data.at[index, 'Majors'] = ', '.join(matched_majors)\n", "            return True\n", "        else:\n", "            return False\n", "        \n", "    except Exception as e:\n", "        print(f\"An error occurred while scraping major info: {e}\")\n", "    \n", "    return False\n", "\n", "try:\n", "    uni_elements = driver.find_elements(By.XPATH, \"//div[@class='row']//div[1][@class='col-12 col-md-6 h2a']//div[@class='school-list bg-white mt-3 p-3']//ul/li\")\n", "    logging.info(f\"Total universities found: {len(uni_elements)}\")\n", "    \n", "    for index in range(len(uni_elements)):\n", "        try:\n", "            # Re-locate all the university elements to avoid stale references\n", "            uni_elements = driver.find_elements(By.XPATH, \"//div[@class='row']//div[1][@class='col-12 col-md-6 h2a']//div[@class='school-list bg-white mt-3 p-3']//ul/li\")\n", "            if index >= len(uni_elements):\n", "                logging.info(f\"Index {index} is out of range after re-locating elements. Breaking loop.\")\n", "                break\n", "            \n", "            current_uni = uni_elements[index]\n", "            try:\n", "                uni_name = current_uni.find_element(By.TAG_NAME, \"a\")\n", "                # Check if the <a> tag has visible text content\n", "                if uni_name.text.strip():  # Strip to remove whitespace and check if non-empty\n", "                    data.at[index, 'Uni'] = uni_name.text\n", "                    logging.info(f\"Scraping data for university: {uni_name.text}\")\n", "                    uni_name.click()\n", "                    sleep(1)\n", "                else:\n", "                    logging.info(f\"Skipping university {index + 1} as it has no visible text in <a> tag.\")\n", "                    continue  # Skip this university and continue with the next one\n", "            \n", "                title_element = driver.find_element(By.TAG_NAME, 'h1')\n", "                data.at[index, 'Title'] = title_element.text\n", "            \n", "                # Initialize lists to store multiple majors, links, enroll numbers, fees, and contents\n", "                data.at[index, 'Majors'] = []\n", "                data.at[index, 'Links'] = []\n", "                data.at[index, 'EnrollNos'] = []\n", "                data.at[index, 'Fees'] = []\n", "                data.at[index, 'Contents'] = []\n", "            \n", "                # Scrape major info\n", "                major_found = scrape_major_info(index)\n", "                if not major_found:\n", "                    logging.info(\"No matching major found in the keyword list.\")\n", "            \n", "                driver.back()\n", "                WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.XPATH, \"//div[@class='row']//div[1][@class='col-12 col-md-6 h2a']//div[@class='school-list bg-white mt-3 p-3']//ul/li\")))\n", "                sleep(1)\n", "\n", "            except NoSuchElementException as e:\n", "                logging.info(f\"No <a> tag found for university: {current_uni.text}. Error: {e}\")\n", "            except ElementClickInterceptedException as e:\n", "                logging.info(f\"ElementClickInterceptedException: {e}\")\n", "                continue  # Skip this university and continue with the next one\n", "\n", "        except (TimeoutException, StaleElementReferenceException) as e:\n", "            logging.info(f\"Error occurred: {e}\")\n", "            driver.back()\n", "            WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.XPATH, \"//div[@class='row']//div[1][@class='col-12 col-md-6 h2a']//div[@class='school-list bg-white mt-3 p-3']//ul/li\")))\n", "            sleep(1)\n", "        except Exception as e:\n", "            logging.info(f\"An unexpected error occurred: {e}\")\n", "            break\n", "\n", "except KeyboardInterrupt:\n", "    print(\"<PERSON><PERSON><PERSON> interrupted by user.\")\n", "\n", "finally:\n", "    data.to_excel('/Users/<USER>/Downloads/Uni-UpdateInfo.xlsx', index=False)\n", "    # Send notification email\n", "    send_email(\n", "        subject=\"Scraping Completed\",\n", "        body=\"Your scraping task has finished.\",\n", "        to_email=\"<EMAIL>\",\n", "        from_email=\"<EMAIL>\",   # google app password to get the password\n", "        app_password=\"wriq rnxk sphe vrwt\"\n", "    )\n", "    logging.info(\"All rows have been processed and an email has been sent to your given mailbox.\")\n", "    driver.quit()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original DataFrame:\n", "                           Uni  \\\n", "0      ĐH Sư Ph<PERSON> Thuật HCM   \n", "1              Đại học Hoa Sen   \n", "2        <PERSON><PERSON> <PERSON><PERSON>   \n", "3    <PERSON><PERSON><PERSON> <PERSON> tế Hồng Bàng   \n", "4     <PERSON><PERSON><PERSON> <PERSON><PERSON>   \n", "..                         ...   \n", "111            <PERSON><PERSON><PERSON> <PERSON>ây Bắc   \n", "112   <PERSON><PERSON><PERSON> vi<PERSON><PERSON> nữ Việt Nam   \n", "113           <PERSON><PERSON><PERSON> <PERSON>   \n", "114            <PERSON><PERSON><PERSON> <PERSON><PERSON>   \n", "115        <PERSON><PERSON><PERSON> <PERSON>   \n", "\n", "                                                 Title  \\\n", "0    <PERSON><PERSON><PERSON><PERSON> chu<PERSON> trường <PERSON><PERSON><PERSON>huật TP.HCM   \n", "1                    <PERSON><PERSON><PERSON><PERSON> chuẩn trường <PERSON><PERSON><PERSON>   \n", "2              <PERSON><PERSON><PERSON><PERSON> chuẩn trườ<PERSON> <PERSON><PERSON><PERSON> học Tôn <PERSON>   \n", "3                  <PERSON><PERSON><PERSON><PERSON> chuẩn trường <PERSON><PERSON><PERSON> học <PERSON>   \n", "4           <PERSON><PERSON><PERSON><PERSON> chuẩn trường <PERSON><PERSON><PERSON> h<PERSON><PERSON>   \n", "..                                                 ...   \n", "111                  <PERSON><PERSON><PERSON><PERSON> chuẩn trường <PERSON><PERSON><PERSON>   \n", "112                <PERSON><PERSON><PERSON><PERSON> chu<PERSON>n <PERSON> viện Phụ nữ Việt Nam   \n", "113                 <PERSON><PERSON><PERSON><PERSON> chuẩn trường <PERSON><PERSON><PERSON> học <PERSON>   \n", "114                  <PERSON><PERSON><PERSON><PERSON> chuẩn trường <PERSON><PERSON><PERSON> học <PERSON>   \n", "115              <PERSON><PERSON><PERSON><PERSON> chuẩn trường <PERSON><PERSON><PERSON> họ<PERSON>   \n", "\n", "                                                Majors  \\\n", "0                                                   []   \n", "1    Quản trị kinh do<PERSON>h, Marketing, Digital Market...   \n", "2    Marketing, <PERSON><PERSON><PERSON> - <PERSON><PERSON>, <PERSON><PERSON><PERSON> ch<PERSON> - ...   \n", "3    <PERSON><PERSON><PERSON><PERSON> tr<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> mại điện tử, <PERSON><PERSON><PERSON> c...   \n", "4    <PERSON><PERSON><PERSON> nghệ thông tin, <PERSON><PERSON> thu<PERSON> p<PERSON><PERSON><PERSON> mề<PERSON>, <PERSON><PERSON><PERSON> m...   \n", "..                                                 ...   \n", "111  <PERSON><PERSON><PERSON><PERSON> tr<PERSON>, <PERSON><PERSON><PERSON> <PERSON>h<PERSON> thông tin, <PERSON><PERSON><PERSON><PERSON>...   \n", "112  <PERSON><PERSON><PERSON><PERSON> tr<PERSON> kinh <PERSON>, <PERSON><PERSON><PERSON> kinh tế, <PERSON><PERSON><PERSON> <PERSON>h<PERSON> t...   \n", "113  <PERSON><PERSON><PERSON> nghệ thông tin, <PERSON><PERSON><PERSON><PERSON> tr<PERSON> dịch vụ du lị<PERSON> ...   \n", "114  <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> - <PERSON><PERSON>, Tà...   \n", "115                                                 []   \n", "\n", "                                                 Links  \\\n", "0                                                   []   \n", "1    ['https://zunia.vn/hoasen/tuyen-sinh/quan-tri-...   \n", "2    ['https://zunia.vn/tdtu/tuyen-sinh/marketing-3...   \n", "3    ['https://zunia.vn/hiu/tuyen-sinh/quan-tri-kin...   \n", "4    ['https://zunia.vn/ntt/tuyen-sinh/cong-nghe-th...   \n", "..                                                 ...   \n", "111  ['https://zunia.vn/utb/tuyen-sinh/quan-tri-kin...   \n", "112  ['https://zunia.vn/hvpnvn/tuyen-sinh/quan-tri-...   \n", "113  ['https://zunia.vn/tvu/tuyen-sinh/cong-nghe-th...   \n", "114  ['https://zunia.vn/hdiu/tuyen-sinh/quan-tri-ki...   \n", "115  ['https://zunia.vn/tdmu/tuyen-sinh/quan-tri-ki...   \n", "\n", "                                             EnrollNos  \\\n", "0                                                   []   \n", "1    ['', '', '', '', '', '', '', '', '', '', '', '...   \n", "2             ['', '', '', '', '', '', '', '', '', '']   \n", "3    ['450', '110', '300', '300', '300', '350', '35...   \n", "4    ['', '', '', '', '', '', '', '', '', '', '', '...   \n", "..                                                 ...   \n", "111               ['30', '80', '30', '30', '30', '30']   \n", "112         ['190', '150', '140', '180', '210', '155']   \n", "113                                     ['200', '200']   \n", "114         ['200', '200', '200', '200', '200', '200']   \n", "115                       ['', '', '', '', '', '', '']   \n", "\n", "                                                  Fees  \\\n", "0                                                   []   \n", "1    ['75.000.000', '75.000.000', '75.000.000', '75...   \n", "2    ['24.600.000', '24.600.000', '24.600.000', '24...   \n", "3    ['55.000.000', '55.000.000', '55.000.000', '55...   \n", "4    ['30.000.000', '30.000.000', '30.000.000', '30...   \n", "..                                                 ...   \n", "111  ['12.800.000', '15.000.000', '12.800.000', '12...   \n", "112  ['12.000.000', '12.000.000', '12.000.000', '12...   \n", "113                       ['18.500.000', '18.500.000']   \n", "114  ['15.900.000', '15.900.000', '15.900.000', '15...   \n", "115  ['18.000.000', '18.000.000', '18.000.000', '18...   \n", "\n", "                                              Contents  \n", "0                                                   []  \n", "1    ['<PERSON><PERSON><PERSON><PERSON> trình đào tạo ngành <PERSON>ản trị kinh doa...  \n", "2    ['<PERSON><PERSON><PERSON><PERSON> trình học ngành Marketing (Mã ngành: ...  \n", "3    ['<PERSON><PERSON><PERSON> tế Hồng Bàng cung cấp chương tr...  \n", "4    ['<PERSON><PERSON><PERSON>ông nghệ thông tin (Mã ngành: 7480201...  \n", "..                                                 ...  \n", "111  ['Tr<PERSON><PERSON><PERSON><PERSON> họ<PERSON>â<PERSON> xây dựng chương trình...  \n", "112  ['<PERSON><PERSON><PERSON> tiêu chung của đào tạo cử nhân Quản trị ...  \n", "113  ['<PERSON><PERSON><PERSON>ông nghệ thông tin (Mã ngành: 7480201...  \n", "114  ['<PERSON><PERSON> viên ngành <PERSON> trị kinh do<PERSON>h (<PERSON><PERSON> ngàn...  \n", "115  ['<PERSON><PERSON><PERSON> tr<PERSON> (Mã ngành: 7340101...  \n", "\n", "[116 rows x 7 columns]\n", "\n", "Expanded DataFrame:\n", "                              Uni  \\\n", "0         ĐH Sư Ph<PERSON> Thuật HCM   \n", "1                 Đại học Hoa Sen   \n", "2                 <PERSON><PERSON><PERSON>   \n", "3                 <PERSON><PERSON><PERSON>   \n", "4                 <PERSON><PERSON><PERSON>   \n", "...                           ...   \n", "81801277      <PERSON><PERSON><PERSON>   \n", "81801278      <PERSON><PERSON><PERSON>   \n", "81801279      <PERSON><PERSON><PERSON>   \n", "81801280      <PERSON><PERSON><PERSON>   \n", "81801281      <PERSON><PERSON><PERSON>   \n", "\n", "                                                      Title  \\\n", "0         <PERSON><PERSON><PERSON><PERSON> chu<PERSON> trường <PERSON><PERSON><PERSON>huật TP.HCM   \n", "1                         <PERSON><PERSON><PERSON><PERSON> chuẩn trường <PERSON><PERSON><PERSON>   \n", "2                         <PERSON><PERSON><PERSON><PERSON> chu<PERSON>n trườ<PERSON> <PERSON><PERSON><PERSON>   \n", "3                         <PERSON><PERSON><PERSON><PERSON> chuẩn trường <PERSON><PERSON><PERSON>   \n", "4                         <PERSON><PERSON><PERSON><PERSON> chuẩn trường <PERSON><PERSON><PERSON>   \n", "...                                                     ...   \n", "81801277              <PERSON><PERSON><PERSON><PERSON> chuẩ<PERSON> trường <PERSON><PERSON><PERSON>ọ<PERSON>   \n", "81801278              <PERSON><PERSON><PERSON><PERSON> chu<PERSON> trườ<PERSON> <PERSON><PERSON><PERSON>ọ<PERSON>   \n", "81801279              <PERSON><PERSON><PERSON><PERSON> chu<PERSON> trườ<PERSON> <PERSON><PERSON><PERSON>ọ<PERSON>   \n", "81801280              <PERSON><PERSON><PERSON><PERSON> chuẩn trườ<PERSON> <PERSON><PERSON><PERSON> họ<PERSON>   \n", "81801281              <PERSON><PERSON><PERSON><PERSON> chuẩ<PERSON> trườ<PERSON> <PERSON><PERSON><PERSON>ọ<PERSON>   \n", "\n", "                       Majors  \\\n", "0                               \n", "1         Quản trị kinh doanh   \n", "2         Quản tr<PERSON> kinh doanh   \n", "3         Quản tr<PERSON> kinh doanh   \n", "4         Quản tr<PERSON> kinh doanh   \n", "...                       ...   \n", "81801277                        \n", "81801278                        \n", "81801279                        \n", "81801280                        \n", "81801281                        \n", "\n", "                                                      Links EnrollNos  \\\n", "0                                                        []             \n", "1         ['https://zunia.vn/hoasen/tuyen-sinh/quan-tri-...             \n", "2         ['https://zunia.vn/hoasen/tuyen-sinh/quan-tri-...             \n", "3         ['https://zunia.vn/hoasen/tuyen-sinh/quan-tri-...             \n", "4         ['https://zunia.vn/hoasen/tuyen-sinh/quan-tri-...             \n", "...                                                     ...       ...   \n", "81801277  ['https://zunia.vn/tdmu/tuyen-sinh/quan-tri-ki...             \n", "81801278  ['https://zunia.vn/tdmu/tuyen-sinh/quan-tri-ki...             \n", "81801279  ['https://zunia.vn/tdmu/tuyen-sinh/quan-tri-ki...             \n", "81801280  ['https://zunia.vn/tdmu/tuyen-sinh/quan-tri-ki...             \n", "81801281  ['https://zunia.vn/tdmu/tuyen-sinh/quan-tri-ki...             \n", "\n", "                Fees                                           Contents  \n", "0                                                                        \n", "1         75.000.000  Chương trình đào tạo ngành <PERSON>ản trị kinh doanh...  \n", "2         75.000.000  kỹ năng và lý thuyết quản trị hiện đại để quản...  \n", "3         75.000.000  có khả năng định hướng phát triển doanh nghiệp...  \n", "4         75.000.000  có đội ngũ giảng viên giàu kinh nghiệm giảng d...  \n", "...              ...                                                ...  \n", "81801277  18.000.000                nhân viên quản lý điều hành vận tải  \n", "81801278  18.000.000                         nhân viên quản lý hàng hóa  \n", "81801279  18.000.000                nhân viên kinh doanh xu<PERSON>t nh<PERSON>p kh<PERSON>u  \n", "81801280  18.000.000  cán bộ quản trị điều hành các phòng ban chức n...  \n", "81801281  18.000.000                                                  …  \n", "\n", "[81801282 rows x 7 columns]\n"]}], "source": ["df = pd.read_excel('/Users/<USER>/Downloads/Uni-UpdateInfo.xlsx')\n", "print(\"Original DataFrame:\")\n", "print(df)\n", "\n", "# Preprocess columns with string representations of lists\n", "def convert_to_list(s):\n", "    return s.strip('[]').replace(\"'\", \"\").split(', ')\n", "\n", "df['Majors'] = df['Majors'].apply(lambda x: x.strip('[]').replace(\"'\", \"\").split(', '))\n", "df['EnrollNos'] = df['EnrollNos'].apply(lambda x: x.strip('[]').replace(\"'\", \"\").split(', '))\n", "df['Fees'] = df['Fees'].apply(lambda x: x.strip('[]').replace(\"'\", \"\").split(', '))\n", "df['Contents'] = df['Contents'].apply(convert_to_list)\n", "\n", "# Use explode to expand lists into separate rows\n", "df_expanded = df.explode('Majors').explode('EnrollNos').explode('Fees').explode('Contents').reset_index(drop=True)\n", "\n", "print(\"\\nExpanded DataFrame:\")\n", "print(df_expanded)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "This sheet is too large! Your sheet size is: 81801282, 7 Max sheet size is: 1048576, 16384", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[11], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mdf_expanded\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mto_excel\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m/Users/<USER>/Downloads/Data-Uni.xlsx\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindex\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/pandas/util/_decorators.py:211\u001b[0m, in \u001b[0;36mdeprecate_kwarg.<locals>._deprecate_kwarg.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    209\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    210\u001b[0m         kwargs[new_arg_name] \u001b[38;5;241m=\u001b[39m new_arg_value\n\u001b[0;32m--> 211\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/pandas/util/_decorators.py:211\u001b[0m, in \u001b[0;36mdeprecate_kwarg.<locals>._deprecate_kwarg.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    209\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    210\u001b[0m         kwargs[new_arg_name] \u001b[38;5;241m=\u001b[39m new_arg_value\n\u001b[0;32m--> 211\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/pandas/core/generic.py:2374\u001b[0m, in \u001b[0;36mNDFrame.to_excel\u001b[0;34m(self, excel_writer, sheet_name, na_rep, float_format, columns, header, index, index_label, startrow, startcol, engine, merge_cells, encoding, inf_rep, verbose, freeze_panes, storage_options)\u001b[0m\n\u001b[1;32m   2361\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mio\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mformats\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mexcel\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ExcelFormatter\n\u001b[1;32m   2363\u001b[0m formatter \u001b[38;5;241m=\u001b[39m ExcelFormatter(\n\u001b[1;32m   2364\u001b[0m     df,\n\u001b[1;32m   2365\u001b[0m     na_rep\u001b[38;5;241m=\u001b[39mna_rep,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   2372\u001b[0m     inf_rep\u001b[38;5;241m=\u001b[39minf_rep,\n\u001b[1;32m   2373\u001b[0m )\n\u001b[0;32m-> 2374\u001b[0m \u001b[43mformatter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwrite\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   2375\u001b[0m \u001b[43m    \u001b[49m\u001b[43mexcel_writer\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2376\u001b[0m \u001b[43m    \u001b[49m\u001b[43msheet_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msheet_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2377\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstartrow\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstartrow\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2378\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstartcol\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstartcol\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2379\u001b[0m \u001b[43m    \u001b[49m\u001b[43mfreeze_panes\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfreeze_panes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2380\u001b[0m \u001b[43m    \u001b[49m\u001b[43mengine\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mengine\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2381\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2382\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/pandas/io/formats/excel.py:933\u001b[0m, in \u001b[0;36mExcelFormatter.write\u001b[0;34m(self, writer, sheet_name, startrow, startcol, freeze_panes, engine, storage_options)\u001b[0m\n\u001b[1;32m    931\u001b[0m num_rows, num_cols \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdf\u001b[38;5;241m.\u001b[39mshape\n\u001b[1;32m    932\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m num_rows \u001b[38;5;241m>\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmax_rows \u001b[38;5;129;01mor\u001b[39;00m num_cols \u001b[38;5;241m>\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmax_cols:\n\u001b[0;32m--> 933\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m    934\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mThis sheet is too large! Your sheet size is: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnum_rows\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnum_cols\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    935\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMax sheet size is: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmax_rows\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmax_cols\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    936\u001b[0m     )\n\u001b[1;32m    938\u001b[0m formatted_cells \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_formatted_cells()\n\u001b[1;32m    939\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(writer, ExcelWriter):\n", "\u001b[0;31mValueError\u001b[0m: This sheet is too large! Your sheet size is: 81801282, 7 Max sheet size is: 1048576, 16384"]}], "source": ["# df_expanded.to_excel('/Users/<USER>/Downloads/Data-Uni.xlsx', index=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}