# import openpyxl
# from difflib import get_close_matches

# file_path = '/Users/<USER>/Downloads/test.xlsx'
# output_sheet_name = 'Summary opinion'
# start_row = 3  # Starting at row 
# start_column = 3  # Starting at column 

# # List of specific criteria counts for each general criteria
# criteria_counts_previous = [5, 2, 3, 2, 2, 2, 1, 1, 1, 1]
# criteria_counts_later = [5, 1, 3, 2, 2, 2, 1, 1, 1, 1]

# # List of old sheet names
# old_sheet_names = ['ĐH-BKHN', 'DN-Agilearn', 'DN-Codegym', 'DN-Cole', 'DN-Funix', 'DN-Gitiho', 'DN-Onschool', 'ĐH-QGHN']  

# # Load the workbook
# workbook = openpyxl.load_workbook(file_path)

# # Ensure the 'Summary' sheet exists
# if output_sheet_name not in workbook.sheetnames:
#     workbook.create_sheet(output_sheet_name)
# summary_sheet = workbook[output_sheet_name]

# # Function to find the best matching row in summary sheet
# def find_matching_row(sheet, search_value, search_range):
#     search_values = [(row, sheet.cell(row=row, column=1).value) for row in range(search_range[0], search_range[1] + 1)]
#     filtered_search_values = [value for row, value in search_values if value is not None]
#     matches = get_close_matches(search_value, filtered_search_values, n=1, cutoff=0.8)
#     if matches:
#         for row, value in search_values:
#             if value == matches[0]:
#                 return row
#     return None

# # Iterate through each sheet in the workbook
# for sheet_name in workbook.sheetnames:
#     if sheet_name != output_sheet_name:
#         sheet = workbook[sheet_name]

#         # Determine which criteria list to use
#         if sheet_name in old_sheet_names:
#             criteria_counts = criteria_counts_previous
#         else:
#             criteria_counts = criteria_counts_later

#         # Find the 'Câu trả lời' column
#         yes_no_column = None
#         header_row = 24  # Assuming the header is in row 
#         for col in range(1, sheet.max_column + 1):
#             header_value = sheet.cell(row=header_row, column=col).value
#             if header_value == 'Câu trả lời':
#                 yes_no_column = col
#                 break

#         if yes_no_column is None:
#             print(f"Error: 'Câu trả lời' column not found in sheet {sheet_name}.")
#             continue
#         else:
#             print(f"'Câu trả lời' column found at index {yes_no_column} in sheet {sheet_name}")

#         # Initialize the variables
#         text_values_list = []

#         # Start processing based on criteria_counts
#         row_index = 25  # Starting row for data (after header)
#         for count in criteria_counts:
#             cell_text = []
#             for _ in range(count):
#                 value = sheet.cell(row=row_index, column=yes_no_column).value
#                 if value is not None:
#                     cell_text.append(str(value))  # Collect text values
#                 row_index += 1
#             text_values_list.append("\n".join(cell_text))  # Join texts with newline separator

#         # Write the text to the matching rows in the 'Summary opinion' sheet
#         match_row = find_matching_row(summary_sheet, sheet_name, (2, 23))
#         if match_row:
#             col_idx = start_column
#             for text in text_values_list:
#                 summary_sheet.cell(row=match_row, column=col_idx, value=text)
#                 col_idx += 1

# # Save the workbook
# workbook.save(file_path)

# print(f"Text has been copied to the '{output_sheet_name}' sheet successfully.")



import openpyxl
from difflib import get_close_matches

file_path = '/Users/<USER>/Downloads/test.xlsx'
output_sheet_name = 'Summary opinion'
start_row = 3  
start_column = 13  

# Load the workbook
workbook = openpyxl.load_workbook(file_path)

# Ensure the 'Summary opinion' sheet exists
if output_sheet_name not in workbook.sheetnames:
    workbook.create_sheet(output_sheet_name)
summary_sheet = workbook[output_sheet_name]

# Function to find the best matching row in the summary sheet
def find_matching_row(sheet, search_value, search_range):
    search_values = [sheet.cell(row=row, column=1).value for row in range(search_range[0], search_range[1] + 1)]
    matches = get_close_matches(search_value, search_values, n=1, cutoff=0.8)
    if matches:
        for row in range(search_range[0], search_range[1] + 1):
            if sheet.cell(row=row, column=1).value == matches[0]:
                return row
    return None

# Function to find the column for a given header
def find_header_column(sheet, header_name):
    for col in range(1, sheet.max_column + 1):
        for row in range(1, sheet.max_row + 1):
            if sheet.cell(row=row, column=col).value == header_name:
                return row, col
    return None, None

# Iterate through each sheet in the workbook
for sheet_name in workbook.sheetnames:
    if sheet_name != output_sheet_name:
        sheet = workbook[sheet_name]

        # Find the row and column of 'Ans' and 'Câu trả lời'
        ans_row, ans_column = find_header_column(sheet, 'Ans')
        cau_tra_loi_row, cau_tra_loi_column = find_header_column(sheet, 'Câu trả lời')

        if ans_row is None or ans_column is None or cau_tra_loi_row is None or cau_tra_loi_column is None:
            print(f"Error: 'Ans' or 'Câu trả lời' headers not found in sheet {sheet_name}.")
            continue
        else:
            print(f"'Ans' header found at row {ans_row}, column {ans_column}.")
            print(f"'Câu trả lời' header found at row {cau_tra_loi_row}, column {cau_tra_loi_column}.")

        # Ensure 'Ans' and 'Câu trả lời' are in the same column and 'Câu trả lời' is below 'Ans'
        if ans_column == cau_tra_loi_column and cau_tra_loi_row > ans_row:
            # Collect text values between 'Ans' and 'Câu trả lời' vertically
            merged_text = []
            for row in range(ans_row + 1, cau_tra_loi_row):
                value = sheet.cell(row=row, column=ans_column).value
                if value:
                    merged_text.append(str(value))

            # Join the merged text with a newline separator
            final_text = "\n".join(merged_text)
            print(f"Merged text for sheet {sheet_name}: {final_text}")

            # Write the merged text to the matching row in the 'Summary opinion' sheet
            match_row = find_matching_row(summary_sheet, sheet_name, (2, 23))
            if match_row:
                summary_sheet.cell(row=match_row, column=start_column, value=final_text)
                print(f"Text written to row {match_row} in 'Summary opinion'.")
            else:
                print(f"No matching row found in 'Summary opinion' for sheet {sheet_name}.")
        else:
            print(f"'Ans' and 'Câu trả lời' headers are not in the expected positions in sheet {sheet_name}.")

# Save the workbook
workbook.save(file_path)

print(f"Merged text has been copied to the '{output_sheet_name}' sheet successfully.")