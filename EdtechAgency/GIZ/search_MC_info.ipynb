{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.common.exceptions import TimeoutException, StaleElementReferenceException\n", "import time\n", "from time import sleep\n", "\n", "# Load the Excel file into a DataFrame\n", "df = pd.read_excel('/Users/<USER>/Downloads/Data-Clean.xlsx',sheet_name='Sheet1')\n", "\n", "# Initialize the WebDriver (this example uses Chrome)\n", "driver = webdriver.Chrome()\n", "\n", "# Function to perform the search and scrape the required information\n", "def search_and_scrape(query):\n", "    while True:\n", "        try:\n", "            # Open the search engine page\n", "            driver.get(\"https://www.google.com\")\n", "\n", "            # Locate the search box, enter the query, and submit\n", "            search_box = driver.find_element(By.NAME, \"q\")\n", "            search_box.send_keys(query)\n", "            search_box.submit()\n", "\n", "            try:\n", "                # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.XPATH, \"YOUR_XPATH_HERE\")))\n", "                sleep(1)\n", "                try:\n", "                    scraped_text = driver.find_element(By.XPATH, \"//div[@class='yp1CPe wDYxhc NFQFxe viOShc LKPcQc']//div//div//div//span//span[@class='hgKElc']\").text\n", "                    return scraped_text\n", "                except Exception:\n", "                    print('No info found')\n", "                    return None\n", "            except TimeoutException:\n", "                print(f\"Timeout occurred for query: {query}\")\n", "                return None\n", "        except StaleElementReferenceException:\n", "            print(f\"StaleElementReferenceException for query: {query}. Retrying...\")\n", "            sleep(1)\n", "            continue\n", "\n", "# List to store the scraped results\n", "results = []\n", "\n", "# Loop through each row in the DataFrame\n", "for index, row in df.iterrows():\n", "    # Form the search query\n", "    search_query = f\"{row[0]} {row[1]} số lượng tuyển sinh\"\n", "    print(f\"Searching for: {search_query}\")\n", "\n", "    # Perform the search and scrape the information\n", "    result = search_and_scrape(search_query)\n", "    results.append(result)\n", "\n", "    # Wait for a second before the next iteration\n", "    time.sleep(1)\n", "\n", "# Add the scraped results to the DataFrame\n", "df['Scraped_Result'] = results\n", "\n", "# Save the DataFrame to a new Excel file\n", "df.to_excel('/Users/<USER>/Downloads/Data-Clean.xlsx', sheet_name='Sheet2', index=False)\n", "\n", "# Quit the WebDriver\n", "driver.quit()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}