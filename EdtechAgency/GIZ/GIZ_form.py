# # WRITE THE SHEET NAME
# import openpyxl
# import pandas as pd

# file_path = '/Users/<USER>/Downloads/check-point.xlsx'
# sheet_names = ['Cole_AI ứng dụng', 'Cole_Data Analyst', 'Cole_<PERSON><PERSON>n đổi số', 'Codegym_Nhập môn <PERSON>', 'Codegym_Data analysis Fundament', 
#                'Onetouch_khung kỹ năng số', 'Unica_Facebook Marketing', 'Unica_Phân tích dữ liệu Python', 'Free_ERX_AI', 'Free_ERX_AI2',
#                'Free_Onlinica_AI', 'Free_Onlinica_AI2', 'Free_Onlinica_DE1', 'Free_Onlinica_DE2', 'Free_Gitiho_DA', 'Paid_Gitiho_DA_Python',
#                'Paid_Gitiho_DA_PowerBI', 'Paid_Gitiho_DE', 'Free_Gitiho_DE', 'Free_BrandCamp_DE', 'Free_OneTouch_DE', 'Free_OneTouch_DA', 'Free_AimAcademy_DE',
#                'INDA_Data Engineer', '<PERSON>iz <PERSON>_Phân tích dữ liệu', '<PERSON><PERSON> tài họ<PERSON>_<PERSON>', 'ElearningPTIT_AI', 'ProtonX_AI', 'ProtonX_DA']  
# output_sheet_name = 'Summary'  
# start_row = 5
# start_column = 1

# # Load the workbook and get the output sheet
# workbook = openpyxl.load_workbook(file_path)
# if output_sheet_name not in workbook.sheetnames:
#     workbook.create_sheet(output_sheet_name)
# output_sheet = workbook[output_sheet_name]

# # Write the names of specified sheets into the specified position in the output sheet
# for index, sheet_name in enumerate(sheet_names):
#     output_sheet.cell(row=start_row + index, column=start_column, value=sheet_name)

# # Save the workbook
# workbook.save(file_path)

# print(f"Sheet names copied to '{output_sheet_name}' starting at row {start_row}, column {start_column} successfully.")

# COUNT THE VALUES FOR EACH GENERAL CRITERION 
import openpyxl
from difflib import get_close_matches

file_path = '/Users/<USER>/Downloads/check-point.xlsx'
output_sheet_name = 'Summary'
start_row = 6  # Starting at row 6
start_column = 2  # Starting at column B

# List of specific criteria counts for each general criteria
criteria_counts = [5, 5, 5, 5, 5, 5, 4, 5, 5, 5, 5, 1, 5, 5, 5, 4, 5, 5, 5, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4, 1, 1, 5, 5, 5, 5, 5, 5, 5]

# Load the workbook
workbook = openpyxl.load_workbook(file_path)

# Ensure the 'Summary' sheet exists
if output_sheet_name not in workbook.sheetnames:
    workbook.create_sheet(output_sheet_name)
summary_sheet = workbook[output_sheet_name]

# Function to find the best matching row in summary sheet
def find_matching_row(sheet, search_value, search_range):
    search_values = [sheet.cell(row=row, column=1).value for row in range(search_range[0], search_range[1] + 1)]
    matches = get_close_matches(search_value, search_values, n=1, cutoff=0.6)
    if matches:
        for row in range(search_range[0], search_range[1] + 1):
            if sheet.cell(row=row, column=1).value == matches[0]:
                return row
    return None

# Iterate through each sheet in the workbook
for sheet_name in workbook.sheetnames:
    if sheet_name != output_sheet_name:
        sheet = workbook[sheet_name]

        # Find the 'Yes/No' column
        yes_no_column = None
        header_row = 8  # Assuming the header is in row 8
        for col in range(1, sheet.max_column + 1):
            header_value = sheet.cell(row=header_row, column=col).value
            if header_value == 'Yes/No':
                yes_no_column = col
                break

        if yes_no_column is None:
            print(f"Error: 'Yes/No' column not found in sheet {sheet_name}.")
            continue
        else:
            print(f"'Yes/No' column found at index {yes_no_column} in sheet {sheet_name}")

        # Initialize the variables
        sum_values_list = []

        # Start processing based on criteria_counts
        row_index = 9  # Starting row for data (after header)
        for count in criteria_counts:
            sum_values = 0
            for _ in range(count):
                value = sheet.cell(row=row_index, column=yes_no_column).value
                if value is not None:
                    try:
                        value = int(value)
                        sum_values += value
                    except ValueError:
                        print(f"Non-integer value encountered: {value}")
                row_index += 1
            sum_values_list.append(sum_values)

        # Write the points to the matching rows in the 'Summary' sheet
        match_row = find_matching_row(summary_sheet, sheet_name, (5, 33))
        if match_row:
            col_idx = start_column
            for points in sum_values_list:
                summary_sheet.cell(row=match_row, column=col_idx, value=points)
                col_idx += 1

# Save the workbook
workbook.save(file_path)

print(f"Summary of criteria counts has been written to the '{output_sheet_name}' sheet successfully.")

