import re
import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import pandas as pd

# Set up logging
logging.basicConfig(filename='email_validation.log', level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Function to validate the structure of an email
def is_valid_email(email):
    # Regular expression for validating an email
    regex = r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
    return re.match(regex, email) is not None

# Function to check email deliverability using Selenium
def check_email_deliverability(driver, email):
    try:
        # Open the email checking website (if not already opened)
        driver.get("https://www.site24x7.com/tools/email-validator.html")  
        
        time.sleep(1)

        # Locate the email input field and input the email
        email_input = driver.find_element(By.ID, "emailInput")  
        email_input.clear()
        email_input.send_keys(email)
        
        # Click the 'Check' button
        check_button = driver.find_element(By.ID, "validateEmailBtn") 
        check_button.click()

        # Wait for the result to load (adjust time as needed)
        time.sleep(2)
        
        # Retrieve the result of the email check
        Syntax_Validation = driver.find_element(By.XPATH, "//tbody[@id='emailValidatorResponseList']//td[2]").text 
        DNS_Record_Validation = driver.find_element(By.XPATH, "//tbody[@id='emailValidatorResponseList']//td[3]").text 
        Deliverable = driver.find_element(By.XPATH, "//tbody[@id='emailValidatorResponseList']//td[4]").text 

        # Determine the validation result based on the conditions provided
        if Syntax_Validation == 'Valid' and Deliverable == 'Yes':
            return 'All valid'
        elif Syntax_Validation == 'Valid' and Deliverable == 'No':
            return 'Undeliverable Email'
        elif Syntax_Validation == 'Invalid' and Deliverable == 'No':
            return 'Invalid Email Format'
        else:
            return 'Unknown validation result'
    except Exception as e:
        logging.error(f"Error while checking email: {email}, error: {e}")
        return 'Error during validation'

# Load your dataset
logging.info("Loading dataset...")
df = pd.read_excel('/Users/<USER>/Downloads/Database.xlsx', sheet_name='Business', header=1) 
df = df.dropna(subset=['ID'])
df = df[df['ID'].str.startswith('EDVN')]

# Create a new column for the validation result
df['Validation_Result'] = ''

# Set up Selenium driver once
chrome_options = Options()
chrome_options.add_argument("--headless")
chrome_options.add_argument("--incognito")
driver = webdriver.Chrome(options=chrome_options)

try:
    logging.info("Starting email validation process...")
    # Process each email
    for index, email in df['Email'].items():
        if isinstance(email, str):  # Check if the email is a string
            logging.info(f"Processing email {index + 1}/{len(df)}: {email}")
            if is_valid_email(email):
                result = check_email_deliverability(driver, email)
                df.at[index, 'Validation_Result'] = result
                logging.info(f"Email: {email} - Validation result: {result}")
            else:
                df.at[index, 'Validation_Result'] = 'Invalid Email Format'
                logging.warning(f"Email: {email} - Invalid format")
        else:
            df.at[index, 'Validation_Result'] = 'Invalid or missing email'
            logging.warning(f"Email at index {index + 1} is missing or not a string.")
finally:
    # Close the browser after all emails have been checked
    driver.quit()
    logging.info("Selenium driver closed.")

# Save the updated dataset with the validation results
df.to_excel('process_email_EDTECHVN.xlsx', index=False)
logging.info("Process complete. Results saved in 'process_email_EDTECHVN.xlsx'")