{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# from datetime import datetime\n", "# import pandas as pd\n", "# from selenium import webdriver\n", "# from selenium.webdriver.chrome.options import Options\n", "# from selenium.webdriver.common.by import By\n", "# from selenium.webdriver.common.keys import Keys\n", "# import time\n", "\n", "# # Function to crawl company address\n", "# def crawl_company_address(company_name):\n", "#     chrome_options = Options()\n", "#     chrome_options.add_argument(\"--incognito\")\n", "#     chrome_options.add_argument('--headless')\n", "#     driver = webdriver.Chrome(options=chrome_options)\n", "\n", "#     try:\n", "#         # Open Google Search\n", "#         driver.get(\"https://www.google.com\")\n", "#         search_box = driver.find_element(By.NAME, \"q\")\n", "#         search_box.send_keys(f\"{company_name} địa chỉ\")\n", "#         search_box.send_keys(Keys.RETURN)\n", "\n", "#         time.sleep(2)  # Wait for results to load\n", "\n", "#         # Extract search results\n", "#         results = []\n", "#         search_results = driver.find_elements(By.CSS_SELECTOR, 'div.g')\n", "#         for result in search_results[:1]:  # Get first result\n", "#             result_text = \"\"\n", "#             try:\n", "#                 # Try center right structure\n", "#                 result_text = result.find_element(By.XPATH, '//*[@id=\"kp-wp-tab-overview\"]/div[2]/div/div/div/div[1]/div/div/span[2]').text\n", "#                 # print(f\"{company_name}:{result_text}\")\n", "\n", "#             except Exception as e:\n", "#                 # print(f\"{company_name} - Center right structure error: {e}\")\n", "\n", "#                 # Try the top left structure 1\n", "#                 try:\n", "#                     result_text = result.find_element(By.XPATH, '//*[@id=\"rso\"]/div[1]/div/block-component/div/div[1]/div/div/div/div/div[1]/div/div/div[2]/div/div[1]/div/span/span').text\n", "#                     # print(f\"{company_name}:{result_text}\")\n", "\n", "#                 except Exception as e:\n", "#                     # print(f\"{company_name} - Top left structure 1 error: {e}\")\n", "\n", "#                     # Try the top left structure 2\n", "#                     try:\n", "#                         result_text = result.find_element(By.XPATH, '//*[@id=\"rso\"]/div[1]/div/block-component/div/div[1]/div/div/div/div/div[1]/div/div/div/div/div[1]/div/span[1]/span').text\n", "#                         # print(f\"{company_name}:{result_text}\")\n", "\n", "#                     except Exception as e:\n", "#                         # print(f\"{company_name} - Top left structure 2 error: {e}\")\n", "\n", "#                         # Try below blocks structure\n", "#                         try:\n", "#                             alt_title = result.find_element(By.XPATH, \".//h3\").text\n", "#                             alt_snippet = result.find_element(By.XPATH, \".//div[@class='VwiC3b yXK7lf lVm3ye r025kc hJNv6b Hdw6tb']\").text\n", "#                             result_text = f\"{alt_title}: {alt_snippet}\"\n", "#                             # print(f\"{company_name}:{result_text}\")\n", "\n", "#                         except Exception as e:\n", "#                             # print(f\"{company_name} - Alternative structure 1 error: {e}\")\n", "\n", "#                             # Try top cover structure\n", "#                             try:\n", "#                                 alt_snippet = result.find_element(By.XPATH, \"//div[@class='ULSxyf']//div[@class='sXLaOe']\").text\n", "#                                 result_text = f\"{alt_snippet}\"\n", "#                                 # print(f\"{company_name}:{result_text}\")\n", "\n", "#                             except Exception as e:\n", "#                                 # print(f\"{company_name} - Alternative structure 2 error: {e}\")\n", "#                                 continue\n", "            \n", "#             if result_text:\n", "#                 results.append(result_text)\n", "        \n", "#         if not results:\n", "#             return \"Unknown\"\n", "\n", "#         return \" | \".join(results)\n", "\n", "#     except Exception as e:\n", "#         # print(f\"Error while crawling company address for {company_name}: {e}\")\n", "#         return \"Error\"\n", "\n", "#     finally:\n", "#         driver.quit()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.common.keys import Keys\n", "import time\n", "import logging\n", "\n", "# Set up logging\n", "logging.basicConfig(\n", "    filename='/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/address_crawler.log', \n", "    level=logging.INFO,  # Log level ensures INFO and higher are logged\n", "    format='%(asctime)s - %(levelname)s - %(message)s',\n", "    force=True  # This ensures that this configuration is enforced even if logging was previously set\n", ")\n", "\n", "def log_event(message):\n", "    logging.info(message)\n", "\n", "def crawl_company_address(company_name):\n", "    chrome_options = Options()\n", "    chrome_options.add_argument(\"--incognito\")\n", "    chrome_options.add_argument('--headless')\n", "    driver = webdriver.Chrome(options=chrome_options)\n", "\n", "    try:\n", "        # Open Google Search\n", "        log_event(f\"Starting Google search for {company_name}\")\n", "        driver.get(\"https://www.google.com\")\n", "        search_box = driver.find_element(By.NAME, \"q\")\n", "        search_box.send_keys(f\"{company_name} địa chỉ\")\n", "        search_box.send_keys(Keys.RETURN)\n", "\n", "        time.sleep(2)  # Wait for results to load\n", "\n", "        # Extract search results\n", "        results = []\n", "        search_results = driver.find_elements(By.CSS_SELECTOR, 'div.g')\n", "        log_event(f\"Found {len(search_results)} search results for {company_name}\")\n", "\n", "        for result in search_results[:1]:  # Get first result\n", "            result_text = \"\"\n", "            try:\n", "                # Try center right structure\n", "                log_event(\"Attempting center right structure\")\n", "                result_text = WebDriverWait(driver, 10).until(\n", "                    EC.presence_of_element_located((By.XPATH, '//*[@id=\"kp-wp-tab-overview\"]/div[2]/div/div/div/div[1]/div/div/span[2]'))\n", "                ).text\n", "\n", "            except Exception as e:\n", "                log_event(f\"Center right structure error: {e}\")\n", "\n", "                # Try center right structure 2\n", "                try:\n", "                    log_event(\"Attempting center right structure 2\")\n", "                    result_text = WebDriverWait(driver, 10).until(\n", "                        EC.presence_of_element_located((By.XPATH, '//*[@id=\"kp-wp-tab-overview\"]/div[1]/div/div/div/div[1]/div/div/span[2]'))\n", "                    ).text\n", "                except Exception as e:\n", "                    log_event(f\"Second XPath structure error: {e}\")\n", "\n", "                    # Try center right structure 3\n", "                    try:\n", "                        log_event(\"Attempting center right structure 3\")\n", "                        result_text = WebDriverWait(driver, 10).until(\n", "                            EC.presence_of_element_located((By.XPATH, '//*[@id=\"_yNsVZ7DtDM2n2roPhvP1qAI_93\"]/div/div/div[1]/div[1]/div/div/span[2]'))\n", "                        ).text\n", "                    except Exception as e:\n", "                        log_event(f\"Second XPath structure error: {e}\")\n", "\n", "                        # Try the top left structure 1\n", "                        try:\n", "                            log_event(\"Attempting top left structure 1\")\n", "                            result_text = WebDriverWait(driver, 10).until(\n", "                                EC.presence_of_element_located((By.XPATH, '//*[@id=\"rso\"]/div[1]/div/block-component/div/div[1]/div/div/div/div/div[1]/div/div/div[2]/div/div[1]/div/span/span'))\n", "                            ).text\n", "\n", "                        except Exception as e:\n", "                            log_event(f\"Top left structure 1 error: {e}\")\n", "\n", "                            # Try the top left structure 2\n", "                            try:\n", "                                log_event(\"Attempting top left structure 2\")\n", "                                result_text = WebDriverWait(driver, 10).until(\n", "                                    EC.presence_of_element_located((By.XPATH, '//*[@id=\"rso\"]/div[1]/div/block-component/div/div[1]/div/div/div/div/div[1]/div/div/div/div/div[1]/div/span[1]/span'))\n", "                                ).text\n", "\n", "                            except Exception as e:\n", "                                log_event(f\"Top left structure 2 error: {e}\")\n", "\n", "                                # Try below blocks structure\n", "                                try:\n", "                                    log_event(\"Attempting alternative block structure\")\n", "                                    alt_title = WebDriverWait(driver, 10).until(\n", "                                        EC.presence_of_element_located((By.XPATH, \".//h3\"))\n", "                                    ).text\n", "                                    alt_snippet = WebDriverWait(driver, 10).until(\n", "                                        EC.presence_of_element_located((By.XPATH, \".//div[@class='VwiC3b yXK7lf lVm3ye r025kc hJNv6b Hdw6tb']\"))\n", "                                    ).text\n", "                                    result_text = f\"{alt_title}: {alt_snippet}\"\n", "\n", "                                except Exception as e:\n", "                                    log_event(f\"Alternative structure 1 error: {e}\")\n", "\n", "                                    # Try top cover structure\n", "                                    try:\n", "                                        log_event(\"Attempting top cover structure\")\n", "                                        alt_snippet = WebDriverWait(driver, 10).until(\n", "                                            EC.presence_of_element_located((By.XPATH, \"//div[@class='ULSxyf']//div[@class='sXLaOe']\"))\n", "                                        ).text\n", "                                        result_text = f\"{alt_snippet}\"\n", "\n", "                                    except Exception as e:\n", "                                        log_event(f\"Alternative structure 2 error: {e}\")\n", "                                        continue\n", "\n", "            if result_text:\n", "                log_event(f\"Extracted text for {company_name}: {result_text}\")\n", "                results.append(result_text)\n", "\n", "        if not results:\n", "            log_event(f\"No results found for {company_name}\")\n", "            return \"Unknown\"\n", "\n", "        return \" | \".join(results)\n", "\n", "    except Exception as e:\n", "        log_event(f\"Error while crawling company address for {company_name}: {e}\")\n", "        return \"Error\"\n", "\n", "    finally:\n", "        log_event(f\"Closing browser for {company_name}\")\n", "        driver.quit()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["df = pd.read_excel('/Users/<USER>/Downloads/FLC.xlsx',sheet_name='Sheet3')\n", "# df = df[df['Tỉnh/TP'] == 'Unknown']\n", "# Apply the function to the 'Company-Name' column\n", "df['Tỉnh/TP'] = df['Tên trường/công ty'].apply(crawl_company_address)\n", "df.to_excel('/Users/<USER>/Downloads/FLC3.xlsx',sheet_name='Sheet1',index=False)\n", "# # Display the updated DataFrame\n", "# print(df.sample(30))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}