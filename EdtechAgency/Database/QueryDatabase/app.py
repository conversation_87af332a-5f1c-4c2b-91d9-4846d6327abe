# app.py
import streamlit as st
import toml
import hashlib
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime
from vnstock import Vnstock
stock = Vnstock().stock(symbol='VND', source='VCI')

def setup_secrets_file():
    """Setup the secrets.toml file if it doesn't exist"""
    secrets_dir = Path(".streamlit")
    secrets_file = secrets_dir / "secrets.toml"
    
    if not secrets_dir.exists():
        secrets_dir.mkdir()
    
    if not secrets_file.exists():
        # Create default secrets file with admin credentials
        secrets_data = {
            "passwords": {
                "admin": hashlib.sha256('admin123'.encode()).hexdigest()
            },
            "user_info": {
                "admin": "Admin User"
            }
        }
        
        with open(secrets_file, "w") as f:
            toml.dump(secrets_data, f)

def load_secrets():
    """Load secrets from the secrets.toml file"""
    secrets_file = Path(".streamlit/secrets.toml")
    if secrets_file.exists():
        return toml.load(secrets_file)
    return {"passwords": {}, "user_info": {}}

def save_secrets(secrets_data):
    """Save secrets to the secrets.toml file"""
    secrets_file = Path(".streamlit/secrets.toml")
    with open(secrets_file, "w") as f:
        toml.dump(secrets_data, f)

def register_user(username, password, display_name):
    """Register a new user"""
    # Load current secrets
    secrets_data = load_secrets()
    
    # Check if username already exists
    if username in secrets_data.get("passwords", {}):
        return False, "Username already exists"
    
    # Ensure password and user_info sections exist
    if "passwords" not in secrets_data:
        secrets_data["passwords"] = {}
    if "user_info" not in secrets_data:
        secrets_data["user_info"] = {}
    
    # Add new user
    secrets_data["passwords"][username] = hashlib.sha256(password.encode()).hexdigest()
    secrets_data["user_info"][username] = display_name
    
    # Save updated secrets
    save_secrets(secrets_data)
    
    return True, "Registration successful"

def verify_credentials(username, password):
    """Verify username and password"""
    # Load secrets directly from file to ensure we have latest data
    secrets_data = load_secrets()
    
    if username not in secrets_data.get("passwords", {}):
        return False, "Invalid username"
    
    stored_password = secrets_data["passwords"][username]
    hashed_password = hashlib.sha256(password.encode()).hexdigest()
    
    if stored_password == hashed_password:
        return True, secrets_data["user_info"].get(username, "User")
    else:
        return False, "Invalid password"

# Function to get stock data using the correct VNStock API
def get_stock_data(symbol):
    try:
        start_date = st.sidebar.date_input("Start date", datetime(2020, 1, 1))
        start_date = start_date.strftime('%Y-%m-%d')
        df = stock.quote.history(symbol=symbol, start_date=start_date, end_date=datetime.now())
       
        # Rename columns to match your expected format
        df = df.rename(columns={
            'time': 'Time',
            'open': 'Open',
            'high': 'High',
            'low': 'Low',
            'close': 'Close',
            'volume': 'Volume'
        })
        
        # Set index and convert to datetime
        df.set_index('Time', inplace=True)
        df.index = pd.to_datetime(df.index)
        
        # Convert to float
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        return df
    
    except Exception as e:
        st.error(f"Error fetching stock data: {e}")
        # Return empty DataFrame with expected columns
        return pd.DataFrame(columns=['Open', 'High', 'Low', 'Close', 'Volume'])

def strategy_loop(data, sl, tp):
    in_position = False
    buydates, selldates = [], []
    buyprices, sellprices = [], []

    for index, row in data.iterrows():
        if not in_position and row.get('Buy', False):
            buyprice = row.get('shifted_open', 0)
            buydates.append(index)
            buyprices.append(buyprice)
            in_position = True
        if in_position:
            if row.get('Low', 0) < buyprice * sl:
                sellprice = buyprice * sl
                sellprices.append(sellprice)
                selldates.append(index)
                in_position = False
            elif row.get('High', 0) > buyprice * tp:
                sellprice = buyprice * tp
                sellprices.append(sellprice)
                selldates.append(index)
                in_position = False
    
    # Return early if no trades were made
    if not sellprices or not buyprices:
        st.write("No trades executed with these parameters")
        return
    
    # Profit calculation/ trading fee
    profits = pd.Series([(sell-buy)/buy - 0.0015 for sell, buy in zip(sellprices, buyprices)])
    st.write(f"Total profit: {(profits + 1).prod():.4f}")

def analysis():
    
    try:
        # Get list of available stocks
        data = stock.listing.all_symbols()
        # Filter to only 3-character ticker symbols if needed
        if not data.empty:
            data = data[data['symbol'].str.len() == 3]
        
        if data.empty:
            st.error("No ticker symbols available. Please check your connection.")
            return
        
        # Let user select a stock
        ticker = st.sidebar.selectbox('Choose security:', data['symbol'])
        
        # df = get_stock_data(ticker)

        df = stock.quote.history(symbol='VND', start_date='2020-01-01', end_date='2025-04-29')
       
        # Rename columns to match your expected format
        df = df.rename(columns={
            'time': 'Time',
            'open': 'Open',
            'high': 'High',
            'low': 'Low',
            'close': 'Close',
            'volume': 'Volume'
        })
        
        # Set index and convert to datetime
        df.set_index('Time', inplace=True)
        df.index = pd.to_datetime(df.index)
        
        # Convert to float
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Display raw data
        st.subheader(f"Historical data for {ticker}")
        st.dataframe(df.head())
        
        # Calculate SMA crossover strategy
        df['SMA_50'] = df['Close'].rolling(50).mean()
        df['SMA_100'] = df['Close'].rolling(100).mean()
        df.dropna(inplace=True)
        
        # Create buy signals
        df['condition'] = (df['SMA_50'] > df['SMA_100'])
        df['Buy'] = (df['condition']) & (df['condition'].shift(1) == False)
        
        # Add shifted open for next day entry
        df['shifted_open'] = df['Open'].shift(-1)
        
        # Create backtest parameters
        sl_range = 1 - np.arange(0.01, 0.05, 0.01)
        tp_range = 1 + np.arange(0.01, 0.05, 0.01)
        
        # Run backtest
        st.subheader("Backtest Results")
        for sl in sl_range:
            for tp in tp_range:
                st.write(f'Stop loss: {sl:.2f} / Target profit: {tp:.2f}')
                strategy_loop(df, sl, tp)
    
    except Exception as e:
        st.error(f"An error occurred: {e}")

def filter():
    import gspread
    from google.oauth2.service_account import Credentials
    import pandas as pd
    SCOPES = ['https://www.googleapis.com/auth/spreadsheets']
    SERVICE_ACCOUNT_FILE = '/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/Datawarehouse/temp_service_account.json'
    credentials = Credentials.from_service_account_file(SERVICE_ACCOUNT_FILE, scopes=SCOPES)
    gc = gspread.authorize(credentials)
    spreadsheet_url = "https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE"
    spreadsheet = gc.open_by_url(spreadsheet_url)

    # Initialize session state for dataframes and tracking filter states
    if 'original_df' not in st.session_state:
        try:
            worksheet_input = spreadsheet.worksheet('All_Businesses')
            header_row_position = 2    
            all_values = worksheet_input.get_all_values()
            
            # Check if we have enough data
            if len(all_values) < header_row_position:
                st.error("Not enough data in the worksheet. Please check the sheet structure.")
                st.stop()
                
            header = all_values[header_row_position - 1]  
            data_rows = all_values[header_row_position:]  
            df = pd.DataFrame(data_rows, columns=header)
            
            def make_columns_unique(columns):
                seen = {}
                new_columns = []
                for col in columns:
                    if col in seen:
                        seen[col] += 1
                        new_columns.append(f"{col}.{seen[col]}")
                    else:
                        seen[col] = 0
                        new_columns.append(col)
                return new_columns

            df.columns = make_columns_unique(df.columns)
            st.session_state.original_df = df
        except Exception as e:
            st.error(f"Error loading initial data: {e}")
            st.stop()

    # Initialize other session state variables if they don't exist
    if 'filtered_df' not in st.session_state:
        st.session_state.filtered_df = st.session_state.original_df.copy()

    if 'pre_duplicate_removal_df' not in st.session_state:
        st.session_state.pre_duplicate_removal_df = None

    if 'pre_na_removal_df' not in st.session_state:
        st.session_state.pre_na_removal_df = None

    if 'pre_filter_df' not in st.session_state:
        st.session_state.pre_filter_df = None

    if 'selected_entity' not in st.session_state:
        st.session_state.selected_entity = None

    # Display the header and original data
    st.markdown("<h2 style='text-align: center; margin-bottom: 20px; background-image: linear-gradient(to right, #96d9a4, #c23640); color:#061c04;'>"
                    "All Data</h2>", unsafe_allow_html=True) 

    # Check if the original dataframe has data
    if st.session_state.original_df.empty:
        st.warning("No data available in the original dataset.")
    else:
        try:
            edited_df = st.data_editor(st.session_state.original_df, num_rows="dynamic")
        except Exception as e:
            st.error(f"Error displaying data editor: {e}")

    # Entity mapping
    mapping = {
        'K12':'K12',
        'Kindy':'KD',
        'HE':'HE',
        'VN Edtech': 'EDVN',
        'Edtech': 'EDQT',
        'Edtech Product': 'Edtech_Product',
        'Government': 'GOV',
        'Press': 'PRS',
        'Association': 'ASC',
        'Fund': 'FU',
        'Abroad Consultant': 'AC',
        'Skill Center': 'SC',
        'Foreign Language Center': 'FLC',
        'Department': 'EDE',
        'Office': 'EOF',
        'Others': 'OCP',
        'K12 Teachers': 'Teacher_K12'
    }

    # Add an option to reset all filters at the top level
    if st.button("🔄 Reset All Filters", key="reset_all"):
        st.session_state.filtered_df = st.session_state.original_df.copy()
        st.session_state.pre_duplicate_removal_df = None
        st.session_state.pre_na_removal_df = None
        st.session_state.pre_filter_df = None
        st.session_state.entity_select = None
        st.session_state.selected_entity = None
        st.success("All filters have been reset!")
        # Reset any filter-specific state variables
        for key in list(st.session_state.keys()):
            if key.startswith(('col_', 'val_', 'na_cols', 'na_option', 'entity_select')):
                st.session_state.pop(key, None)
        st.rerun()

    # Callback for entity selection change
    def on_entity_change():
        entity = st.session_state.entity_select
        value = mapping.get(entity)
        st.session_state.selected_entity = value
        
        # Save current dataframe state before changing
        st.session_state.pre_entity_filter_df = st.session_state.filtered_df.copy()
        
        # Handle normal filtering vs special sheets
        if value in ['Edtech_Product', 'Teacher_K12']:
            # Read different sheet
            try:
                worksheet_special = spreadsheet.worksheet(value)
                all_values_special = worksheet_special.get_all_values()
                
                if not all_values_special:
                    st.error(f"The {value} sheet appears to be empty.")
                    return
                    
                header_row_position = 1
                header_special = all_values_special[header_row_position - 1]
                data_rows_special = all_values_special[header_row_position:]
                
                # Make sure we have headers
                if not header_special:
                    st.error(f"No headers found in the {value} sheet.")
                    return
                    
                df_special = pd.DataFrame(data_rows_special, columns=header_special)
                
                # Make columns unique
                def make_columns_unique(columns):
                    seen = {}
                    new_columns = []
                    for col in columns:
                        if col in seen:
                            seen[col] += 1
                            new_columns.append(f"{col}.{seen[col]}")
                        else:
                            seen[col] = 0
                            new_columns.append(col)
                    return new_columns
                    
                df_special.columns = make_columns_unique(df_special.columns)
                st.session_state.filtered_df = df_special.copy()
            except Exception as e:
                st.error(f"Error loading special sheet: {e}")
        else:
            if value:
                # Check if 'ID' column exists before filtering
                if 'ID' in st.session_state.original_df.columns:
                    filtered = st.session_state.original_df[st.session_state.original_df['ID'].str.startswith(value)]
                    if filtered.empty:
                        st.warning(f"No data matches the filter for entity '{entity}' (prefix '{value}')")
                    st.session_state.filtered_df = filtered.copy()
                else:
                    st.error("ID column not found in the dataframe. Cannot filter by entity.")
            else:
                st.session_state.filtered_df = st.session_state.original_df.copy()

    # Entity selector
    entity_col1, entity_col2 = st.columns([3, 1])
    with entity_col1:
        entity = st.selectbox(
            'Select Entity',
            [None] + list(mapping.keys()),
            key='entity_select',
            on_change=on_entity_change
        )
    with entity_col2:
        if st.button("Reset Entity Filter", key="reset_entity"):
            st.session_state.filtered_df = st.session_state.original_df.copy()
            st.session_state.entity_select = None
            st.session_state.selected_entity = None
            st.success("Entity filter reset!")
            st.rerun()

    # Display the currently filtered dataframe
    try:
        if st.session_state.filtered_df.empty:
            st.warning("No data matches the current filters.")
        else:
            st.dataframe(st.session_state.filtered_df)
            
        # Filtering options header
        st.markdown("<h4 style='text-align: center; margin-bottom: 20px; background-image: linear-gradient(to right, #96d9a4, #c23640); color:#061c04;'>"
                    "Filtering Options</h4>", unsafe_allow_html=True) 
        
        # Step 1: Remove Duplicates
        st.subheader("1. Remove Duplicates")
        
        # Column selection for duplicates
        if st.session_state.filtered_df.empty:
            st.warning("No data available to check for duplicates.")
            dup_cols = []
        else:
            dup_cols = st.multiselect("Select columns to check for duplicates", st.session_state.filtered_df.columns)
        
        dup_col1, dup_col2 = st.columns([1, 1])
        
        with dup_col1:
            def remove_duplicates():
                if not dup_cols:
                    st.warning("Please select at least one column to check duplicates.")
                    return
                    
                if st.session_state.filtered_df.empty:
                    st.warning("No data to remove duplicates from.")
                    return
                    
                # Save current state before modification
                st.session_state.pre_duplicate_removal_df = st.session_state.filtered_df.copy()
                
                # Remove duplicates
                before_count = len(st.session_state.filtered_df)
                st.session_state.filtered_df = st.session_state.filtered_df.drop_duplicates(subset=dup_cols)
                after_count = len(st.session_state.filtered_df)
                removed = before_count - after_count
                
                st.success(f"Removed {removed} duplicate rows based on columns: {', '.join(dup_cols)}")
            
            if st.button("Remove Duplicates"):
                remove_duplicates()
        
        with dup_col2:
            if st.button("Reset Duplicate Removal", key="reset_dups"):
                if st.session_state.pre_duplicate_removal_df is not None:
                    st.session_state.filtered_df = st.session_state.pre_duplicate_removal_df.copy()
                    st.session_state.pre_duplicate_removal_df = None
                    st.success("Duplicate removal reset!")
                else:
                    st.info("No previous state to restore for duplicate removal.")

        # Display the current filtered dataframe
        if st.session_state.filtered_df.empty:
            st.warning("No data after removing duplicates.")
        else:
            st.dataframe(st.session_state.filtered_df)

        # 2. Remove Missing Data
        st.subheader("2. Remove Missing Data")
        
        remove_na_col1, remove_na_col2 = st.columns([3, 1])
        
        with remove_na_col1:
            remove_na_option = st.radio(
                "Remove missing data by:", 
                ["Whole rows", "Selected columns only"],
                key="na_option"
            )
        
        with remove_na_col2:
            if st.button("Reset Missing Data Removal", key="reset_na"):
                if st.session_state.pre_na_removal_df is not None:
                    st.session_state.filtered_df = st.session_state.pre_na_removal_df.copy()
                    st.session_state.pre_na_removal_df = None
                    st.success("Missing data removal reset!")
                else:
                    st.info("No previous state to restore for missing data removal.")
        
        if remove_na_option == "Whole rows":
            if st.button("Remove Rows with Any Missing Data"):
                if st.session_state.filtered_df.empty:
                    st.warning("No data to process.")
                else:
                    # Save current state before modification
                    st.session_state.pre_na_removal_df = st.session_state.filtered_df.copy()
                    
                    # Convert empty strings and whitespace to NaN
                    temp_df = st.session_state.filtered_df.copy()
                    temp_df = temp_df.replace(r'^\s*$', np.nan, regex=True)
                    
                    before_count = len(temp_df)
                    temp_df = temp_df.dropna()
                    after_count = len(temp_df)
                    removed = before_count - after_count
                    
                    st.session_state.filtered_df = temp_df
                    
                    if removed > 0:
                        st.success(f"Removed {removed} rows with missing values")
                    else:
                        st.info("No rows with missing values were found.")
        else:
            # Selected columns only
            if st.session_state.filtered_df.empty:
                st.warning("No data available to check for missing values.")
                na_cols = []
            else:
                na_cols = st.multiselect(
                    "Select columns to check for missing values", 
                    st.session_state.filtered_df.columns,
                    key="na_cols"
                )
            
            if st.button("Remove Rows with Missing Data in Selected Columns"):
                if not na_cols:
                    st.warning("Please select at least one column to check for missing values.")
                elif st.session_state.filtered_df.empty:
                    st.warning("No data to process.")
                else:
                    # Save current state before modification
                    st.session_state.pre_na_removal_df = st.session_state.filtered_df.copy()
                    
                    # Convert empty strings and whitespace to NaN in selected columns
                    temp_df = st.session_state.filtered_df.copy()
                    
                    # First replace empty strings with NaN
                    temp_df[na_cols] = temp_df[na_cols].replace(r'^\s*$', np.nan, regex=True)
                    
                    # Also handle whitespace-only strings
                    for col in na_cols:
                        temp_df[col] = temp_df[col].apply(
                            lambda x: np.nan if isinstance(x, str) and x.strip() == '' else x
                        )
                    
                    before_count = len(temp_df)
                    temp_df = temp_df.dropna(subset=na_cols)
                    after_count = len(temp_df)
                    removed = before_count - after_count
                    
                    st.session_state.filtered_df = temp_df
                    
                    if removed > 0:
                        st.success(f"Removed {removed} rows with missing values in columns: {', '.join(na_cols)}")
                    else:
                        st.info("No rows with missing values were found in the selected columns.")

        # Display current dataframe after missing data removal
        if st.session_state.filtered_df.empty:
            st.warning("No data after removing missing values.")
        else:
            st.dataframe(st.session_state.filtered_df)
        
        # Step 3: Filter specific values
        st.subheader("3. Filter Specific Values")
        
        filter_col1, filter_col2 = st.columns([3, 1])
        
        with filter_col1:
            filter_sections = st.number_input("How many filter conditions do you want to add?", 
                                            min_value=0, max_value=10, value=1, step=1)
        
        with filter_col2:
            if st.button("Reset Value Filters", key="reset_filters"):
                if st.session_state.pre_filter_df is not None:
                    st.session_state.filtered_df = st.session_state.pre_filter_df.copy()
                    st.session_state.pre_filter_df = None
                    st.success("Value filters reset!")
                    # Clear filter condition state
                    for i in range(10):  # Maximum possible filters
                        if f"col_{i}" in st.session_state:
                            del st.session_state[f"col_{i}"]
                        if f"val_{i}" in st.session_state:
                            del st.session_state[f"val_{i}"]
                else:
                    st.info("No previous state to restore for value filters.")
        
        def apply_filters():
            if st.session_state.filtered_df.empty:
                st.warning("No data to filter.")
                return
                
            # Save current state before applying filters
            st.session_state.pre_filter_df = st.session_state.filtered_df.copy()
            
            temp_df = st.session_state.filtered_df.copy()
            filters_applied = False
            
            for i in range(filter_sections):
                col_key = f"col_{i}"
                val_key = f"val_{i}"
                
                if col_key in st.session_state and val_key in st.session_state:
                    col_to_filter = st.session_state[col_key]
                    filter_values_input = st.session_state[val_key]
                    
                    if col_to_filter and filter_values_input:
                        allowed_values = [v.strip() for v in filter_values_input.split(",")]
                        
                        # Check if column exists
                        if col_to_filter not in temp_df.columns:
                            st.error(f"Column '{col_to_filter}' not found in the dataframe")
                            continue
                            
                        # Apply filter
                        before_count = len(temp_df)
                        temp_df = temp_df[temp_df[col_to_filter].isin(allowed_values)]
                        after_count = len(temp_df)
                        filters_applied = True
                        
                        # Check if filter removed all rows
                        if temp_df.empty:
                            st.warning(f"Filter on column '{col_to_filter}' with values {allowed_values} removed all rows.")
                            # Option to continue or restore
                            if st.button(f"Continue with empty result for filter {i+1}"):
                                pass
                            else:
                                return
            
            if filters_applied:
                st.session_state.filtered_df = temp_df
                st.success("Filters applied successfully!")
            else:
                st.info("No valid filters were applied.")
        
        for i in range(filter_sections):
            st.markdown(f"**Filter Condition {i+1}**")
            
            if st.session_state.filtered_df.empty:
                st.warning("No data available to filter.")
                continue
                
            st.selectbox(f"Select column for condition {i+1}", 
                        options=[None] + list(st.session_state.filtered_df.columns), 
                        key=f"col_{i}")
            st.text_input(f"Enter allowed value(s) (separated by commas)", key=f"val_{i}")
        
        if filter_sections > 0:
            if st.button("Apply Filters"):
                apply_filters()
        
        # Display current dataframe after specific value filtering
        if st.session_state.filtered_df.empty:
            st.warning("No data after applying filters.")
        else:
            st.dataframe(st.session_state.filtered_df)

        # Download final filtered data
        st.subheader("📥 Download Final Filtered Data")
        
        import io
        def convert_df(df):
            # Create a writable file-like object in memory
            excel_buffer = io.BytesIO()
            # Save the DataFrame to the file-like object
            df.to_excel(excel_buffer, index=False)
            # Reset the buffer's position to the start for reading
            excel_buffer.seek(0)
            # Return the bytes of the Excel file
            return excel_buffer.getvalue()
        
        if st.session_state.filtered_df.empty:
            st.warning("No data available to download.")
        else:
            try:
                save_name = st.text_input('Specify your file name:', value=None, placeholder='...')
                if save_name:  
                    xlsx = convert_df(st.session_state.filtered_df)
                    st.download_button(
                        label="Download data as XLSX format",
                        data=xlsx,
                        file_name=f'{save_name}.xlsx',
                        mime='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    )
            except Exception as e:
                st.error(f"Error preparing download: {e}")

    except Exception as e:
        st.error(f'Encountered an error: {e}. Please try again.')
        st.info("If the error persists, try refreshing the page or resetting all filters.")


def login_page():
    """Display login page"""
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.subheader("Login")
        with st.form("login_form"):
            username = st.text_input("Username")
            password = st.text_input("Password", type="password")
            submit_login = st.form_submit_button("Login")
            
            if submit_login:
                if username and password:
                    success, message = verify_credentials(username, password)
                    if success:
                        st.session_state["authenticated"] = True
                        st.session_state["username"] = username
                        st.session_state["name"] = message  # message contains the display name
                        st.rerun()
                    else:
                        st.error(f"Login failed: {message}")
                else:
                    st.error("Please enter both username and password")
    
    with col2:
        st.subheader("Register")
        with st.form("register_form"):
            new_username = st.text_input("New Username")
            new_password = st.text_input("New Password", type="password")
            confirm_password = st.text_input("Confirm Password", type="password")
            display_name = st.text_input("Your Name")
            submit_register = st.form_submit_button("Register")
            
            if submit_register:
                if not new_username or not new_password or not confirm_password or not display_name:
                    st.error("Please fill in all fields")
                elif new_password != confirm_password:
                    st.error("Passwords do not match")
                elif len(new_password) < 6:
                    st.error("Password must be at least 6 characters")
                else:
                    success, message = register_user(new_username, new_password, display_name)
                    if success:
                        st.success(message)
                        st.info(f"You can now log in with username: {new_username}")
                    else:
                        st.error(message)

def main():
    st.set_page_config(page_title="Analysis System", page_icon="📊", layout="wide")
    
    # Ensure secrets file exists
    setup_secrets_file()
    
    # Initialize session state for authentication
    if "authenticated" not in st.session_state:
        st.session_state["authenticated"] = False
    
    # Hide sidebar if not authenticated
    if not st.session_state["authenticated"]:
        st.markdown("""
        <style>
        [data-testid="stSidebar"] {
            display: none;
        }
        </style>
        """, unsafe_allow_html=True)
    
    # Display content based on authentication status
    if st.session_state["authenticated"]:
        # Show logout button in sidebar
        st.sidebar.title(f"Welcome, {st.session_state['name']}")
        if st.sidebar.button("Logout"):
            st.session_state["authenticated"] = False
            st.rerun()
        
        with st.sidebar:
            from streamlit_option_menu import option_menu 
            selected = option_menu(
                menu_title='Menu', 
                options=['Analysis','Filter','NL2SQL','Dashboard'], 
                icons=['bluetooth','gear-wide-connected','subtract','window-dash'], 
                menu_icon='cast', 
                default_index=0 
            )

        if selected == 'Analysis':
            analysis()

        elif selected == 'Filter':
            filter()

        elif selected == 'NL2SQL':
            from nl2sql_sqlite import main
            main()
    else:
        # Show login page
        st.subheader("Data System")
        login_page()

if __name__ == "__main__":
    main()