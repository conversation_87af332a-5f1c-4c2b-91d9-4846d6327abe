import streamlit as st
import pandas as pd
from transformers import AutoModelForSeq2SeqLM, AutoTokenizer

# # Set page config
# st.set_page_config(
#     page_title="SQL Query Generator",
#     page_icon="🔍",
#     layout="wide"
# )

@st.cache_resource
def load_model():
    model_name = "gaussalgo/T5-LM-Large-text2sql-spider"
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForSeq2SeqLM.from_pretrained(model_name)
    return tokenizer, model

def generate_sql(question, database_schema, tokenizer, model):
    # Combine the question and schema information
    input_text = f"Schema: {database_schema}\nQuestion: {question}"
    
    # Tokenize the input
    inputs = tokenizer(input_text, return_tensors="pt", max_length=512, truncation=True)
    
    # Generate SQL query
    outputs = model.generate(**inputs, max_length=128)
    
    # Decode the output
    sql_query = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    return sql_query


def main():
    st.markdown("<h2 style='text-align: center'>Text to SQL to SQLite Database Query Tool</h2>", unsafe_allow_html=True)
    st.markdown("<p style='text-align: center';class='subheader'>Ask questions about your data in natural language</p>", unsafe_allow_html=True)
    st.divider()

    # Load model
    with st.spinner("Loading model..."):
        tokenizer, model = load_model()
    
    # Load dataset
    from sqlalchemy import create_engine
    db_path = '/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Ranking/2025/EA_DataWarehouse.db'  
    connection_string = f"sqlite:///{db_path}"
    engine = create_engine(connection_string)
    df = pd.read_sql("SELECT * FROM test", con=engine)
    st.subheader('Default dataset')
    st.write(df)
        
    col1, col2 = st.columns([1, 1])
    
    with col1:
        # Schema input
        st.subheader("Database Schema")
        default_schema = """
        CREATE TABLE test (
            ID INT PRIMARY KEY,
            Name TEXT,
            Age INT,
            Class TEXT,
            Score INT,
            );"""
        database_schema = st.text_area("Enter your database schema", 
                                       default_schema, 
                                       height=250)
        
        # User question
        st.subheader("Your Question")
        question = st.text_input("Enter your question in natural language", 
                                 "Show me the total rows in test table")
        
        generate_button = st.button("Generate Query", type="primary", use_container_width=True)
    

    # Placeholders for results
    sql_query = st.empty()

    # Store query results in session state so they persist between button clicks
    if 'sql_query' not in st.session_state:
        st.session_state.sql_query = ""
    if 'corrected_sql_query' not in st.session_state:
        st.session_state.corrected_sql_query = ""
      
    # Process when Generate Query button is clicked
    if generate_button:
        with col2:
            with st.spinner("Generating SQL query..."):
                # Generate SQL query
                sql_query = generate_sql(question, database_schema, tokenizer, model)
                st.session_state.sql_query = sql_query
            
                # Display results
                st.markdown(f"**Original SQL Query:**\n```sql\n{sql_query}\n```")
                
            try:
                df = pd.read_sql(sql_query, con=engine)
                
                if df is not None and not df.empty:
                    st.subheader("Query Results")
                    st.dataframe(df, use_container_width=True)
                else:
                    st.info("No results found or error executing query.")
            except Exception as e:
                st.error(f"Error executing query: {str(e)}")

# if __name__ == "__main__":
#     main()