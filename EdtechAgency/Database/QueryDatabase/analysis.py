import streamlit as st
import pandas as pd
import numpy as np
from vnstock import Vnstock
stock = Vnstock().stock(symbol='VND', source='VCI')
from datetime import datetime

def all():
    st.sidebar.subheader('Indicators')

    def getdata(symbol, start):
        end_date = datetime.now()
        end_date_str = end_date.strftime('%Y-%m-%d')
        df = stock.quote.history(symbol, start, end_date_str)
        df = df.iloc[:,:6]
        df.columns = ['Time', 'Open', 'High', 'Low', 'Close', 'Volume']  
        df.set_index('Time', inplace=True)
        df.index = pd.to_datetime(df.index)  
        df = df.astype(float)
        return df

    def strategy_loop(data,sl,tp):
        in_position = False
        buydates,selldates = [], []
        buyprices,sellprices = [], []

        for index, row in data.iterrows():
            if not in_position and row.Buy == True:
                buyprice = row.shifted_open
                buydates.append(index)
                buyprices.append(buyprice)
                in_position = True
            if in_position:
                if row.Low < buyprice * sl:
                    sellprice = buyprice * sl
                    sellprices.append(sellprice)
                    selldates.append(index)
                    in_position = False
                elif row.High > buyprice * tp:
                    sellprice = buyprice * tp
                    sellprices.append(sellprice)
                    selldates.append(index)
                    in_position = False
        # profit calculation/ trading fee
        profits = pd.Series([(sell-buy)/buy - 0.0015 for sell,buy in zip(sellprices,buyprices)])
        st.write((profits + 1).prod())


    data = stock.listing.symbols_by_exchange()
    data = data[data['symbol'].str.len() == 3]
    ticker = st.sidebar.selectbox('Choose security:',data['symbol'])

    # pulling intraday security prices
    df = getdata(ticker,'2020-01-01')
    # SMA crossover strategy
    df['SMA_50'] = df['Close'].rolling(50).mean()
    df['SMA_100'] = df['Close'].rolling(100).mean()
    df.dropna(inplace=True)
    df['condition'] = (df['SMA_50'] > df['SMA_100'])
    df['Buy'] = (df.condition) & (df.condition.shift(1) == False)
    # iterative backtest
    df['shifted_open'] = df['Open'].shift(-1)

    # stop loss/ target profit arrays
    sl_range = 1 - np.arange(0.01,0.05,0.01)
    tp_range = 1 + np.arange(0.01,0.05,0.01)
    # finding best pair of SL/TP
    for sl in sl_range:
        for tp in tp_range:
            st.write('stop loss:' + str(sl), '/target profit:' + str(tp))
            strategy_loop(df,sl,tp)   