{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import logging\n", "from datetime import datetime\n", "import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.common.keys import Keys\n", "from selenium.common.exceptions import NoSuchElementException\n", "from selenium.webdriver.support.ui import Select\n", "import time"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# # Set up logging\n", "# logging.basicConfig(\n", "#     filename='/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/crawling.log',\n", "#     level=logging.INFO,\n", "#     format='%(asctime)s %(message)s'\n", "# )\n", "\n", "# def log_event(message):\n", "#     logging.info(message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chrome_options = Options()\n", "chrome_options.add_argument(\"--incognito\")\n", "# chrome_options.add_argument('--headless')\n", "driver = webdriver.Chrome(options=chrome_options)\n", "\n", "# Navigate to the target URL\n", "url = 'https://fast500.vn/Charts/Index?chartId=3'\n", "driver.get(url)\n", "\n", "# Locate the <select> dropdown element by its XPath\n", "dropdown = driver.find_element(By.XPATH, \"//*[@id='dataTables-search_length']/label/select\")\n", "\n", "# Create a Select object to interact with the dropdown\n", "select = Select(dropdown)\n", "\n", "# Select the option with the value \"100\"\n", "select.select_by_visible_text(\"100\")\n", "\n", "# Define the XPaths for the specific columns\n", "column_xpaths = {\n", "    'CompanyName': \".//*[@id='dataTables-search']/tbody/tr[{row_index}]/th[2]/span/span/a\",\n", "    'PersonRole': \".//*[@id='dataTables-search']/tbody/tr[{row_index}]/th[2]/span/div/span[1]\",\n", "    'PersonName': \".//*[@id='dataTables-search']/tbody/tr[{row_index}]/th[2]/span/div/span[1]/span[1]/a\",\n", "    'CompanyIndustry': \".//*[@id='dataTables-search']/tbody/tr[{row_index}]/th[2]/span/div/span[2]/span/a[2]\"\n", "}\n", "\n", "# Initialize an empty list to store row data\n", "table_data = []\n", "\n", "# Function to extract data for a single row based on the provided XPaths\n", "def extract_row_data(row_index):\n", "    row_data = {}\n", "    for column_name, xpath_template in column_xpaths.items():\n", "        try:\n", "            # Replace the {row_index} placeholder with the actual row index\n", "            xpath = xpath_template.format(row_index=row_index)\n", "            # Use the XPath to find the specific cell for the column\n", "            cell = driver.find_element(By.XPATH, xpath)\n", "            # Get the text content of the cell and add it to the dictionary\n", "            row_data[column_name] = cell.text.strip()\n", "        except NoSuchElementException:\n", "            # If any of the XPaths are not found, return None (assuming no more rows)\n", "            return None\n", "    return row_data\n", "\n", "# Function to extract data from all rows on the current page\n", "def extract_table_data():\n", "    row_index = 1\n", "    while True:\n", "        row_data = extract_row_data(row_index)\n", "        if row_data is None:\n", "            break  # No more data found, stop the loop\n", "        table_data.append(row_data)\n", "        row_index += 1\n", "\n", "# Function to go to the next page in pagination by clicking page numbers\n", "def click_next_page(page_number):\n", "    try:\n", "        # Find the pagination button by its page number and click it\n", "        page_button = driver.find_element(By.XPATH, f\"//a[@class='paginate_button' and text()='{page_number}']\")\n", "        page_button.click()\n", "        time.sleep(2)  # Wait for the page to load\n", "        return True\n", "    except NoSuchElementException:\n", "        return False  # No more pages\n", "\n", "# Extract data from the first page\n", "extract_table_data()\n", "\n", "# Find the total number of pagination buttons\n", "pagination_buttons = driver.find_elements(By.XPATH, \"//a[contains(@class, 'paginate_button') and not(contains(@class, 'current'))]\")\n", "\n", "# Iterate through all pages\n", "for page_button in pagination_buttons:\n", "    page_number = page_button.text.strip()\n", "    if page_number.isdigit():  # Only handle actual page numbers\n", "        click_next_page(page_number)\n", "        extract_table_data()\n", "\n", "# Convert the table data to a DataFrame\n", "df = pd.DataFrame(table_data)\n", "\n", "# Display the DataFrame or save it to a CSV file\n", "print(df.shape[0])\n", "df.to_excel('/Users/<USER>/Documents/Career_DataAnalyst_EdtechAgency/Product-Listing/500-1st/Scraped_Non-Edtech.xlsx', index=False)  \n", "\n", "# Close the WebDriver\n", "driver.quit()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "InvalidSelectorException", "evalue": "Message: invalid selector\nfrom javascript error: {\"status\":32,\"value\":\"The result of the xpath expression \\\"//*[@id='dataTables-search']/tbody/tr[1]/th[3]/span/div/span[1]/text()[1]\\\" is: [object Text]. It should be an element.\"}\n  (Session info: chrome=129.0.6668.58); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\nStacktrace:\n0   chromedriver                        0x0000000103313ed4 cxxbridge1$str$ptr + 1906348\n1   chromedriver                        0x000000010330c344 cxxbridge1$str$ptr + 1874716\n2   chromedriver                        0x0000000102f20264 cxxbridge1$string$len + 89492\n3   chromedriver                        0x0000000102f25350 cxxbridge1$string$len + 110208\n4   chromedriver                        0x0000000102f26ac0 cxxbridge1$string$len + 116208\n5   chromedriver                        0x0000000102f26b38 cxxbridge1$string$len + 116328\n6   chromedriver                        0x0000000102f64284 cxxbridge1$string$len + 368052\n7   chromedriver                        0x0000000102f9e7d4 cxxbridge1$string$len + 606980\n8   chromedriver                        0x0000000102f59134 cxxbridge1$string$len + 322660\n9   chromedriver                        0x0000000102f59d84 cxxbridge1$string$len + 325812\n10  chromedriver                        0x00000001032dbf90 cxxbridge1$str$ptr + 1677160\n11  chromedriver                        0x00000001032e08fc cxxbridge1$str$ptr + 1695956\n12  chromedriver                        0x00000001032c14b8 cxxbridge1$str$ptr + 1567888\n13  chromedriver                        0x00000001032e11cc cxxbridge1$str$ptr + 1698212\n14  chromedriver                        0x00000001032b2a60 cxxbridge1$str$ptr + 1507896\n15  chromedriver                        0x00000001032fd214 cxxbridge1$str$ptr + 1812972\n16  chromedriver                        0x00000001032fd36c cxxbridge1$str$ptr + 1813316\n17  chromedriver                        0x000000010330bfe4 cxxbridge1$str$ptr + 1873852\n18  libsystem_pthread.dylib             0x00000001848f606c _pthread_start + 320\n19  libsystem_pthread.dylib             0x00000001848f0da0 thread_start + 8\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mInvalidSelectorException\u001b[0m                  Traceback (most recent call last)", "Cell \u001b[0;32mIn[4], line 183\u001b[0m\n\u001b[1;32m    181\u001b[0m row_index \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[1;32m    182\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m--> 183\u001b[0m     row_data \u001b[38;5;241m=\u001b[39m \u001b[43mextract_row_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrow_index\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    184\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m row_data \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m    185\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m  \u001b[38;5;66;03m# No more rows on this page\u001b[39;00m\n", "Cell \u001b[0;32mIn[4], line 126\u001b[0m, in \u001b[0;36mextract_row_data\u001b[0;34m(row_index)\u001b[0m\n\u001b[1;32m    124\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    125\u001b[0m     xpath \u001b[38;5;241m=\u001b[39m xpath_template\u001b[38;5;241m.\u001b[39mformat(row_index\u001b[38;5;241m=\u001b[39mrow_index)\n\u001b[0;32m--> 126\u001b[0m     cell \u001b[38;5;241m=\u001b[39m \u001b[43mdriver\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfind_element\u001b[49m\u001b[43m(\u001b[49m\u001b[43mBy\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mXPATH\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mxpath\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    127\u001b[0m     row_data[column_name] \u001b[38;5;241m=\u001b[39m cell\u001b[38;5;241m.\u001b[39mtext\u001b[38;5;241m.\u001b[39mstrip()\n\u001b[1;32m    128\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m NoSuchElementException:\n", "File \u001b[0;32m~/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/selenium/webdriver/remote/webdriver.py:741\u001b[0m, in \u001b[0;36mWebDriver.find_element\u001b[0;34m(self, by, value)\u001b[0m\n\u001b[1;32m    738\u001b[0m     by \u001b[38;5;241m=\u001b[39m By\u001b[38;5;241m.\u001b[39mCSS_SELECTOR\n\u001b[1;32m    739\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m[name=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mvalue\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m]\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m--> 741\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mCommand\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mFIND_ELEMENT\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m{\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43musing\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mby\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mvalue\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m}\u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalue\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n", "File \u001b[0;32m~/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/selenium/webdriver/remote/webdriver.py:347\u001b[0m, in \u001b[0;36mWebDriver.execute\u001b[0;34m(self, driver_command, params)\u001b[0m\n\u001b[1;32m    345\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcommand_executor\u001b[38;5;241m.\u001b[39mexecute(driver_command, params)\n\u001b[1;32m    346\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m response:\n\u001b[0;32m--> 347\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43merror_handler\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcheck_response\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresponse\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    348\u001b[0m     response[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalue\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_unwrap_value(response\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalue\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[1;32m    349\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m response\n", "File \u001b[0;32m~/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/selenium/webdriver/remote/errorhandler.py:229\u001b[0m, in \u001b[0;36mErrorHandler.check_response\u001b[0;34m(self, response)\u001b[0m\n\u001b[1;32m    227\u001b[0m         alert_text \u001b[38;5;241m=\u001b[39m value[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124malert\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtext\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    228\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exception_class(message, screen, stacktrace, alert_text)  \u001b[38;5;66;03m# type: ignore[call-arg]  # mypy is not smart enough here\u001b[39;00m\n\u001b[0;32m--> 229\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m exception_class(message, screen, stacktrace)\n", "\u001b[0;31mInvalidSelectorException\u001b[0m: Message: invalid selector\nfrom javascript error: {\"status\":32,\"value\":\"The result of the xpath expression \\\"//*[@id='dataTables-search']/tbody/tr[1]/th[3]/span/div/span[1]/text()[1]\\\" is: [object Text]. It should be an element.\"}\n  (Session info: chrome=129.0.6668.58); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\nStacktrace:\n0   chromedriver                        0x0000000103313ed4 cxxbridge1$str$ptr + 1906348\n1   chromedriver                        0x000000010330c344 cxxbridge1$str$ptr + 1874716\n2   chromedriver                        0x0000000102f20264 cxxbridge1$string$len + 89492\n3   chromedriver                        0x0000000102f25350 cxxbridge1$string$len + 110208\n4   chromedriver                        0x0000000102f26ac0 cxxbridge1$string$len + 116208\n5   chromedriver                        0x0000000102f26b38 cxxbridge1$string$len + 116328\n6   chromedriver                        0x0000000102f64284 cxxbridge1$string$len + 368052\n7   chromedriver                        0x0000000102f9e7d4 cxxbridge1$string$len + 606980\n8   chromedriver                        0x0000000102f59134 cxxbridge1$string$len + 322660\n9   chromedriver                        0x0000000102f59d84 cxxbridge1$string$len + 325812\n10  chromedriver                        0x00000001032dbf90 cxxbridge1$str$ptr + 1677160\n11  chromedriver                        0x00000001032e08fc cxxbridge1$str$ptr + 1695956\n12  chromedriver                        0x00000001032c14b8 cxxbridge1$str$ptr + 1567888\n13  chromedriver                        0x00000001032e11cc cxxbridge1$str$ptr + 1698212\n14  chromedriver                        0x00000001032b2a60 cxxbridge1$str$ptr + 1507896\n15  chromedriver                        0x00000001032fd214 cxxbridge1$str$ptr + 1812972\n16  chromedriver                        0x00000001032fd36c cxxbridge1$str$ptr + 1813316\n17  chromedriver                        0x000000010330bfe4 cxxbridge1$str$ptr + 1873852\n18  libsystem_pthread.dylib             0x00000001848f606c _pthread_start + 320\n19  libsystem_pthread.dylib             0x00000001848f0da0 thread_start + 8\n"]}], "source": ["import logging\n", "from datetime import datetime\n", "import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import Select, WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.common.exceptions import NoSuchElementException, TimeoutException\n", "import time\n", "import os\n", "\n", "# Setup logging configuration\n", "log_file = '/Users/<USER>/Documents/Career_DataAnalyst_EdtechAgency/Product-Listing/500-1st/scrape-log.log'\n", "logging.basicConfig(filename=log_file, level=logging.INFO, \n", "                    format='%(asctime)s:%(levelname)s:%(message)s')\n", "\n", "# Initialize Chrome WebDriver\n", "chrome_options = Options()\n", "chrome_options.add_argument(\"--incognito\")\n", "chrome_options.add_argument('--headless')\n", "driver = webdriver.Chrome(options=chrome_options)\n", "\n", "# Navigate to the target URL\n", "# url = 'https://fast500.vn/Charts/Index?chartId=3'   # TOP 500 FASTEST GROWING COMPANIES\n", "# url = 'https://profit500.vn/Charts/Index?chartId=12'  # TOP 500 MOST PROFITABLE COMPANIES\n", "url = 'https://vnr500.com.vn/Charts/GetBusinessChartByVnrType?idVnrType=&areazipId=1'  # TOP BIGGEST VIETNAM COMPANIES IN EACH REGION\n", "driver.get(url)\n", "logging.info('Navigated to URL: %s', url)\n", "\n", "# ADDED: TOP BIGGEST VIETNAM COMPANIES IN EACH REGION --------------------------------\n", "# Filter to cities\n", "element = driver.find_element(By.XPATH,'//*[@id=\"page\"]/div[2]/div[2]/ul/div[3]/li/a')\n", "element.click()\n", "\n", "# Choose the specified city \n", "city_input = input('Specify the city name: ') # TP. = <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> Nẵng / Tỉnh = lefts\n", "# Locate the element where the text matches the input using f-string\n", "try:\n", "    city = driver.find_element(By.XPATH, f\"//*[@id='results']/div/a[text()='{city_input}']\")\n", "    city.click()  # Click on the matched city element\n", "    driver.implicitly_wait(2)\n", "    logging.info(f\"Chosen specified city: {city_input}\")\n", "except NoSuchElementException as e:\n", "    logging.error(f\"City '{city_input}' not found: {str(e)}\")\n", "# ------------------------------------------------------------------------------------\n", "\n", "# Locate and select the dropdown element\n", "dropdown = driver.find_element(By.XPATH, \"//*[@id='dataTables-search_length']/label/select\")\n", "select = Select(dropdown)\n", "select.select_by_visible_text(\"100\")\n", "logging.info(\"Selected '100' rows per page\")\n", "\n", "# Define the XPaths for the specific columns\n", "column_xpaths = {\n", "    # 'CompanyName': \".//*[@id='dataTables-search']/tbody/tr[{row_index}]/th[2]/span/span/a\",\n", "    'CompanyName': \"//*[@id='dataTables-search']/tbody/tr[{row_index}]/th[3]/span/span/a\",\n", "    # 'PersonRole': \".//*[@id='dataTables-search']/tbody/tr[{row_index}]/th[2]/span/div/span[1]\",\n", "    'PersonRole': \"//*[@id='dataTables-search']/tbody/tr[{row_index}]/th[3]/span/div/span[1]/text()[1]\",\n", "    # 'PersonName': \".//*[@id='dataTables-search']/tbody/tr[{row_index}]/th[2]/span/div/span[1]/span[1]/a\",\n", "    'PersonName': \"//*[@id='dataTables-search']/tbody/tr[{row_index}]/th[3]/span/div/span[1]/span[1]/a\",\n", "    # 'CompanyIndustry': \".//*[@id='dataTables-search']/tbody/tr[{row_index}]/th[2]/span/div/span[2]/span/a[2]\",\n", "    'CompanyIndustry': \"//*[@id='dataTables-search']/tbody/tr[{row_index}]/th[3]/span/div/span[2]/span/a\"\n", "}\n", "\n", "# Create the output file directory if it doesn't exist\n", "today = datetime.today().strftime('%Y-%m-%d')\n", "file_path = f'/Users/<USER>/Documents/Career_DataAnalyst_EdtechAgency/Product-Listing/500-1st/Scraped_Profitable_Non-Edtech_{today}.xlsx'\n", "directory = os.path.dirname(file_path)\n", "if not os.path.exists(directory):\n", "    os.makedirs(directory, exist_ok=True)\n", "\n", "# Initialize an empty list to store row data temporarily\n", "batch_data = []\n", "\n", "# Function to extract data for a single row\n", "def extract_row_data(row_index):\n", "    row_data = {}\n", "    try:\n", "        # Get CompanyName link element\n", "        company_name_link = driver.find_element(By.XPATH, column_xpaths['CompanyName'].format(row_index=row_index))\n", "        row_data['CompanyName'] = company_name_link.text.strip()\n", "\n", "        # Click the company name link to navigate to the details page\n", "        company_name_link.click()\n", "\n", "        # Wait for the new page to load\n", "        WebDriverWait(driver, 5).until(EC.presence_of_element_located((By.XPATH, \"//*[@id='home']\")))\n", "\n", "        # Extract additional data from the new page\n", "        row_data['CompanyEngName']        = driver.find_element(By.XPATH, \"//*[@id='home']/div/div[1]/div/table/tbody/tr[1]/td[2]\").text.strip()\n", "        row_data['CompanyRank']           = driver.find_element(By.XPATH, \"//*[@id='home']/div/div[1]/div/table/tbody/tr[2]/td[2]\").text.strip()\n", "        row_data['CompanyTaxCode']        = driver.find_element(By.XPATH, \"//*[@id='home']/div/div[1]/div/table/tbody/tr[3]/td[2]\").text.strip()\n", "        row_data['CompanyStockSymbol']    = driver.find_element(By.XPATH, \"//*[@id='home']/div/div[1]/div/table/tbody/tr[4]/td[2]\").text.strip()\n", "        row_data['CompanyAddress']        = driver.find_element(By.XPATH, \"//*[@id='home']/div/div[1]/div/table/tbody/tr[5]/td[2]\").text.strip()\n", "        row_data['PersonPhoneNo']         = driver.find_element(By.XPATH, \"//*[@id='home']/div/div[1]/div/table/tbody/tr[6]/td[2]\").text.strip()\n", "        row_data['PersonEmail']           = driver.find_element(By.XPATH, \"//*[@id='home']/div/div[1]/div/table/tbody/tr[8]/td[2]\").text.strip()\n", "        row_data['CompanyWebsite']        = driver.find_element(By.XPATH, \"//*[@id='home']/div/div[1]/div/table/tbody/tr[9]/td[2]\").text.strip()\n", "        row_data['CompanyEstablishedTime']= driver.find_element(By.XPATH, \"//*[@id='home']/div/div[1]/div/table/tbody/tr[10]/td[2]\").text.strip()\n", "\n", "        # Locate the parent element using the specified XPath\n", "        parent_element = driver.find_element(By.XPATH, \"//*[@id='home']/div/div[2]/div\")\n", "        # Find all <p> elements inside the parent element\n", "        p_elements = parent_element.find_elements(By.TAG_NAME, 'p')\n", "        # Extract the text from each <p> tag and join it into a single string\n", "        all_p_text = ' '.join([p.text.strip() for p in p_elements])\n", "        # Save the extracted text in the row_data dictionary under the key 'CompanyWebsite'\n", "        row_data['CompanyInfo'] = all_p_text\n", "\n", "        # Navigate back to the previous page\n", "        driver.back()\n", "\n", "        # Wait for the table page to load back\n", "        WebDriverWait(driver, 5).until(EC.presence_of_element_located((By.XPATH, \"//*[@id='dataTables-search']\")))\n", "\n", "    except NoSuchElementException as e:\n", "        logging.error(\"NoSuchElementException for row %s: %s\", row_index, str(e))\n", "        return None\n", "    except TimeoutException as e:\n", "        logging.error(\"TimeoutException for row %s: %s\", row_index, str(e))\n", "        return None\n", "\n", "    # Extract other data from the table row\n", "    for column_name, xpath_template in column_xpaths.items():\n", "        if column_name != 'CompanyName':\n", "            try:\n", "                xpath = xpath_template.format(row_index=row_index)\n", "                cell = driver.find_element(By.XPATH, xpath)\n", "                row_data[column_name] = cell.text.strip()\n", "            except NoSuchElementException:\n", "                row_data[column_name] = None  # Handle missing data gracefully\n", "\n", "    return row_data\n", "\n", "# Pagination function to go to the next page based on current active page\n", "def go_to_next_page():\n", "    try:\n", "        # Wait until the pagination buttons are visible\n", "        wait = Web<PERSON>river<PERSON>ait(driver, 5)\n", "        \n", "        # Find the currently active page button\n", "        current_page_button = wait.until(EC.presence_of_element_located((By.XPATH, \"//a[contains(@class, 'paginate_button current')]\")))\n", "        \n", "        # Try to find the next sibling button\n", "        next_page_button = current_page_button.find_element(By.XPATH, \"following-sibling::a[contains(@class, 'paginate_button')]\")\n", "        \n", "        # Click the next page button if it exists\n", "        if next_page_button:\n", "            next_page_button.click()\n", "            logging.info(f\"Clicked to go to next page.\")\n", "            \n", "            # Wait for the table on the next page to load\n", "            WebDriverWait(driver, 5).until(EC.presence_of_element_located((By.XPATH, \"//*[@id='dataTables-search']\")))\n", "            return True\n", "        else:\n", "            logging.info(\"No next page found. End of pages.\")\n", "            return False\n", "    \n", "    except NoSuchElementException:\n", "        logging.error(\"No next page found. Reached the last page.\")\n", "        return False\n", "    except TimeoutException:\n", "        logging.error(\"Timeout waiting for pagination buttons.\")\n", "        return False\n", "\n", "# Function to save data in batches to the Excel file\n", "def save_batch_to_excel(data, file_path):\n", "    try:\n", "        if os.path.exists(file_path):\n", "            existing_df = pd.read_excel(file_path)\n", "            new_df = pd.DataFrame(data)\n", "            combined_df = pd.concat([existing_df, new_df], ignore_index=True)\n", "        else:\n", "            combined_df = pd.DataFrame(data)\n", "\n", "        combined_df.to_excel(file_path, index=False)\n", "        logging.info(\"Successfully saved batch data to Excel\")\n", "    except Exception as e:\n", "        logging.error(\"Error while saving data to Excel: %s\", str(e))\n", "\n", "# Main scraping loop through pages\n", "while True:\n", "    row_index = 1\n", "    while True:\n", "        row_data = extract_row_data(row_index)\n", "        if row_data is None:\n", "            break  # No more rows on this page\n", "        batch_data.append(row_data)\n", "        row_index += 1\n", "\n", "        # Save data every 10 rows\n", "        if len(batch_data) >= 10:\n", "            save_batch_to_excel(batch_data, file_path)\n", "            batch_data = []  # Clear batch data after saving\n", "\n", "    # Try to go to the next page\n", "    if not go_to_next_page():\n", "        break  # No more pages\n", "\n", "# Save any remaining data after the last page\n", "if batch_data:\n", "    save_batch_to_excel(batch_data, file_path)\n", "\n", "# Close the WebDriver\n", "driver.quit()\n", "logging.info(\"Scraping completed and WebDriver closed\")\n", "\n", "\n", "logging.info(\"Read data from file path\")\n", "df = pd.read_excel(file_path)\n", "# Function to extract Role, PersonName, and PersonTaxCode\n", "def extract_info(row):\n", "    # Splitting the string based on the role and tax code markers\n", "    try:\n", "        role_part, tax_part = row.split('MST:')\n", "        role, person_name = role_part.split(':')\n", "        role = role.strip()\n", "        person_name = person_name.strip()\n", "        person_tax_code = tax_part.strip()\n", "\n", "        return pd.Series([role, person_name, person_tax_code])\n", "\n", "    except Exception as e:\n", "        return pd.Series([None, None, None])  # <PERSON><PERSON> errors gracefully\n", "\n", "logging.info(\"Apply the extraction from Person field\")\n", "# Apply the function to the 'Info' column and assign the result to new columns\n", "df[['PersonRole', 'PersonName', 'PersonTaxCode']] = df['PersonRole'].apply(extract_info)  # replace the original value of 'PersonRole' column with new 'PersonRole' value\n", "\n", "logging.info(\"Reconstruct the column position\")\n", "df = df[['CompanyName','CompanyEngName','CompanyWebsite','CompanyIndustry','CompanyStockSymbol','CompanyAddress','CompanyTaxCode','CompanyRank','CompanyEstablishedTime',\n", "         'CompanyInfo','PersonName','PersonRole','PersonEmail', 'EmailType','PersonPhoneNo','PersonTaxCode']]\n", "\n", "\n", "logging.info(\"Clean the Person phone number\")\n", "import re\n", "# Function to clean phone numbers\n", "def clean_phone_numbers(phone):\n", "    if not isinstance(phone, str):  # Check if the value is a string\n", "        return phone  # Return as is if not a string\n", "\n", "    # Remove spaces, dots, and parentheses\n", "    phone = re.sub(r'[.\\s()]', '', phone)  # Remove dots, spaces, and parentheses\n", "\n", "    # Handle specific cases with a leading country code\n", "    if phone.startswith('+84'):\n", "        phone = '0' + phone[3:]  # Replace +84 with 0\n", "    elif phone.startswith('84'):\n", "        phone = '0' + phone[2:]  # Replace 84 with 0\n", "\n", "    # Handle cases with multiple phone numbers separated by '/'\n", "    if '/' in phone:\n", "        parts = phone.split('/')\n", "        cleaned_parts = []\n", "        for part in parts:\n", "            part = part.strip()  # Remove extra spaces\n", "            # Format parts that include dashes\n", "            part = part.replace('-', '')  # Remove hyphens for processing\n", "            cleaned_parts.append(part)  # Add cleaned part\n", "\n", "        # Ensure proper formatting\n", "        if len(cleaned_parts) > 1:\n", "            # Assuming the area code is always the first part\n", "            area_code = cleaned_parts[0][:3]  # Take the first 3 digits as the area code\n", "            # Join the parts with the area code\n", "            cleaned_number = f\"{cleaned_parts[0]} - {area_code}{cleaned_parts[1]}\"\n", "            return cleaned_number\n", "\n", "    # Remove hyphens for single numbers\n", "    phone = phone.replace('-', '')\n", "\n", "    # Match and reformat based on the structure\n", "    match = re.match(r'(\\d{2,4})(\\d{7})(?:-(\\d{7}))?', phone)\n", "    if match:\n", "        if match.group(3):  # If there's a second part after a dash\n", "            return f\"{match.group(1)}{match.group(2)} - {match.group(1)}{match.group(3)}\"\n", "        return f\"{match.group(1)}{match.group(2)}\"\n", "\n", "    return phone\n", "\n", "# Apply the cleaning function to the PhoneNumbers column\n", "df['PersonPhoneNo'] = df['PersonPhoneNo'].apply(clean_phone_numbers)\n", "\n", "\n", "logging.info(\"Transform the Company city\")\n", "# List of city names to check for (case-insensitive)\n", "city_keywords = ['hà nội', 'hồ chí minh']\n", "\n", "# Function to determine city based on address\n", "def assign_city(address):\n", "    address_lower = address.lower()  # Convert address to lowercase for case-insensitive matching\n", "    for city in city_keywords:\n", "        if city in address_lower:\n", "            return city.title()  # Return city name in title case (capitalize first letter)\n", "    return 'Others'  # If no match, return 'Others'\n", "\n", "# Create new 'City' column based on 'CompanyAddress'\n", "df['CompanyCity'] = df['CompanyAddress'].apply(assign_city)\n", "\n", "\n", "logging.info(\"Transform the Company stock symbol\")\n", "import numpy as np\n", "# Ensure the column values are stripped of extra whitespace\n", "df['CompanyStockSymbol'] = df['CompanyStockSymbol'].str.strip()\n", "\n", "# 1st way: Use np.where to classify company size\n", "df['CompanySize'] = np.where(df['CompanyStockSymbol'] == 'Chưa niêm yết', 'medium', 'big')\n", "# Handle missing or empty values separately\n", "df['CompanySize'] = np.where(df['CompanyStockSymbol'].isna() | (df['CompanyStockSymbol'] == ''), 'medium', df['CompanySize'])\n", "\n", "\n", "logging.info(\"Save the transformed data to original file path\")\n", "df.to_excel(file_path, index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}