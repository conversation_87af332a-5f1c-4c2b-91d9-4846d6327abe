{"cells": [{"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import sqlite3\n", "\n", "def connect_to_sqlite(database_path):\n", "    \"\"\"Connects to an SQLite database.\"\"\"\n", "    try:\n", "        conn = sqlite3.connect(database_path)\n", "        return conn\n", "    except sqlite3.Error as e:\n", "        print(f\"Error connecting to SQLite: {e}\")\n", "        return None\n", "\n", "def execute_sqlite_query(conn, query, params=None):\n", "    \"\"\"Executes a SQL query on the SQLite database.\"\"\"\n", "    if conn is None:\n", "        return None\n", "\n", "    try:\n", "        cursor = conn.cursor()\n", "        if params:\n", "            cursor.execute(query, params)\n", "        else:\n", "            cursor.execute(query)\n", "        if query.lower().startswith(\"select\"):\n", "            results = cursor.fetchall()\n", "            return results\n", "        else:\n", "            conn.commit()\n", "            return None\n", "    except sqlite3.Error as e:\n", "        print(f\"Error executing SQLite query: {e}\")\n", "        conn.rollback()\n", "        return None\n", "    finally:\n", "        cursor.close()\n", "\n", "conn = connect_to_sqlite(\"/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/Datawarehouse/data/form_data.db\")\n", "# if conn:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['id', 'name', 'company', 'role', 'phone_no', 'email', 'sentiment', 'submission_time']\n"]}], "source": ["cursor = conn.cursor()\n", "cursor.execute(f\"PRAGMA table_info(form_submissions)\")\n", "columns = [row[1] for row in cursor.fetchall()]\n", "print((columns))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["[(1,\n", "  'test',\n", "  'test',\n", "  'C-level',\n", "  '0983 546 789',\n", "  '<EMAIL>',\n", "  1,\n", "  '2025-03-24 21:46:16'),\n", " (2,\n", "  'admin',\n", "  'merge',\n", "  'M-level',\n", "  '0983 546 789',\n", "  '<EMAIL>',\n", "  4,\n", "  '2025-03-24 21:49:16'),\n", " (3,\n", "  'admin',\n", "  'ea',\n", "  'C-level',\n", "  '0987 654 3219',\n", "  '<EMAIL>',\n", "  2,\n", "  '2025-03-24 22:08:39'),\n", " (4,\n", "  'add',\n", "  'eaa',\n", "  'E-level',\n", "  '0987 654 3221',\n", "  '<EMAIL>',\n", "  4,\n", "  '2025-03-24 22:12:01'),\n", " (5,\n", "  'addaa',\n", "  'eaaa',\n", "  'M-level',\n", "  '0987 654 3200',\n", "  '<EMAIL>',\n", "  2,\n", "  '2025-03-24 22:13:46'),\n", " (6,\n", "  'test2',\n", "  'test2',\n", "  'M-level',\n", "  '0987 678 456',\n", "  '<EMAIL>',\n", "  5,\n", "  '2025-03-25 16:04:51')]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["execute_sqlite_query(conn, \"SELECT * FROM form_submissions\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["execute_sqlite_query(conn, \"DELETE FROM form_submissions WHERE submission_time = '2025-03-25 16:08:31'\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["conn.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}