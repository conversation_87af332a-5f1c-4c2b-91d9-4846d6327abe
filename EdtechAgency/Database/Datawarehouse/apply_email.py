import gspread
from oauth2client.service_account import ServiceAccountCredentials
import pandas as pd
import re
from datetime import datetime
import logging

# Set up logging
logging.basicConfig(
    filename='/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/Datawarehouse/email_processing.log',
    level=logging.INFO,
    format='%(asctime)s %(message)s'
)

def log_event(message):
    logging.info(message)

# Updated regex pattern to match valid email structure within a string
email_pattern = re.compile(r'[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+')

# Function to fix common typos and structural issues
def fix_common_typos(email):
    if email is None:
        return None

    # Extract valid email part from any extra symbols like <>, (), etc.
    match = email_pattern.search(email)
    if match:
        email = match.group(0)  # Only keep the valid email part

    # Remove multiple '@' and keep only the first one
    if email.count('@') > 1:
        parts = email.split('@')
        email = parts[0] + '@' + ''.join(parts[1:])  # Keep only first '@'

    # Common typo corrections
    corrections = {
        '@domain': '.com',
        'gmailcom': 'gmail.com',
        '.cm': '.com',
        'gamil.com': 'gmail.com',
        'yahoocom': 'yahoo.com',
        'gmai.com': 'gmail.com',
        'yahoooo.com': 'yahoo.com',
        'gmal': 'gmail',
        'hanoieduvn': 'hanoiedu.vn',
        'tayho,edu,vn': 'tayho.edu.vn',
        'gmaill.com': 'gmail.com',
        'gmil.com': 'gmail.com',
        'yahô.com': 'yahoo.com',
        'yanhoo.com': 'yahoo.com',
        'gmailk': 'gmail',
        'gmail..com': 'gmail.com',
        'hanoiưdu': 'hanoiedu',
        'gmaill.com': 'gmail.com',
        'nocomment.con': 'nocomment.com',
        'gmaill@com': 'gmail.com',
        'yahoocomvn': 'yahoo.com.vn',
        'gmail. Com': 'gmail.com',
        'gmail,com': 'gmail.com',
        '@.gmail': '@gmail',
        '"gmail': '@gmail',
        'Gmail': 'gmail',
        '.om': '.com',
        '.cpm': '.com',
        '.con': '.com'
    }

    # Apply all corrections
    for wrong, right in corrections.items():
        email = email.replace(wrong, right)

    # Clean extra spaces and leading/trailing hyphens
    email = email.strip().strip('-')

    # Clean spaces within the email
    email = ''.join(email.split())

    # Convert to lowercase if the entire email is in uppercase
    if email.isupper():
        email = email.lower()

    # Final validation after all corrections
    if email.endswith('.'):
        email = email[:-1]  # Remove trailing dot if present
    
    if email.endswith('gmail'):
        email = email.replace('gmail','gmail.com')

    return email

# Function to validate email structure but apply typo fixes for invalid ones
def validate_email(email):
    if email is None or not isinstance(email, str):
        return None, False  # Return None and False for invalid email input

    # Apply typo fixes first
    email = fix_common_typos(email)

    # Check for valid structure after typo fixes
    if re.match(email_pattern, email):
        return email, True  # Valid email

    # Even if invalid, return the cleaned email (typos fixed) and mark as invalid
    return email, False

# Function to clean the email column and return both cleaned emails and validation flag
def clean_emails(email_cell):
    # Check if the email_cell is a string
    if isinstance(email_cell, str):
        # Split by ' - ' if multiple emails are separated
        emails = [email.strip() for email in email_cell.split(' - ') if email.strip()]
        
        # Initialize a list to store cleaned emails and a validation flag
        cleaned_emails = []
        is_valid = True  # Assume all are valid initially

        for email in emails:
            # Validate and clean the email
            cleaned_email, valid = validate_email(email)
            
            # If any email is invalid, set the overall validity to False
            if not valid:
                is_valid = False

            # Add the cleaned email to the list
            cleaned_emails.append(cleaned_email)
        
        # Join cleaned emails back into a single string
        return ' - '.join(cleaned_emails), is_valid
    else:
        # If email_cell is not a string (e.g., NaN), return None and False
        return None, False

# Backup Google Sheet
def backup_sheet(sheet, worksheet_name, backup_name):
    worksheet = sheet.worksheet(worksheet_name)
    sheet.duplicate_sheet(source_sheet_id=worksheet.id, new_sheet_name=backup_name)

# Set up the connection to Google Sheets
scope = ["https://spreadsheets.google.com/feeds", 'https://www.googleapis.com/auth/drive']
creds = ServiceAccountCredentials.from_json_keyfile_name('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/Datawarehouse/temp_service_account.json', scope)
client = gspread.authorize(creds)

# Open the Google Sheet by URL
sheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')

# Backup the original sheet
backup_name = f"Backup_Merge2Business_AppliedEmail_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
backup_sheet(sheet, 'Merge2Business', backup_name)

# Log retrieval
log_event('Retrieving data from Google Sheets')
worksheet = sheet.worksheet("Merge2Business")

# Fetch data and manually set the second row as the header
data = worksheet.get_all_values()
first_row = data[0]  # Preserve the first row
header = data[1]  # Set the second row (index 1) as the header
rows = data[2:]   # Start data from the third row (index 2)

# Convert to DataFrame
existing_data = pd.DataFrame(rows, columns=header)

# Ensure the 'ID' column exists and apply email cleaning logic if an 'Email' column exists
# if 'ID' in existing_data.columns and 'Email' in existing_data.columns:
if 'Email' in existing_data.columns:

    # Apply the email cleaning to the 'Email' column
    log_event('Cleaning emails in the "Email" column')
    
    # Apply the email cleaning logic to the 'Email' column
    cleaned_emails, validate_flags = zip(*existing_data['Email'].apply(clean_emails))
    
    # Get the position of the 'Email' column
    email_col_index = existing_data.columns.get_loc('Email') + 1  # +1 to insert after 'Email'
    
    # Insert 'Cleaned_Emails' at the correct position
    existing_data.insert(email_col_index, 'Cleaned_Emails', cleaned_emails)
    
    # Insert 'validate' column at the correct position (which is now shifted by +1)
    existing_data.insert(email_col_index + 1, 'validate', validate_flags)
    
    # Now modify the header to include 'Cleaned_Emails' and 'validate'
    header.insert(email_col_index, 'Cleaned_Emails')  # Insert after 'Email'
    header.insert(email_col_index + 1, 'validate')    # Insert the 'validate' column

else:
    log_event('Error: "ID" or "Email" column not found in the sheet.')

# Log completion of data processing
log_event('Saving changes to Google Sheets')

# Reconstruct the Google Sheet with the first row, header, and cleaned data
updated_data = [first_row] + [header] + existing_data.values.tolist()

# Update Google Sheets, keeping the first row and header intact
worksheet.clear()  # Clear the worksheet before updating it
worksheet.update(f'A1', updated_data)  # Start updating from row 1 (A1)

# Log the completion of the update
log_event('Changes saved to Google Sheets successfully')