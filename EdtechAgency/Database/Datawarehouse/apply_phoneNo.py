import re
import pandas as pd
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from datetime import datetime
import logging

# Set up logging
logging.basicConfig(
    filename='/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/Datawarehouse/phoneNo_processing.log',
    level=logging.INFO,
    format='%(asctime)s %(message)s'
)

def log_event(message):
    logging.info(message)

# Function to clean phone numbers
def clean_phone_numbers(phone):
    if not isinstance(phone, str):  # Check if the value is a string
        return phone  # Return as is if not a string

    # Skip phone numbers that are already cleaned (in the format xxx xxx xxx or xxx xxx xxxx)
    if re.match(r'(\d{4} \d{3} \d{3}|\d{4} \d{3} \d{4})( - (\d{4} \d{3} \d{3}|\d{4} \d{3} \d{4}))*', phone):
        return phone  # Return the phone number as is if it matches the cleaned format

    # Replace '|' with '-' for separation
    phone = phone.replace('|', '-')

    # Remove spaces, dots, and parentheses
    phone = re.sub(r'[.\s()]', '', phone)  # Remove dots, spaces, and parentheses

    # Handle specific cases with a leading country code
    if phone.startswith('+84'):
        phone = '0' + phone[3:]  # Replace +84 with 0
    elif phone.startswith('84'):
        phone = '0' + phone[2:]  # Replace 84 with 0

    # Handle cases with multiple phone numbers separated by '/'
    if '/' in phone:
        parts = phone.split('/')
        cleaned_parts = []
        for part in parts:
            part = part.strip()  # Remove extra spaces
            part = part.replace('-', '')  # Remove hyphens for processing
            cleaned_parts.append(clean_phone_numbers(part))  # Recursively clean each part

        return ' - '.join(cleaned_parts)  # Return the cleaned numbers separated by '-'

    # Remove hyphens for single numbers
    phone = phone.replace('-', '')

    # Match the digits of the phone number
    match = re.match(r'(\d+)', phone)
    if match:
        digits = match.group(1)

        # Ensure that the number starts with '0'
        if not digits.startswith('0'):
            digits = '0' + digits

        # Format based on the number of digits
        if len(digits) == 10:  # Format for 9-digit numbers (with the leading 0)
            return f"{digits[:4]} {digits[4:7]} {digits[7:]}"
        elif len(digits) == 11:  # Format for 10-digit numbers (with the leading 0)
            return f"{digits[:4]} {digits[4:7]} {digits[7:]}"
        elif len(digits) > 11:  # Handle more than 10 digits (with leading 0)
            return f"{digits[:4]} {digits[4:7]} {digits[7:]}"

    return phone

# Google Sheets authentication and setup
scope = ["https://spreadsheets.google.com/feeds", 'https://www.googleapis.com/auth/drive']
creds = ServiceAccountCredentials.from_json_keyfile_name('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/Datawarehouse/temp_service_account.json', scope)
client = gspread.authorize(creds)

# Open the Google Sheet
sheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')

# Backup the original sheet
def backup_sheet(sheet, sheet_name, backup_name):
    original_worksheet = sheet.worksheet(sheet_name)
    original_data = original_worksheet.get_all_values()
    backup_worksheet = sheet.add_worksheet(title=backup_name, rows=len(original_data), cols=len(original_data[0]))
    backup_worksheet.update('A1', original_data)

backup_name = f"Backup_Business_AppliedPhoneNo_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
backup_sheet(sheet, 'Merge2Business', backup_name)

# Fetch data from the Google Sheet
log_event('Retrieving data from Google Sheets')
worksheet = sheet.worksheet("Merge2Business")

# Fetch data and manually set the second row as the header
data = worksheet.get_all_values()
first_row = data[0]  # Preserve the first row
header = data[1]  # Set the second row (index 1) as the header
rows = data[2:]   # Start data from the third row (index 2)

# Convert to DataFrame
existing_data = pd.DataFrame(rows, columns=header)

# Clean the phone numbers and store them in a new column right after the original column
existing_data['Processed_PhoneNo'] = existing_data['SĐT'].apply(clean_phone_numbers)
# existing_data['Processed_PhoneNo'] = existing_data['Processed_PhoneNo'].apply(clean_phone_numbers)

# Reorder columns to place 'Processed_PhoneNo' right after 'SĐT'
columns_order = list(existing_data.columns)
# sdt_index = columns_order.index('SĐT')  # Get index of original column
# columns_order.insert(sdt_index + 1, columns_order.pop(columns_order.index('Processed_PhoneNo')))  # Move 'Processed_PhoneNo' next to 'SĐT'
# existing_data = existing_data[columns_order]  # Reorder the DataFrame

# Log completion of data processing
log_event('Saving changes to Google Sheets')

# Reconstruct the Google Sheet with the first row, header, and cleaned data
updated_data = [first_row] + [columns_order] + existing_data.values.tolist()

# Update Google Sheets, keeping the first row and header intact
worksheet.clear()  # Clear the worksheet before updating it
worksheet.update(f'A1', updated_data)  # Start updating from row 1 (A1)
log_event('Changes saved to Google Sheets successfully')