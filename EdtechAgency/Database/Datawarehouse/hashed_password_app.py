import mysql.connector
import bcrypt

# Create a connection to the MySQL database
def get_db_connection():
    return mysql.connector.connect(
        host="localhost", 
        user="root",       
        password="HnAm2002#@!",  
        database="UserName_Password_EdtechAgencyDataWarehouse" 
    )

# Function to create an admin user with a hashed password
def create_admin_user(username, password):
    conn = get_db_connection()
    cursor = conn.cursor()

    # Hash the password using bcrypt
    hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

    # SQL query to insert the user
    query = "INSERT INTO users (EA_username, EA_password, role) VALUES (%s, %s, %s)"
    cursor.execute(query, (username, hashed_password.decode('utf-8'), 'admin'))

    conn.commit()
    conn.close()

# Use this function to create your admin user
create_admin_user('admin', 'edtechagency2024')