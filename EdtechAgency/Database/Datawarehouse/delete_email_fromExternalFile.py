import pandas as pd
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from datetime import datetime

# Google Sheets authentication and setup
scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
creds = ServiceAccountCredentials.from_json_keyfile_name('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/.streamlit/ea-database-form-52d7d9ec9d8c.json', scope)
client = gspread.authorize(creds)

# Open the Google Sheet
sheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')
worksheet = sheet.worksheet("All_Businesses")

# Backup function
def backup_sheet(sheet, sheet_name, backup_name):
    original_worksheet = sheet.worksheet(sheet_name)
    original_data = original_worksheet.get_all_values()
    backup_worksheet = sheet.add_worksheet(title=backup_name, rows=len(original_data), cols=len(original_data[0]))
    backup_worksheet.update('A1', original_data)

backup_name = f"Backup_All_Businesses_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
backup_sheet(sheet, 'All_Businesses', backup_name)

# Fetch data and manually set the second row as the header
data = worksheet.get_all_values()
first_row = data[0]  # Preserve the first row
header = data[1]  # Set the second row (index 1) as the header
rows = data[2:]   # Start data from the third row (index 2)

# Convert to DataFrame
sheet_data = pd.DataFrame(rows, columns=header)

# Load emails from File A (local CSV/Excel file)
file_a_path = '/Users/<USER>/Downloads/Email-Error.xlsx'  
file_a_emails = pd.read_excel(file_a_path, sheet_name='Sheet2')
# file_a_emails = file_a_emails[file_a_emails['Object'] == 'K12']
email_column_file_a = 'Mail'  
emails_in_file_a = set(file_a_emails[email_column_file_a].str.strip().dropna())

# Identify emails to keep in Sheet B
email_column_sheet_b = 'Email'  
sheet_emails = sheet_data[email_column_sheet_b].str.strip()
rows_to_keep = ~sheet_emails.isin(emails_in_file_a)

# Filter the DataFrame to exclude matching emails
filtered_data = sheet_data[rows_to_keep]

# Update the Google Sheet with filtered data
worksheet.clear()  # Clear the sheet
worksheet.update([first_row] + [header] + filtered_data.values.tolist())  # Write back the header and filtered data
