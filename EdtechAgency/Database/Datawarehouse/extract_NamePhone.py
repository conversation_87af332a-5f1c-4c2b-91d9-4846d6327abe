import re
import pandas as pd
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from datetime import datetime
# import logging

# # Set up logging
# logging.basicConfig(
#     filename='/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/Datawarehouse/phoneNo_processing.log',
#     level=logging.INFO,
#     format='%(asctime)s %(message)s'
# )

# def log_event(message):
#     logging.info(message)

# Google Sheets authentication and setup
scope = ["https://spreadsheets.google.com/feeds", 'https://www.googleapis.com/auth/drive']
creds = ServiceAccountCredentials.from_json_keyfile_name('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/.streamlit/ea-database-form-fbf45c7169a9.json', scope)
client = gspread.authorize(creds)

# Open the Google Sheet
sheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')

# Backup the original sheet
def backup_sheet(sheet, sheet_name, backup_name):
    original_worksheet = sheet.worksheet(sheet_name)
    original_data = original_worksheet.get_all_values()
    backup_worksheet = sheet.add_worksheet(title=backup_name, rows=len(original_data), cols=len(original_data[0]))
    backup_worksheet.update('A1', original_data)

backup_name = f"Backup_Business_ExtractPhoneNo_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
backup_sheet(sheet, 'Business', backup_name)

# Fetch data from the Google Sheet
# log_event('Retrieving data from Google Sheets')
worksheet = sheet.worksheet("Business")

# Fetch data and manually set the second row as the header
data = worksheet.get_all_values()
first_row = data[0]  # Preserve the first row
header = data[1]  # Set the second row (index 1) as the header
rows = data[2:]   # Start data from the third row (index 2)

# Convert to DataFrame
existing_data = pd.DataFrame(rows, columns=header)

# Split into two parts: Rows where 'ID' starts with 'AC' and others
ac_data = existing_data[existing_data['ID'].str.startswith('AC')]  # Only rows with 'ID' starting with 'AC'
other_data = existing_data[~existing_data['ID'].str.startswith('AC')]  # The rest of the rows

# Regular expression pattern for phone numbers (matches various formats)
phone_pattern = r'(\+?\d{1,3}[-.\s]?\(?\d{1,3}\)?[-.\s]?\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,9})'

# Process only 'AC' rows
ac_data['Processed_PhoneNo'] = ac_data['Người phụ trách/đại diện'].astype(str).apply(lambda x: re.findall(phone_pattern, x))

# If there are multiple phone numbers, join them into a single string, else take the first one
ac_data['Processed_PhoneNo'] = ac_data['Processed_PhoneNo'].apply(lambda x: ', '.join(x) if x else '')

# Remove the phone number from the 'Người phụ trách/đại diện' column to keep only the name
ac_data['Người phụ trách/đại diện'] = ac_data['Người phụ trách/đại diện'].astype(str).apply(lambda x: re.sub(phone_pattern, '', x).strip())

# Combine the processed 'AC' rows and the unprocessed other rows back into one DataFrame
combined_data = pd.concat([ac_data, other_data])

# Sort back to original order if necessary (optional, based on original row order)
combined_data.sort_index(inplace=True)

# Log completion of data processing
columns_order = list(combined_data.columns)

# Reconstruct the Google Sheet with the first row, header, and cleaned data
updated_data = [first_row] + [columns_order] + combined_data.values.tolist()

# Update Google Sheets, keeping the first row and header intact
worksheet.clear()  # Clear the worksheet before updating it
worksheet.update(f'A1', updated_data)  # Start updating from row 1 (A1)
# log_event('Changes saved to Google Sheets successfully')