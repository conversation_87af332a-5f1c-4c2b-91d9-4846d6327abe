import streamlit as st
import pandas as pd
import numpy as np
import io
import base64
from PIL import Image
from oauth2client.service_account import ServiceAccountCredentials
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import gspread
import json
import os
from datetime import datetime

# C<PERSON>u hình tiêu đề trang
st.set_page_config(
    page_title="Bảng Thu Thập Thông Tin",
    page_icon="📝",
    layout="wide"
)

# Hàm kết nối với Google Sheets
@st.cache_resource
def connect_to_gsheets():
    # Đặt biến môi trường cho chứng chỉ
    try:
        # Đường dẫn đến tệp chứng chỉ của bạn
        credentials_path = '/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/.streamlit/ea-database-form-fbf45c7169a9.json'
        
        # Đ<PERSON><PERSON> bảo tệp tồn tại và có quyền đọc
        if not os.path.exists(credentials_path):
            st.error(f"Không tìm thấy tệp chứng chỉ: {credentials_path}")
            return None
            
        # Đọc tệp chứng chỉ
        scope = ["https://spreadsheets.google.com/feeds", 
                 "https://www.googleapis.com/auth/drive"]
        
        # Sử dụng thư viện google.oauth2.service_account
        credentials = service_account.Credentials.from_service_account_file(
            credentials_path, scopes=scope)
            
        # Tạo client
        client = gspread.authorize(credentials)
        return client
    except Exception as e:
        st.error(f"Lỗi kết nối: {str(e)}")
        return None

# Hàm tạo mới hoặc mở một Google Sheet
def get_or_create_sheet(gc, sheet_name="BangThuThapThongTin"):
    if gc is None:
        st.error("Không thể kết nối với Google Sheets. Vui lòng kiểm tra chứng chỉ.")
        return None
        
    try:
        # Thử mở sheet hiện có
        sheet = gc.open(sheet_name)
        st.success(f"Đã kết nối tới bảng tính '{sheet_name}'")
    except Exception as e:
        st.info(f"Đang tạo bảng tính mới '{sheet_name}'...")
        try:
            # Tạo sheet mới nếu không tồn tại
            sheet = gc.create(sheet_name)
            # Chia sẻ sheet với mọi người có link
            sheet.share(None, perm_type='anyone', role='writer', with_link=True)
            
            # Tạo worksheet đầu tiên với dữ liệu khởi tạo
            worksheet = sheet.get_worksheet(0)
            headers = ["Timestamp", "Row", "Column", "Column Title", "Text Content", "Image Data"]
            worksheet.append_row(headers)
            
            # Tạo worksheet cho tiêu đề cột
            title_worksheet = sheet.add_worksheet(title="Column Titles", rows=2, cols=5)
            title_worksheet.append_row(["Column 1", "Column 2", "Column 3", "Column 4"])
            st.success(f"Đã tạo bảng tính mới '{sheet_name}'")
        except Exception as create_error:
            st.error(f"Không thể tạo bảng tính: {str(create_error)}")
            return None
    
    return sheet

# Hàm lưu dữ liệu vào Google Sheets
def save_to_sheets(gc, data, sheet_name="BangThuThapThongTin"):
    sheet = get_or_create_sheet(gc, sheet_name)
    worksheet = sheet.get_worksheet(0)
    worksheet.append_row(data)

# Hàm lưu tiêu đề cột
def save_column_titles(gc, titles, sheet_name="BangThuThapThongTin"):
    sheet = get_or_create_sheet(gc, sheet_name)
    try:
        title_worksheet = sheet.worksheet("Column Titles")
    except:
        title_worksheet = sheet.add_worksheet(title="Column Titles", rows=2, cols=5)
    
    # Xóa dữ liệu cũ và thêm dữ liệu mới
    title_worksheet.clear()
    title_worksheet.append_row(titles)

# Hàm tải tiêu đề cột
def load_column_titles(gc, sheet_name="BangThuThapThongTin"):
    sheet = get_or_create_sheet(gc, sheet_name)
    try:
        title_worksheet = sheet.worksheet("Column Titles")
        titles = title_worksheet.row_values(1)
        # Đảm bảo luôn có 4 tiêu đề
        if len(titles) < 4:
            titles.extend(["Title Column " + str(i+1) for i in range(len(titles), 4)])
        return titles[:4]
    except:
        return ["Title Column 1", "Title Column 2", "Title Column 3", "Title Column 4"]

# Hàm chuyển đổi hình ảnh sang base64 để lưu trữ
def image_to_base64(image):
    buffered = io.BytesIO()
    image.save(buffered, format="JPEG")
    img_str = base64.b64encode(buffered.getvalue()).decode()
    return img_str

# Hàm chuyển đổi base64 thành hình ảnh
def base64_to_image(base64_str):
    if base64_str:
        img_data = base64.b64decode(base64_str)
        return Image.open(io.BytesIO(img_data))
    return None

# Tải CSS tùy chỉnh
def load_css():
    st.markdown("""
    <style>
    .main {
        padding: 0rem 1rem;
    }
    .logo-container {
        background-color: #0e5c7e;
        padding: 20px;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 20px;
    }
    .note-container {
        background-color: #0e5c7e;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 10px;
        color: white;
    }
    .input-text {
        background-color: yellow;
        color: black;
        padding: 10px;
        border-radius: 5px;
        width: 100%;
    }
    .add-button {
        background-color: yellow;
        color: black;
        padding: 10px 20px;
        border-radius: 20px;
        font-weight: bold;
        text-align: center;
        cursor: pointer;
    }
    .image-placeholder {
        background-color: yellow;
        color: black;
        padding: 10px;
        border-radius: 5px;
        text-align: center;
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .edit-title {
        margin-top: 10px;
    }
    .title-input {
        width: 100%;
    }
    .arrow-icon {
        font-size: 50px;
        color: #0e5c7e;
        text-align: center;
    }
    </style>
    """, unsafe_allow_html=True)

def main():
    load_css()
    
    # Kết nối đến Google Sheets
    gc = connect_to_gsheets()
    
    # Hiển thị logo và tiêu đề
    st.markdown('<div class="logo-container"><h1>BẢNG THU THẬP THÔNG TIN</h1></div>', unsafe_allow_html=True)
    
    # Tải tiêu đề cột
    if gc:
        column_titles = load_column_titles(gc)
    else:
        column_titles = ["Title Column 1", "Title Column 2", "Title Column 3", "Title Column 4"]
    
    # Khởi tạo session state nếu chưa có
    if 'notes' not in st.session_state:
        st.session_state.notes = [{"texts": ["", ""], "images": ["", ""]}]
    
    if 'edit_titles' not in st.session_state:
        st.session_state.edit_titles = False
    
    # Nút chỉnh sửa tiêu đề
    if st.button("Chỉnh sửa tiêu đề cột" if not st.session_state.edit_titles else "Lưu tiêu đề cột"):
        st.session_state.edit_titles = not st.session_state.edit_titles
        
        # Lưu tiêu đề mới vào Google Sheets
        if not st.session_state.edit_titles and gc:
            save_column_titles(gc, column_titles)
    
    # Hiển thị input để chỉnh sửa tiêu đề nếu đang trong chế độ chỉnh sửa
    if st.session_state.edit_titles:
        cols = st.columns(4)
        for i in range(4):
            column_titles[i] = cols[i].text_input(f"Tiêu đề cột {i+1}", column_titles[i], key=f"title_{i}")
    
    # Hiển thị các hàng ghi chú
    for idx, note in enumerate(st.session_state.notes):
        st.markdown(f"<h3>Note row {idx+1}</h3>", unsafe_allow_html=True)
        
        cols = st.columns([1, 1, 1, 1, 0.5])
        
        # Hai cột đầu cho văn bản
        for i in range(2):
            with cols[i]:
                st.markdown(f"<div class='note-container'><h4>{column_titles[i]}</h4>", unsafe_allow_html=True)
                note["texts"][i] = st.text_area("", note["texts"][i], key=f"text_{idx}_{i}", 
                                               placeholder="(Input text here)", height=100)
                st.markdown("</div>", unsafe_allow_html=True)
        
        # Hai cột sau cho hình ảnh
        for i in range(2, 4):
            with cols[i]:
                st.markdown(f"<div class='note-container'><h4>{column_titles[i]}</h4>", unsafe_allow_html=True)
                
                uploaded_file = st.file_uploader(f"Tải ảnh cho {column_titles[i]}", type=["jpg", "jpeg", "png"], key=f"img_{idx}_{i}", label_visibility="collapsed")
                
                if uploaded_file is not None:
                    image = Image.open(uploaded_file)
                    note["images"][i-2] = image_to_base64(image)
                    st.image(image, use_column_width=True)
                elif note["images"][i-2]:
                    try:
                        image = base64_to_image(note["images"][i-2])
                        st.image(image, use_column_width=True)
                    except:
                        st.markdown("<div class='image-placeholder'>Insert picture here</div>", unsafe_allow_html=True)
                else:
                    st.markdown("<div class='image-placeholder'>Insert picture here</div>", unsafe_allow_html=True)
                
                st.markdown("</div>", unsafe_allow_html=True)
        
        # Cột cuối cho nút thêm ghi chú
        if idx == len(st.session_state.notes) - 1:
            with cols[4]:
                st.markdown("<div class='add-button' onclick='add_note()' id='add-note'>(+) Add note</div>", 
                           unsafe_allow_html=True)
                st.markdown("""
                <script>
                function add_note() {
                    document.querySelector('#add-note-button').click();
                }
                </script>
                """, unsafe_allow_html=True)
                
                # Nút ẩn kích hoạt sự kiện thêm ghi chú
                if st.button("", key="add-note-button", help="Add a new note"):
                    st.session_state.notes.append({"texts": ["", ""], "images": ["", ""]})
                    st.experimental_rerun()
        
        # Hiển thị mũi tên chỉ xuống nếu không phải hàng cuối
        if idx < len(st.session_state.notes) - 1:
            st.markdown("<div class='arrow-icon'>⬇️</div>", unsafe_allow_html=True)
    
    # Nút lưu tất cả thay đổi vào Google Sheets
    if st.button("Lưu tất cả thay đổi"):
        if gc:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            for idx, note in enumerate(st.session_state.notes):
                for col in range(4):
                    content = ""
                    if col < 2:
                        content = note["texts"][col]
                    else:
                        content = note["images"][col-2]
                    
                    data = [timestamp, idx+1, col+1, column_titles[col], content, ""]
                    save_to_sheets(gc, data)
            
            st.success("Đã lưu tất cả thay đổi vào Google Sheets!")
        else:
            st.error("Không thể lưu dữ liệu. Vui lòng kiểm tra kết nối Google Sheets.")

if __name__ == "__main__":
    main()