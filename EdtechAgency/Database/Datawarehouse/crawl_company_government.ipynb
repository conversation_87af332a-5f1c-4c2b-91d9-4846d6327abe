{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# WEBSITE IS USING CLOUDFLARE"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No pop-up or error handling pop-up: Message: \n", "Stacktrace:\n", "0   chromedriver                        0x0000000100634500 cxxbridge1$str$ptr + 1917112\n", "1   chromedriver                        0x000000010062c890 cxxbridge1$str$ptr + 1885256\n", "2   chromedriver                        0x000000010023c538 cxxbridge1$string$len + 89424\n", "3   chromedriver                        0x0000000100280878 cxxbridge1$string$len + 368784\n", "4   chromedriver                        0x00000001002bab7c cxxbridge1$string$len + 607124\n", "5   chromedriver                        0x0000000100275374 cxxbridge1$string$len + 322444\n", "6   chromedriver                        0x0000000100275fc4 cxxbridge1$string$len + 325596\n", "7   chromedriver                        0x00000001005fbd2c cxxbridge1$str$ptr + 1685732\n", "8   chromedriver                        0x0000000100600530 cxxbridge1$str$ptr + 1704168\n", "9   chromedriver                        0x00000001005e0e08 cxxbridge1$str$ptr + 1575360\n", "10  chromedriver                        0x0000000100600e00 cxxbridge1$str$ptr + 1706424\n", "11  chromedriver                        0x00000001005d1f94 cxxbridge1$str$ptr + 1514316\n", "12  chromedriver                        0x000000010061d62c cxxbridge1$str$ptr + 1823204\n", "13  chromedriver                        0x000000010061d7ac cxxbridge1$str$ptr + 1823588\n", "14  chromedriver                        0x000000010062c530 cxxbridge1$str$ptr + 1884392\n", "15  libsystem_pthread.dylib             0x0000000185c1e06c _pthread_start + 320\n", "16  libsystem_pthread.dylib             0x0000000185c18da0 thread_start + 8\n", "\n"]}, {"ename": "TimeoutException", "evalue": "Message: \n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTimeoutException\u001b[0m                          Traceback (most recent call last)", "Cell \u001b[0;32mIn[2], line 149\u001b[0m\n\u001b[1;32m    146\u001b[0m handle_pop_up()\n\u001b[1;32m    148\u001b[0m \u001b[38;5;66;03m# Scrape the current page after handling any pop-ups\u001b[39;00m\n\u001b[0;32m--> 149\u001b[0m page_data \u001b[38;5;241m=\u001b[39m \u001b[43mscrape_page\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    150\u001b[0m all_data\u001b[38;5;241m.\u001b[39mextend(page_data)\n\u001b[1;32m    152\u001b[0m \u001b[38;5;66;03m# Try to navigate to the next page\u001b[39;00m\n", "Cell \u001b[0;32mIn[2], line 80\u001b[0m, in \u001b[0;36mscrape_page\u001b[0;34m()\u001b[0m\n\u001b[1;32m     77\u001b[0m scraped_data \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m     79\u001b[0m \u001b[38;5;66;03m# Wait for the blocks to load and find them\u001b[39;00m\n\u001b[0;32m---> 80\u001b[0m blocks \u001b[38;5;241m=\u001b[39m \u001b[43mWebDriverWait\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdriver\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m10\u001b[39;49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43muntil\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     81\u001b[0m \u001b[43m    \u001b[49m\u001b[43mEC\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpresence_of_all_elements_located\u001b[49m\u001b[43m(\u001b[49m\u001b[43m(\u001b[49m\u001b[43mBy\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mXPATH\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m//*[@id=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mform1\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m]/div[4]/div[2]/div[1]/ul/li[1]\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     82\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     84\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m block \u001b[38;5;129;01min\u001b[39;00m blocks:\n\u001b[1;32m     85\u001b[0m     \u001b[38;5;66;03m# Scrape data from each block\u001b[39;00m\n\u001b[1;32m     86\u001b[0m     block_data \u001b[38;5;241m=\u001b[39m scrape_block_data(block)\n", "File \u001b[0;32m~/opt/anaconda3/envs/myenv/lib/python3.11/site-packages/selenium/webdriver/support/wait.py:105\u001b[0m, in \u001b[0;36mWebDriverWait.until\u001b[0;34m(self, method, message)\u001b[0m\n\u001b[1;32m    103\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m time\u001b[38;5;241m.\u001b[39mmonotonic() \u001b[38;5;241m>\u001b[39m end_time:\n\u001b[1;32m    104\u001b[0m         \u001b[38;5;28;01mbreak\u001b[39;00m\n\u001b[0;32m--> 105\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m TimeoutException(message, screen, stacktrace)\n", "\u001b[0;31mTimeoutException\u001b[0m: Message: \n"]}], "source": ["import logging\n", "from datetime import datetime\n", "import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.common.keys import Keys\n", "from selenium.common.exceptions import NoSuchElementException\n", "from selenium.webdriver.support.ui import Select\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "import time\n", "\n", "chrome_options = Options()\n", "chrome_options.add_argument(\"--incognito\")\n", "# chrome_options.add_argument('--headless')\n", "driver = webdriver.Chrome(options=chrome_options)\n", "\n", "# Navigate to the target URL\n", "url = 'https://tuvanduhoc.icd.edu.vn/to-chuc-tu-van-du-hoc.html'\n", "driver.get(url)\n", "\n", "def scrape_block_data(block):\n", "    \"\"\"\n", "    Scrape data from the block of content.\n", "    :param block: Web element representing a block.\n", "    :return: Dictionary containing scraped data.\n", "    \"\"\"\n", "    try:\n", "        # Extract public data from the block\n", "        comp_name = block.find_element(By.XPATH, '//*[@id=\"form1\"]/div[4]/div[2]/div[1]/ul/li[1]/div[2]/a/text()').text\n", "        publish_date = block.find_element(By.XPATH, '//*[@id=\"form1\"]/div[4]/div[2]/div[1]/ul/li[1]/div[2]/div[1]').text\n", "        \n", "        # Print the extracted data (or store it as needed)\n", "        print(f\"Comp Name: {comp_name}, Publish Date: {publish_date}\")\n", "        \n", "        # Return data for further use\n", "        return {\"Comp Name\": comp_name, \"Publish Date\": publish_date}\n", "    except Exception as e:\n", "        print(f\"Error scraping block: {e}\")\n", "        return None\n", "\n", "def scrape_details_from_block(block):\n", "    \"\"\"\n", "    Click on the block to extract detailed information.\n", "    :param block: Web element representing the clickable block.\n", "    :return: Detailed information from the new page.\n", "    \"\"\"\n", "    try:\n", "        # Click the block to load detailed information\n", "        block.click()\n", "        \n", "        # Wait for the detail page to load and scrape the details\n", "        active = WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located((By.XPATH, '//*[@id=\"form1\"]/div[4]/div[2]/div[1]/div[3]/b'))\n", "        ).text\n", "        print(f\"Active: {active}\")\n", "\n", "        address = WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located((By.XPATH, '//*[@id=\"form1\"]/div[4]/div[2]/div[1]/div[7]'))\n", "        ).text\n", "        print(f\"Address: {address}\")\n", "        \n", "        # Return to the previous page to continue scraping other blocks\n", "        driver.back()\n", "        \n", "        return active, address\n", "    except Exception as e:\n", "        print(f\"Error scraping details: {e}\")\n", "        return None\n", "\n", "def scrape_page():\n", "    \"\"\"\n", "    Scrape all blocks on the current page and extract their data.\n", "    :return: List of scraped data.\n", "    \"\"\"\n", "    scraped_data = []\n", "    \n", "    # Wait for the blocks to load and find them\n", "    blocks = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located((By.XPATH, '//*[@id=\"form1\"]/div[4]/div[2]/div[1]/ul/li[1]'))\n", "    )\n", "    \n", "    for block in blocks:\n", "        # Scrape data from each block\n", "        block_data = scrape_block_data(block)\n", "        if block_data:\n", "            scraped_data.append(block_data)\n", "        \n", "        # Scrape details by clicking the block\n", "        active, address = scrape_details_from_block(block)\n", "        if active and address:\n", "            block_data[\"Active\"] = active\n", "            block_data[\"Address\"] = address\n", "    \n", "    return scraped_data\n", "\n", "def go_to_next_page(current_page_num):\n", "    \"\"\"\n", "    Navigate to the next page based on the 'class' and 'title' attributes.\n", "    The current page has class 'hientai', and other pages have class 'trangkhac' with the 'title' as the next page number.\n", "    :param current_page_num: The current page number (int).\n", "    :return: True if the next page is found and clicked, else False.\n", "    \"\"\"\n", "    try:\n", "        # The next page number is one more than the current page\n", "        next_page_num = current_page_num + 1\n", "        \n", "        # XPath to find the page element with the next page number and class 'trangkhac'\n", "        next_page_xpath = f'//*[@class=\"trangkhac\" and @title=\"{next_page_num}\"]'\n", "        \n", "        # Wait for the element to be clickable and click on it\n", "        next_page = WebDriverWait(driver, 5).until(\n", "            EC.element_to_be_clickable((By.XPATH, next_page_xpath))\n", "        )\n", "        next_page.click()\n", "        return True\n", "    except Exception as e:\n", "        print(f\"No more pages or error navigating to next page: {e}\")\n", "        return False\n", "\n", "def handle_pop_up():\n", "    \"\"\"\n", "    Handle the pop-up if found on the page by clicking the close button.\n", "    :return: True if the pop-up was handled, else False.\n", "    \"\"\"\n", "    try:\n", "        # Find the button inside the pop-up using the given XPath and click it\n", "        pop_up_button = WebDriverWait(driver, 10).until(\n", "            EC.element_to_be_clickable((By.XPATH, '//*[@id=\"rXOa8\"]/div/label/input')) # //*[@id=\"rXOa8\"]/div/label/input\n", "        )\n", "        pop_up_button.click()\n", "        print(\"Pop-up closed successfully.\")\n", "        return True\n", "    except Exception as e:\n", "        # No pop-up found or error while handling it\n", "        print(f\"No pop-up or error handling pop-up: {e}\")\n", "        return False\n", "\n", "# Main crawling loop\n", "all_data = []\n", "current_page = 1\n", "\n", "while True:\n", "    # Handle pop-up if it appears\n", "    handle_pop_up()\n", "\n", "    # Scrape the current page after handling any pop-ups\n", "    page_data = scrape_page()\n", "    all_data.extend(page_data)\n", "    \n", "    # Try to navigate to the next page\n", "    if not go_to_next_page(current_page):\n", "        break\n", "    current_page += 1\n", "\n", "# Once done, close the WebDriver\n", "driver.quit()\n", "\n", "# Print or save the final scraped data\n", "print(all_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}