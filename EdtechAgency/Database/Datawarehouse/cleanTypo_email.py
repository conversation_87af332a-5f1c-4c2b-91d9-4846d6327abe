# import pandas as pd
# import re

# # Sample data for testing
# data = {'emails': [
#     '<EMAIL> - <EMAIL> - <EMAIL>',
#     'invalid-email@@domain.com - user4@domain',
#     '<EMAIL> - user6@co',
#     '-<EMAIL>-',
#     'user8@gmailcom - - - user9@',
#     '<EMAIL>',
#     '<EMAIL>'
# ]}

# # Create DataFrame
# df = pd.DataFrame(data)

# # Regex pattern for a valid email
# email_pattern = re.compile(r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$')

# # Function to validate individual emails
# def validate_email(email):
#     # Check for missing '@' or domain issues
#     if not re.match(email_pattern, email):
#         return False
#     return True

# # Optional: Define a function to fix common typos (e.g., .cm to .com)
# # Fix common typos, like replacing '.cm' with '.com' without over-replacing '.com'
# def fix_common_typos(email):
#     # Remove multiple '@' and keep only the first one
#     if email.count('@') > 1:
#         email = email.split('@', 1)[0] + '@' + email.split('@', 1)[1].replace('@', '')

#     # Fix missing ".com" in domain names
#     if email.endswith('@domain'):  # Specific to the "domain" case
#         email += '.com'
    
#     # Fix missing dot in ".com"
#     if 'gmailcom' in email:
#         email = email.replace('gmailcom', 'gmail.com')
    
    # # Replace '.cm' only when it's at the end of the email
    # if email.endswith('.cm'):
    #     email = email.replace('.cm', '.com')
    
    # # Replace '.co' with '.com', ensuring it's not already '.com'
    # if email.endswith('.co'):
    #     email = email.replace('.co', '.com')
    
    # # Handle ".con" typos
    # if email.endswith('.con'):
    #     email = email.replace('.con', '.com')

    # if email.endswith('@.gmail.com'):
    #     email = email.replace('@.gmail.com', '@gmail.com')

    # if email.endswith(',com'):
    #     email = email.replace(',com', '.com')

    # if email.endswith('gmaill.com'):
    #     email = email.replace('gmaill.com', 'gmail.com')

    # if email.endswith('gmil.com'):
    #     email = email.replace('gmil.com', 'gmail.com')

    # if email.endswith('gmai.com'):
    #     email = email.replace('gmai.com', 'gmail.com')

    # if email.endswith('yahô.com'):
    #     email = email.replace('yahô.com', 'yahoo.com')

    # if email.endswith('yanhoo.com'):
    #     email = email.replace('yanhoo.com', 'yahoo.com')

    # if email.endswith('gamil.com'):
    #     email = email.replace('gamil.com', 'gmail.com')
    
    # if 'gmailk' in email:
    #     email = email.replace('gmailk', 'gmail')

    # if '.con' in email:
    #     email = email.replace('.con', '.com')

    # if '.cm' in email:
    #     email = email.replace('.cm', '.com')

    # if '.vom' in email:
    #     email = email.replace('.vom', '.com')
    
    # if '.ocm' in email:
    #     email = email.replace('.ocm', '.com')
    
    # if email.endswith('.co'):
    #     email = email.replace('.co', '.com')
    
    # if '. Com' in email:
    #     email = email.replace('. Com', '.com')
    
    # if email.endswith('gmail'):
    #     email = email.replace('gmail', 'gmail.com')
    
    # if email.endswith('.ed.vn'):
    #     email = email.replace('.ed.vn', '.edu.vn')
    
    # if 'gmal' in email:
    #     email = email.replace('gmal', 'gmail')
    
    # if email.endswith('.com.'):
    #     email = email.replace('.com.', '.com')
    
    # if email.endswith('.comn'):
    #     email = email.replace('.comn', '.com')
    
    # if 'gmail..com' in email:
    #     email = email.replace('gmail..com', 'gmail.com')
    
    # if 'hanoieduvn' in email:
    #     email = email.replace('hanoieduvn', 'hanoiedu.vn')
    
    # if 'hanoiưdu' in email:
    #     email = email.replace('hanoiưdu', 'hanoiedu')
    
    # if '"gmail' in email:
    #     email = email.replace('"gmail', '@gmail')
    
    # if email.endswith('yahoocomvn'):
    #     email = email.replace('yahoocomvn', 'yahoo.com.vn')
    
    # if email.endswith('tayho,edu,vn'):
    #     email = email.replace('tayho,edu,vn', 'tayho.edu.vn')

#     return email

# # Function to clean the email column
# def clean_emails(email_cell):
#     # Split emails by '-' and remove empty spaces
#     emails = [email.strip() for email in email_cell.split('-') if email.strip()]
    
#     # Initialize a list to store cleaned emails
#     cleaned_emails = []
    
#     for email in emails:
#         email = fix_common_typos(email)  # Optional: Fix typos before validation
#         if validate_email(email):
#             cleaned_emails.append(email)
#         else:
#             # Handle special cases here (custom rules)
#             # You can print a message or flag invalid emails here if needed
#             print(f"Invalid email found: {email}")
    
#     # Join valid emails back into a single string separated by ' - '
#     return ' - '.join(cleaned_emails)

# # Apply the cleaning function to the 'emails' column
# df['cleaned_emails'] = df['emails'].apply(clean_emails)

# # Display the cleaned DataFrame
# print(df[['emails', 'cleaned_emails']])
# # print(df.iloc[0,1])









import pandas as pd
import re

# Load the Excel file
df = pd.read_excel('/Users/<USER>/Downloads/DW-Full.xlsx')

# Updated regex pattern to match valid email structure within a string
email_pattern = re.compile(r'[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+')

# Function to fix common typos and structural issues
def fix_common_typos(email):
    if email is None:
        return None

    # Extract valid email part from any extra symbols like <>, (), etc.
    match = email_pattern.search(email)
    if match:
        email = match.group(0)  # Only keep the valid email part

    # Remove multiple '@' and keep only the first one
    if email.count('@') > 1:
        parts = email.split('@')
        email = parts[0] + '@' + ''.join(parts[1:])  # Keep only first '@'

    # Common typo corrections
    corrections = {
        '@domain': '.com',
        'gmailcom': 'gmail.com',
        '.cm': '.com',
        'gamil.com': 'gmail.com',
        'yahoocom': 'yahoo.com',
        'gmai.com': 'gmail.com',
        'yahoooo.com': 'yahoo.com',
        'gmal': 'gmail',
        'hanoieduvn': 'hanoiedu.vn',
        'tayho,edu,vn': 'tayho.edu.vn',
        'gmaill.com': 'gmail.com',
        'gmil.com': 'gmail.com',
        'yahô.com': 'yahoo.com',
        'yanhoo.com': 'yahoo.com',
        'gmailk': 'gmail',
        'gmail..com': 'gmail.com',
        'hanoiưdu': 'hanoiedu',
        'gmaill.com': 'gmail.com',
        'nocomment.con': 'nocomment.com',
        'gmaill@com': 'gmail.com',
        'yahoocomvn': 'yahoo.com.vn',
        'gmail. Com': 'gmail.com',
        'gmail,com': 'gmail.com',
        '@.gmail': '@gmail',
        '"gmail': '@gmail',
        'Gmail': 'gmail',
        '.con': '.com'
    }

    # Apply all corrections
    for wrong, right in corrections.items():
        email = email.replace(wrong, right)

    # Clean extra spaces and leading/trailing hyphens
    email = email.strip().strip('-')

    # Clean spaces within the email
    email = ''.join(email.split())

    # Convert to lowercase if the entire email is in uppercase
    if email.isupper():
        email = email.lower()

    # Final validation after all corrections
    if email.endswith('.'):
        email = email[:-1]  # Remove trailing dot if present
    
    if email.endswith('gmail'):
        email = email.replace('gmail','gmail.com')

    return email

# Function to validate email structure but apply typo fixes for invalid ones
def validate_email(email):
    if email is None or not isinstance(email, str):
        return None, False  # Return None and False for invalid email input

    # Apply typo fixes first
    email = fix_common_typos(email)

    # Check for valid structure after typo fixes
    if re.match(email_pattern, email):
        return email, True  # Valid email

    # Even if invalid, return the cleaned email (typos fixed) and mark as invalid
    return email, False

# Function to clean the email column and return both cleaned emails and validation flag
def clean_emails(email_cell):
    # Check if the email_cell is a string
    if isinstance(email_cell, str):
        # Split by ' - ' if multiple emails are separated
        emails = [email.strip() for email in email_cell.split(' - ') if email.strip()]
        
        # Initialize a list to store cleaned emails and a validation flag
        cleaned_emails = []
        is_valid = True  # Assume all are valid initially

        for email in emails:
            # Validate and clean the email
            cleaned_email, valid = validate_email(email)
            
            # If any email is invalid, set the overall validity to False
            if not valid:
                is_valid = False

            # Add the cleaned email to the list
            cleaned_emails.append(cleaned_email)
        
        # Join cleaned emails back into a single string
        return ' - '.join(cleaned_emails), is_valid
    else:
        # If email_cell is not a string (e.g., NaN), return None and False
        return None, False

# Apply the cleaning function to the 'Email' column
df['Cleaned_Emails'], df['validate'] = zip(*df['Email'].apply(clean_emails))

# Display the cleaned DataFrame
# print(df[['Email', 'Cleaned_Emails']].sample(300))
df.to_excel('/Users/<USER>/Downloads/Cleaned_Emails5.xlsx', index=False)