# import streamlit as st
# import gspread
# from oauth2client.service_account import ServiceAccountCredentials
# from datetime import datetime
# import re
# import smtplib
# from email.mime.multipart import MIMEMultipart
# from email.mime.text import MIMEText
# import logging
# import uuid
# import json
# import pandas as pd
# import plotly.express as px
# import hashlib
# import time
# import os
# import sqlite3
# from gspread.exceptions import APIError
# import backoff

# # Set up logging with a timestamp for each log entry
# logging.basicConfig(
#     filename='form.log',
#     level=logging.INFO,
#     format='%(asctime)s - %(levelname)s - %(message)s',
#     datefmt='%Y-%m-%d %H:%M:%S'
# )

# # Log helper function
# def log_event(message):
#     logging.info(message)
#     st.session_state.latest_logs.append({"timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"), "message": message})
#     # Keep only the last 100 logs in memory
#     if len(st.session_state.latest_logs) > 100:
#         st.session_state.latest_logs = st.session_state.latest_logs[-100:]

# # Database setup for local storage fallback
# def setup_local_db():
#     """Set up SQLite database for local storage when Google Sheets is unavailable"""
#     try:
#         # Create data directory if it doesn't exist
#         if not os.path.exists('data'):
#             os.makedirs('data')
        
#         conn = sqlite3.connect('data/form_data.db')
#         cursor = conn.cursor()
        
#         # Create tables if they don't exist
#         cursor.execute('''
#         CREATE TABLE IF NOT EXISTS form_submissions (
#             id INTEGER PRIMARY KEY AUTOINCREMENT,
#             name TEXT,
#             company TEXT,
#             role TEXT,
#             phone_no TEXT,
#             email TEXT,
#             sentiment INTEGER,
#             submission_time TIMESTAMP
#         )
#         ''')
        
#         cursor.execute('''
#         CREATE TABLE IF NOT EXISTS user_analytics (
#             id INTEGER PRIMARY KEY AUTOINCREMENT,
#             timestamp TIMESTAMP,
#             session_id TEXT,
#             user_id TEXT,
#             event_type TEXT,
#             event_details TEXT,
#             duration REAL,
#             form_completion TEXT,
#             browser TEXT,
#             device_type TEXT,
#             referrer TEXT
#         )
#         ''')
        
#         cursor.execute('''
#         CREATE TABLE IF NOT EXISTS user_sessions (
#             id INTEGER PRIMARY KEY AUTOINCREMENT,
#             session_id TEXT,
#             user_id TEXT,
#             start_time TIMESTAMP,
#             end_time TIMESTAMP,
#             duration REAL,
#             page_views INTEGER,
#             form_submitted TEXT,
#             browser TEXT,
#             device_type TEXT,
#             referrer TEXT
#         )
#         ''')
        
#         cursor.execute('''
#         CREATE TABLE IF NOT EXISTS user_retention (
#             id INTEGER PRIMARY KEY AUTOINCREMENT,
#             user_id TEXT UNIQUE,
#             email TEXT,
#             first_visit TIMESTAMP,
#             last_visit TIMESTAMP,
#             visit_count INTEGER,
#             form_submissions INTEGER,
#             average_form_rating REAL,
#             last_form_rating INTEGER
#         )
#         ''')
        
#         conn.commit()
#         conn.close()
#         log_event("Local database setup successfully")
#         return True
#     except Exception as e:
#         log_event(f"Error setting up local database: {str(e)}")
#         return False

# # Function to store form submission in local database
# def store_form_local(data):
#     """Store form submission in local SQLite database"""
#     try:
#         conn = sqlite3.connect('data/form_data.db')
#         cursor = conn.cursor()
        
#         # Insert form data
#         cursor.execute('''
#         INSERT INTO form_submissions 
#         (name, company, role, phone_no, email, sentiment, submission_time)
#         VALUES (?, ?, ?, ?, ?, ?, ?)
#         ''', (
#             data['name'],
#             data['company'],
#             data['role'],
#             data['phoneNo'],
#             data['email'],
#             data['sentiment'],
#             datetime.now().strftime("%Y-%m-%d %H:%M:%S")
#         ))
        
#         conn.commit()
#         conn.close()
#         log_event(f"Form data stored locally for {data['email']}")
#         return True
#     except Exception as e:
#         log_event(f"Error storing form data locally: {str(e)}")
#         return False

# # Function to store analytics data in local database
# def store_analytics_local(data):
#     """Store analytics data in local SQLite database"""
#     try:
#         conn = sqlite3.connect('data/form_data.db')
#         cursor = conn.cursor()
        
#         # Insert analytics data
#         cursor.execute('''
#         INSERT INTO user_analytics 
#         (timestamp, session_id, user_id, event_type, event_details, 
#         duration, form_completion, browser, device_type, referrer)
#         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
#         ''', (
#             data.get('timestamp', datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
#             data.get('session_id', "unknown"),
#             data.get('user_id', "anonymous"),
#             data.get('event_type', "unknown"),
#             data.get('event_details', ""),
#             data.get('duration', 0),
#             data.get('form_completion', "No"),
#             data.get('browser', "Unknown"),
#             data.get('device_type', "Unknown"),
#             data.get('referrer', "Direct")
#         ))
        
#         conn.commit()
#         conn.close()
#         return True
#     except Exception as e:
#         log_event(f"Error storing analytics data locally: {str(e)}")
#         return False

# # Function to store session data in local database
# def store_session_local(data):
#     """Store session data in local SQLite database"""
#     try:
#         conn = sqlite3.connect('data/form_data.db')
#         cursor = conn.cursor()
        
#         # Insert session data
#         cursor.execute('''
#         INSERT INTO user_sessions 
#         (session_id, user_id, start_time, end_time, duration, 
#         page_views, form_submitted, browser, device_type, referrer)
#         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
#         ''', (
#             data.get('session_id', "unknown"),
#             data.get('user_id', "anonymous"),
#             data.get('start_time', datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
#             data.get('end_time', datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
#             data.get('duration', 0),
#             data.get('page_views', 0),
#             data.get('form_submitted', "No"),
#             data.get('browser', "Unknown"),
#             data.get('device_type', "Unknown"),
#             data.get('referrer', "Direct")
#         ))
        
#         conn.commit()
#         conn.close()
#         return True
#     except Exception as e:
#         log_event(f"Error storing session data locally: {str(e)}")
#         return False

# # Function to update user retention data in local database
# def update_retention_local(email, form_rating=None):
#     """Update user retention data in local SQLite database"""
#     if not email:
#         return False
    
#     try:
#         user_id = generate_user_id(email)
#         conn = sqlite3.connect('data/form_data.db')
#         cursor = conn.cursor()
        
#         # Check if user exists
#         cursor.execute('SELECT * FROM user_retention WHERE user_id = ?', (user_id,))
#         user_exists = cursor.fetchone()
#         current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
#         if user_exists:
#             # Update existing user
#             if form_rating:
#                 # Calculate new average rating
#                 cursor.execute('''
#                 SELECT visit_count, form_submissions, average_form_rating 
#                 FROM user_retention WHERE user_id = ?
#                 ''', (user_id,))
#                 row = cursor.fetchone()
#                 visit_count = row[0] + 1
#                 form_submissions = row[1] + 1
#                 avg_rating = row[2] or 0
                
#                 if form_submissions > 1:
#                     new_avg_rating = ((avg_rating * (form_submissions - 1)) + form_rating) / form_submissions
#                 else:
#                     new_avg_rating = form_rating
                
#                 cursor.execute('''
#                 UPDATE user_retention SET 
#                 last_visit = ?, 
#                 visit_count = ?, 
#                 form_submissions = ?, 
#                 average_form_rating = ?, 
#                 last_form_rating = ?
#                 WHERE user_id = ?
#                 ''', (
#                     current_time, 
#                     visit_count, 
#                     form_submissions, 
#                     round(new_avg_rating, 2),
#                     form_rating,
#                     user_id
#                 ))
#             else:
#                 # Just update visit count
#                 cursor.execute('''
#                 UPDATE user_retention SET 
#                 last_visit = ?, 
#                 visit_count = visit_count + 1
#                 WHERE user_id = ?
#                 ''', (current_time, user_id))
#         else:
#             # Add new user
#             cursor.execute('''
#             INSERT INTO user_retention 
#             (user_id, email, first_visit, last_visit, visit_count, 
#             form_submissions, average_form_rating, last_form_rating)
#             VALUES (?, ?, ?, ?, ?, ?, ?, ?)
#             ''', (
#                 user_id,
#                 email,
#                 current_time,
#                 current_time,
#                 1,
#                 1 if form_rating else 0,
#                 form_rating if form_rating else None,
#                 form_rating if form_rating else None
#             ))
        
#         conn.commit()
#         conn.close()
#         return True
#     except Exception as e:
#         log_event(f"Error updating retention data locally: {str(e)}")
#         return False

# # Function to get analytics data from local database
# def get_analytics_data_local():
#     """Get analytics data from local SQLite database"""
#     try:
#         conn = sqlite3.connect('data/form_data.db')
#         conn.row_factory = sqlite3.Row
#         cursor = conn.cursor()
        
#         cursor.execute('SELECT * FROM user_analytics')
#         rows = cursor.fetchall()
        
#         # Convert to list of dicts
#         data = [dict(row) for row in rows]
#         conn.close()
#         return data
#     except Exception as e:
#         log_event(f"Error getting analytics data from local database: {str(e)}")
#         return []

# # Function to get retention data from local database
# def get_retention_data_local():
#     """Get retention data from local SQLite database"""
#     try:
#         conn = sqlite3.connect('data/form_data.db')
#         conn.row_factory = sqlite3.Row
#         cursor = conn.cursor()
        
#         cursor.execute('SELECT * FROM user_retention')
#         rows = cursor.fetchall()
        
#         # Convert to list of dicts
#         data = [dict(row) for row in rows]
#         conn.close()
#         return data
#     except Exception as e:
#         log_event(f"Error getting retention data from local database: {str(e)}")
#         return []

# # Function to get session data from local database
# def get_sessions_data_local():
#     """Get session data from local SQLite database"""
#     try:
#         conn = sqlite3.connect('data/form_data.db')
#         conn.row_factory = sqlite3.Row
#         cursor = conn.cursor()
        
#         cursor.execute('SELECT * FROM user_sessions')
#         rows = cursor.fetchall()
        
#         # Convert to list of dicts
#         data = [dict(row) for row in rows]
#         conn.close()
#         return data
#     except Exception as e:
#         log_event(f"Error getting session data from local database: {str(e)}")
#         return []

# # Google Sheets connection with retry logic
# @backoff.on_exception(backoff.expo, 
#                      (APIError, ConnectionError), 
#                      max_tries=3,
#                      jitter=None)
# def connect_to_google_sheets():
#     """Connect to Google Sheets with retry logic and error handling"""
#     try:
#         # Check if we need to restore service account from session state
#         if 'service_account_json' in st.session_state and st.session_state.service_account_json:
#             # Save to temporary file
#             with open('temp_service_account.json', 'w') as f:
#                 json.dump(st.session_state.service_account_json, f)
            
#             # Use the temporary file for auth
#             scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
#             creds = ServiceAccountCredentials.from_json_keyfile_name('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/.streamlit/ea-database-form-876356d31535.json', scope)
#             client = gspread.authorize(creds)
            
#             # Test connection
#             # Just get the list of spreadsheets to verify connection works
#             client.list_spreadsheet_files()
            
#             log_event("Successfully connected to Google Sheets API")
#             st.session_state.google_sheets_available = True
#             return client
#         else:
#             # Try regular connection from file
#             try:
#                 scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
#                 creds = ServiceAccountCredentials.from_json_keyfile_name('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/.streamlit/ea-database-form-876356d31535.json', scope)
#                 client = gspread.authorize(creds)
                
#                 # Test connection
#                 client.list_spreadsheet_files()
                
#                 log_event("Successfully connected to Google Sheets API")
#                 st.session_state.google_sheets_available = True
#                 return client
#             except Exception as e:
#                 log_event(f"Error connecting with service account file: {str(e)}")
#                 st.session_state.google_sheets_available = False
#                 return None
#     except Exception as e:
#         log_event(f"Error connecting to Google Sheets: {str(e)}")
#         st.session_state.google_sheets_available = False
#         return None

# # Initialize global variables and session state
# def initialize_app():
#     """Initialize app state and variables"""
#     # Initialize session state variables
#     if 'session_id' not in st.session_state:
#         st.session_state.session_id = str(uuid.uuid4())
    
#     if 'session_start' not in st.session_state:
#         st.session_state.session_start = datetime.now()
        
#     if 'interactions' not in st.session_state:
#         st.session_state.interactions = []
        
#     if 'user_id' not in st.session_state:
#         st.session_state.user_id = None
    
#     if 'google_sheets_available' not in st.session_state:
#         st.session_state.google_sheets_available = False
    
#     if 'latest_logs' not in st.session_state:
#         st.session_state.latest_logs = []
    
#     if 'is_local_db_setup' not in st.session_state:
#         st.session_state.is_local_db_setup = setup_local_db()
    
#     # Try to connect to Google Sheets
#     if 'client' not in st.session_state:
#         st.session_state.client = connect_to_google_sheets()
    
#     # Use the client from session state
#     global client
#     client = st.session_state.client

# # Session tracking initialization
# def initialize_session():
#     """Initialize session tracking"""
#     # Already set up in initialize_app(), just track page load
#     track_interaction("page_load", "Session initialized")

# # Create or get an analytics worksheet with fallback
# def get_analytics_sheet():
#     """Get analytics worksheet with local fallback"""
#     if not st.session_state.google_sheets_available or not client:
#         log_event("Using local storage for analytics (Google Sheets unavailable)")
#         return None
    
#     try:
#         spreadsheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')
#         analytics_title = 'User_Analytics'
        
#         try:
#             worksheet = spreadsheet.worksheet(analytics_title)
#         except gspread.exceptions.WorksheetNotFound:
#             worksheet = spreadsheet.add_worksheet(title=analytics_title, rows="1000", cols="10")
#             # Set header row
#             worksheet.append_row([
#                 "Timestamp", "SessionID", "UserID", "EventType", 
#                 "EventDetails", "Duration", "FormCompletion", 
#                 "Browser", "DeviceType", "Referrer"
#             ])
        
#         return worksheet
#     except Exception as e:
#         log_event(f"Error accessing analytics sheet: {str(e)}")
#         return None

# # Create a sessions worksheet with fallback
# def get_sessions_sheet():
#     """Get sessions worksheet with local fallback"""
#     if not st.session_state.google_sheets_available or not client:
#         log_event("Using local storage for sessions (Google Sheets unavailable)")
#         return None
    
#     try:
#         spreadsheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')
#         sessions_title = 'User_Sessions'
        
#         try:
#             worksheet = spreadsheet.worksheet(sessions_title)
#         except gspread.exceptions.WorksheetNotFound:
#             worksheet = spreadsheet.add_worksheet(title=sessions_title, rows="1000", cols="10")
#             # Set header row
#             worksheet.append_row([
#                 "SessionID", "UserID", "StartTime", "EndTime", 
#                 "Duration", "PageViews", "FormSubmitted", 
#                 "Browser", "DeviceType", "Referrer"
#             ])
        
#         return worksheet
#     except Exception as e:
#         log_event(f"Error accessing sessions sheet: {str(e)}")
#         return None

# # Get retention worksheet with fallback
# def get_retention_sheet():
#     """Get retention worksheet with local fallback"""
#     if not st.session_state.google_sheets_available or not client:
#         log_event("Using local storage for retention (Google Sheets unavailable)")
#         return None
    
#     try:
#         spreadsheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')
#         retention_title = 'User_Retention'
        
#         try:
#             worksheet = spreadsheet.worksheet(retention_title)
#         except gspread.exceptions.WorksheetNotFound:
#             worksheet = spreadsheet.add_worksheet(title=retention_title, rows="1000", cols="8")
#             # Set header row
#             worksheet.append_row([
#                 "UserID", "Email", "FirstVisit", "LastVisit", 
#                 "VisitCount", "FormSubmissions", "AverageFormRating", 
#                 "LastFormRating"
#             ])
        
#         return worksheet
#     except Exception as e:
#         log_event(f"Error accessing retention sheet: {str(e)}")
#         return None

# # Record user interaction with fallback
# def track_interaction(event_type, event_details=""):
#     """Track user interaction with local fallback"""
#     if 'interactions' in st.session_state:
#         timestamp = datetime.now()
#         st.session_state.interactions.append({
#             "timestamp": timestamp,
#             "event_type": event_type,
#             "event_details": event_details
#         })
        
#         # Get browser and device info (simulation since Streamlit doesn't expose this directly)
#         browser = "Unknown"
#         device_type = "Desktop"  # Default assumption
#         referrer = "Direct"
        
#         # Prepare data for storage
#         data = {
#             "timestamp": timestamp.strftime("%Y-%m-%d %H:%M:%S"),
#             "session_id": st.session_state.session_id,
#             "user_id": st.session_state.user_id if st.session_state.user_id else "Anonymous",
#             "event_type": event_type,
#             "event_details": event_details,
#             "duration": (timestamp - st.session_state.session_start).total_seconds(),
#             "form_completion": "No" if event_type != "form_submitted" else "Yes",
#             "browser": browser,
#             "device_type": device_type,
#             "referrer": referrer
#         }
        
#         # Try Google Sheets first
#         analytics_sheet = get_analytics_sheet()
#         if analytics_sheet:
#             try:
#                 analytics_sheet.append_row([
#                     data["timestamp"],
#                     data["session_id"],
#                     data["user_id"],
#                     data["event_type"],
#                     data["event_details"],
#                     data["duration"],
#                     data["form_completion"],
#                     data["browser"],
#                     data["device_type"],
#                     data["referrer"]
#                 ])
#             except Exception as e:
#                 log_event(f"Error adding to analytics sheet: {str(e)}")
#                 # Fallback to local storage
#                 store_analytics_local(data)
#         else:
#             # Use local storage
#             store_analytics_local(data)

# # Generate a user ID from email (hashed for privacy)
# def generate_user_id(email):
#     """Generate a user ID from email (hashed for privacy)"""
#     if not email:
#         return None
#     return hashlib.md5(email.lower().encode()).hexdigest()

# # Update user retention data with fallback
# def update_retention(email, form_rating=None):
#     """Update user retention data with local fallback"""
#     if not email:
#         return
    
#     user_id = generate_user_id(email)
#     st.session_state.user_id = user_id
    
#     # Try Google Sheets first
#     retention_sheet = get_retention_sheet()
#     if retention_sheet:
#         try:
#             retention_data = retention_sheet.get_all_records()
            
#             # Check if user exists
#             user_exists = False
#             row_index = 2  # Accounting for header row
            
#             for i, row in enumerate(retention_data):
#                 if row.get("UserID") == user_id:
#                     user_exists = True
#                     row_index = i + 2  # +2 for header row and 0-indexing
#                     break
            
#             current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
#             if user_exists:
#                 # Update existing user
#                 user_row = retention_data[row_index - 2]  # Adjust index
#                 visit_count = int(user_row.get("VisitCount", 0)) + 1
#                 form_submissions = int(user_row.get("FormSubmissions", 0)) + 1
                
#                 # Calculate average rating
#                 if form_rating:
#                     avg_rating = float(user_row.get("AverageFormRating", 0)) if user_row.get("AverageFormRating") else 0
#                     total_submissions = form_submissions - 1  # Exclude current submission
                    
#                     if total_submissions > 0:
#                         new_avg_rating = ((avg_rating * total_submissions) + form_rating) / form_submissions
#                     else:
#                         new_avg_rating = form_rating
#                 else:
#                     new_avg_rating = float(user_row.get("AverageFormRating", 0)) if user_row.get("AverageFormRating") else 0
                
#                 # Update the row
#                 retention_sheet.update(f'D{row_index}', current_time)  # LastVisit
#                 retention_sheet.update(f'E{row_index}', visit_count)  # VisitCount
#                 retention_sheet.update(f'F{row_index}', form_submissions)  # FormSubmissions
#                 retention_sheet.update(f'G{row_index}', round(new_avg_rating, 2))  # AverageFormRating
                
#                 if form_rating:
#                     retention_sheet.update(f'H{row_index}', form_rating)  # LastFormRating
#             else:
#                 # Add new user
#                 retention_sheet.append_row([
#                     user_id,
#                     email,
#                     current_time,  # FirstVisit
#                     current_time,  # LastVisit
#                     1,  # VisitCount
#                     1 if form_rating else 0,  # FormSubmissions
#                     form_rating if form_rating else "",  # AverageFormRating
#                     form_rating if form_rating else ""   # LastFormRating
#                 ])
#         except Exception as e:
#             log_event(f"Error updating retention sheet: {str(e)}")
#             # Fallback to local storage
#             update_retention_local(email, form_rating)
#     else:
#         # Use local storage
#         update_retention_local(email, form_rating)

# # End session tracking with fallback
# def end_session():
#     """End session tracking with local fallback"""
#     if 'session_start' in st.session_state and 'session_id' in st.session_state:
#         end_time = datetime.now()
#         duration = (end_time - st.session_state.session_start).total_seconds()
        
#         # Count page views from interactions
#         page_views = sum(1 for interaction in st.session_state.interactions 
#                          if interaction["event_type"] == "page_view" or
#                             interaction["event_type"] == "page_load")
        
#         # Check if form was submitted
#         form_submitted = any(interaction["event_type"] == "form_submitted" 
#                              for interaction in st.session_state.interactions)
        
#         # Prepare data for storage
#         data = {
#             "session_id": st.session_state.session_id,
#             "user_id": st.session_state.user_id if st.session_state.user_id else "Anonymous",
#             "start_time": st.session_state.session_start.strftime("%Y-%m-%d %H:%M:%S"),
#             "end_time": end_time.strftime("%Y-%m-%d %H:%M:%S"),
#             "duration": round(duration, 2),
#             "page_views": page_views,
#             "form_submitted": "Yes" if form_submitted else "No",
#             "browser": "Unknown",
#             "device_type": "Desktop",
#             "referrer": "Direct"
#         }
        
#         # Try Google Sheets first
#         sessions_sheet = get_sessions_sheet()
#         if sessions_sheet:
#             try:
#                 sessions_sheet.append_row([
#                     data["session_id"],
#                     data["user_id"],
#                     data["start_time"],
#                     data["end_time"],
#                     data["duration"],
#                     data["page_views"],
#                     data["form_submitted"],
#                     data["browser"],
#                     data["device_type"],
#                     data["referrer"]
#                 ])
#             except Exception as e:
#                 log_event(f"Error adding to sessions sheet: {str(e)}")
#                 # Fallback to local storage
#                 store_session_local(data)
#         else:
#             # Use local storage
#             store_session_local(data)
        
#         # Clear session state
#         st.session_state.interactions = []
#         st.session_state.session_start = datetime.now()
#         st.session_state.session_id = str(uuid.uuid4())
#         # Keep user_id for returning users

# # Generate analytics dashboard with fallback
# def show_analytics_dashboard():
#     """Generate analytics dashboard with data from either Google Sheets or local storage"""
#     st.title("User Analytics Dashboard")
    
#     # Display storage status
#     if st.session_state.google_sheets_available:
#         st.success("✅ Google Sheets integration is active")
#     else:
#         st.warning("⚠️ Using local storage (Google Sheets unavailable)")
    
#     # Get data for analysis
#     analytics_data = []
#     retention_data = []
#     sessions_data = []
    
#     if st.session_state.google_sheets_available:
#         # Try to get data from Google Sheets
#         analytics_sheet = get_analytics_sheet()
#         if analytics_sheet:
#             try:
#                 analytics_data = analytics_sheet.get_all_records()
#             except Exception as e:
#                 log_event(f"Error reading analytics data from Google Sheets: {str(e)}")
        
#         retention_sheet = get_retention_sheet()
#         if retention_sheet:
#             try:
#                 retention_data = retention_sheet.get_all_records()
#             except Exception as e:
#                 log_event(f"Error reading retention data from Google Sheets: {str(e)}")
        
#         sessions_sheet = get_sessions_sheet()
#         if sessions_sheet:
#             try:
#                 sessions_data = sessions_sheet.get_all_records()
#             except Exception as e:
#                 log_event(f"Error reading sessions data from Google Sheets: {str(e)}")
    
#     # If data is empty or Google Sheets is unavailable, use local data
#     if not analytics_data:
#         analytics_data = get_analytics_data_local()
    
#     if not retention_data:
#         retention_data = get_retention_data_local()
    
#     if not sessions_data:
#         sessions_data = get_sessions_data_local()
    
#     if not analytics_data and not retention_data and not sessions_data:
#         st.info("Chưa có đủ dữ liệu để hiển thị phân tích.")
#         return
    
#     # Convert to pandas DataFrames
#     analytics_df = pd.DataFrame(analytics_data)
#     retention_df = pd.DataFrame(retention_data)
#     sessions_df = pd.DataFrame(sessions_data)
    
#     # Add timestamp columns
#     if not analytics_df.empty and 'Timestamp' in analytics_df.columns:
#         analytics_df['Timestamp'] = pd.to_datetime(analytics_df['Timestamp'])
    
#     if not retention_df.empty:
#         if 'FirstVisit' in retention_df.columns:
#             retention_df['FirstVisit'] = pd.to_datetime(retention_df['FirstVisit'])
        
#         if 'LastVisit' in retention_df.columns:
#             retention_df['LastVisit'] = pd.to_datetime(retention_df['LastVisit'])
    
#     if not sessions_df.empty and 'StartTime' in sessions_df.columns:
#         sessions_df['StartTime'] = pd.to_datetime(sessions_df['StartTime'])
    
#     # Key metrics
#     col1, col2, col3, col4 = st.columns(4)
    
#     with col1:
#         total_users = len(retention_df) if not retention_df.empty else 0
#         st.metric("Tổng số người dùng", total_users)
    
#     with col2:
#         total_sessions = len(sessions_df) if not sessions_df.empty else 0
#         st.metric("Tổng số phiên", total_sessions)
    
#     with col3:
#         if not sessions_df.empty and 'FormSubmitted' in sessions_df.columns:
#             form_submissions = sessions_df[sessions_df['FormSubmitted'] == 'Yes'].shape[0]
#         else:
#             form_submissions = 0
#         st.metric("Số lượt gửi form", form_submissions)
    
#     with col4:
#         if form_submissions > 0 and total_sessions > 0:
#             conversion_rate = round((form_submissions / total_sessions) * 100, 2)
#         else:
#             conversion_rate = 0
#         st.metric("Tỷ lệ chuyển đổi", f"{conversion_rate}%")
    
#     # Daily visitors chart
#     st.subheader("Lượt truy cập theo ngày")
#     if not sessions_df.empty and 'StartTime' in sessions_df.columns:
#         try:
#             daily_visits = sessions_df.resample('D', on='StartTime').size()
#             daily_visits_df = daily_visits.reset_index()
#             daily_visits_df.columns = ['Ngày', 'Số lượt truy cập']
            
#             fig = px.line(daily_visits_df, x='Ngày', y='Số lượt truy cập',
#                          title='Lượt truy cập theo ngày')
#             st.plotly_chart(fig)
#         except Exception as e:
#             st.error(f"Lỗi khi hiển thị biểu đồ truy cập: {str(e)}")
    
#     # User retention chart
#     st.subheader("Phân tích giữ chân người dùng")
    
#     if not retention_df.empty and len(retention_df) > 0 and 'FirstVisit' in retention_df.columns:
#         try:
#             # Calculate days since first visit
#             recent_date = datetime.now()
#             retention_df['DaysSinceFirstVisit'] = (recent_date - retention_df['FirstVisit']).dt.days
            
#             # Calculate days since last visit
#             if 'LastVisit' in retention_df.columns:
#                 retention_df['DaysSinceLastVisit'] = (recent_date - retention_df['LastVisit']).dt.days
            
#             # Group by days since first visit
#             retention_by_age = retention_df.groupby('DaysSinceFirstVisit').size().reset_index()
#             retention_by_age.columns = ['Số ngày kể từ lần đầu', 'Số người dùng']
            
#             fig = px.bar(retention_by_age, x='Số ngày kể từ lần đầu', y='Số người dùng',
#                         title='Phân tích người dùng theo thời gian')
#             st.plotly_chart(fig)
            
#             # Returning users
#             if 'VisitCount' in retention_df.columns:
#                 returning_users = retention_df[retention_df['VisitCount'] > 1].shape[0]
#                 returning_rate = round((returning_users / total_users) * 100, 2) if total_users > 0 else 0
                
#                 st.metric("Tỷ lệ người dùng quay lại", f"{returning_rate}%")
#         except Exception as e:
#             st.error(f"Lỗi khi hiển thị phân tích giữ chân: {str(e)}")
    
#     # Event distribution
#     st.subheader("Phân bố các sự kiện tương tác")
    
#     if not analytics_df.empty and 'EventType' in analytics_df.columns:
#         try:
#             event_counts = analytics_df['EventType'].value_counts().reset_index()
#             event_counts.columns = ['Loại sự kiện', 'Số lượng']
            
#             fig = px.pie(event_counts, values='Số lượng', names='Loại sự kiện',
#                         title='Phân bố các loại sự kiện tương tác')
#             st.plotly_chart(fig)
#         except Exception as e:
#             st.error(f"Lỗi khi hiển thị phân bố sự kiện: {str(e)}")
    
#     # Form rating distribution
#     st.subheader("Phân bố đánh giá form")
    
#     if not retention_df.empty and 'LastFormRating' in retention_df.columns:
#         try:
#             # Only include rows with ratings
#             rating_df = retention_df[retention_df['LastFormRating'].notna()]
#             if not rating_df.empty:
#                 rating_counts = rating_df['LastFormRating'].value_counts().reset_index()
#                 rating_counts.columns = ['Đánh giá', 'Số lượng']
                
#                 fig = px.bar(rating_counts, x='Đánh giá', y='Số lượng',
#                             title='Phân bố đánh giá form')
#                 st.plotly_chart(fig)
#         except Exception as e:
#             st.error(f"Lỗi khi hiển thị phân bố đánh giá: {str(e)}")
    
#     # Latest logs
#     with st.expander("Xem nhật ký hoạt động gần đây"):
#         logs_df = pd.DataFrame(st.session_state.latest_logs)
#         if not logs_df.empty:
#             st.dataframe(logs_df)
#         else:
#             st.info("Chưa có nhật ký nào được ghi lại.")

# # Setup for Google Sheets credentials
# def show_google_sheets_setup():
#     """Show Google Sheets setup interface"""
#     st.subheader("Cài đặt kết nối Google Sheets")
    
#     # Check current connection status
#     if st.session_state.google_sheets_available:
#         st.success("✅ Đã kết nối thành công với Google Sheets API")
#     else:
#         st.error("❌ Chưa kết nối được với Google Sheets API")
    
#     # Option to upload service account JSON
#     with st.expander("Tải lên tệp Service Account JSON"):
#         uploaded_file = st.file_uploader("Chọn tệp JSON chứng chỉ dịch vụ", type="json")
        
#         if uploaded_file is not None:
#             # Read and parse JSON
#             try:
#                 service_account_json = json.load(uploaded_file)
                
#                 # Verify it's a valid service account JSON
#                 required_keys = ["type", "project_id", "private_key_id", "private_key", "client_email"]
#                 if all(key in service_account_json for key in required_keys):
#                     st.session_state.service_account_json = service_account_json
                    
#                     if st.button("Kiểm tra kết nối"):
#                         # Try to connect with the new credentials
#                         st.session_state.client = connect_to_google_sheets()
                        
#                         if st.session_state.google_sheets_available:
#                             st.success("✅ Kết nối thành công!")
#                             # Update global client
#                             global client
#                             client = st.session_state.client
#                         else:
#                             st.error("❌ Kết nối thất bại. Kiểm tra lại tệp JSON và quyền truy cập.")
#                 else:
#                     st.error("Tệp không phải là chứng chỉ dịch vụ Google hợp lệ.")
#             except json.JSONDecodeError:
#                 st.error("Không thể đọc tệp JSON. Định dạng không hợp lệ.")
    
#     # Instructions for creating a service account
#     with st.expander("Hướng dẫn tạo Service Account"):
#         st.markdown("""
#         ### Các bước tạo Service Account cho Google Sheets API:
        
#         1. **Truy cập Google Cloud Console**: Đi tới [console.cloud.google.com](https://console.cloud.google.com/)
        
#         2. **Tạo dự án mới** hoặc chọn dự án hiện có
        
#         3. **Bật Google Sheets API**:
#            - Điều hướng đến "APIs & Services" > "Library"
#            - Tìm "Google Sheets API" và bật
        
#         4. **Tạo Service Account**:
#            - Đi tới "APIs & Services" > "Credentials"
#            - Nhấp vào "Create Credentials" > "Service account"
#            - Điền thông tin và cấp quyền "Editor"
        
#         5. **Tạo khóa mới**:
#            - Chọn service account vừa tạo
#            - Chọn tab "Keys" > "Add key" > "Create new key"
#            - Chọn định dạng JSON và tải xuống
        
#         6. **Chia sẻ Google Sheet**:
#            - Mở Google Sheet cần truy cập
#            - Nhấp vào nút "Share"
#            - Thêm email của service account (định dạng: `<EMAIL>`)
#            - Cấp quyền chỉnh sửa
        
#         7. **Tải lên tệp JSON** vào form phía trên
#         """)

# # Create a new worksheet with the current date with fallback
# def create_google_sheet():
#     """Create a new worksheet with the current date with local fallback"""
#     if not st.session_state.google_sheets_available or not client:
#         log_event("Using local storage for form data (Google Sheets unavailable)")
#         return None
    
#     try:
#         today_date = datetime.now().strftime("%Y-%m-%d")
#         spreadsheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')
#         worksheet_title = f'Form_{today_date}'

#         # Create the worksheet if it doesn't exist
#         try:
#             worksheet = spreadsheet.add_worksheet(title=worksheet_title, rows="100", cols="6")
#             # Set header row
#             worksheet.append_row(["Name", "Company", "Role", "PhoneNo", "Email", "Sentiment"])
#         except gspread.exceptions.WorksheetExists:
#             worksheet = spreadsheet.worksheet(worksheet_title)  # Get the existing worksheet

#         return worksheet
#     except Exception as e:
#         log_event(f"Error creating/accessing Google Sheet: {str(e)}")
#         return None

# # Insert user info with fallback
# def add_info(data):
#     """Insert user info with local fallback"""
#     # First try Google Sheets
#     worksheet = create_google_sheet()
#     success = False
    
#     if worksheet:
#         try:
#             # Append user data
#             worksheet.append_row([
#                 data['name'], 
#                 data['company'], 
#                 data['role'], 
#                 data['phoneNo'], 
#                 data['email'], 
#                 data['sentiment']
#             ])
#             success = True
#         except Exception as e:
#             log_event(f"Error adding data to Google Sheet: {str(e)}")
#             success = False
    
#     # If Google Sheets failed, use local storage
#     if not success:
#         store_form_local(data)
    
#     # Update retention data
#     update_retention(data['email'], data['sentiment'])
    
#     # Track form submission event
#     track_interaction("form_submitted", f"Form submitted by {data['email']}")

# # Check if user is returning based on session state
# def check_returning_user():
#     """Check if user is returning based on session state"""
#     # Try to get email from session state
#     if 'user_email' in st.session_state:
#         email = st.session_state.user_email
#         if email:
#             user_id = generate_user_id(email)
#             st.session_state.user_id = user_id
            
#             # Check if user exists in retention data
#             # First try Google Sheets
#             if st.session_state.google_sheets_available:
#                 retention_sheet = get_retention_sheet()
#                 if retention_sheet:
#                     try:
#                         retention_data = retention_sheet.get_all_records()
                        
#                         for row in retention_data:
#                             if row.get("UserID") == user_id:
#                                 # Update visit count but not form submissions
#                                 update_retention(email)
#                                 track_interaction("returning_visit", f"Returning visit by {email}")
#                                 return True
#                     except Exception as e:
#                         log_event(f"Error checking returning user in Google Sheets: {str(e)}")
            
#             # If Google Sheets check failed or user not found, check local DB
#             try:
#                 conn = sqlite3.connect('data/form_data.db')
#                 cursor = conn.cursor()
                
#                 cursor.execute('SELECT * FROM user_retention WHERE user_id = ?', (user_id,))
#                 user_exists = cursor.fetchone()
                
#                 if user_exists:
#                     # Update visit count in local DB
#                     update_retention_local(email)
#                     track_interaction("returning_visit", f"Returning visit by {email}")
#                     conn.close()
#                     return True
                
#                 conn.close()
#             except Exception as e:
#                 log_event(f"Error checking returning user in local DB: {str(e)}")
    
#     return False

# # Phone number cleaning function
# def clean_phone_numbers(phone):
#     if not isinstance(phone, str):
#         return phone
#     if re.match(r'(\d{4} \d{3} \d{3}|\d{4} \d{3} \d{4})( - (\d{4} \d{3} \d{3}|\d{4} \d{3} \d{4}))*', phone):
#         return phone
#     phone = phone.replace('|', '-')
#     phone = re.sub(r'[.\s()]', '', phone)
#     if phone.startswith('+84'):
#         phone = '0' + phone[3:]
#     elif phone.startswith('84'):
#         phone = '0' + phone[2:]
#     if '/' in phone:
#         parts = phone.split('/')
#         cleaned_parts = [clean_phone_numbers(part.strip().replace('-', '')) for part in parts]
#         return ' - '.join(cleaned_parts)
#     phone = phone.replace('-', '')
#     match = re.match(r'(\d+)', phone)
#     if match:
#         digits = match.group(1)
#         if not digits.startswith('0'):
#             digits = '0' + digits
#         if len(digits) == 10:
#             return f"{digits[:4]} {digits[4:7]} {digits[7:]}"
#         elif len(digits) == 11:
#             return f"{digits[:4]} {digits[4:7]} {digits[7:]}"
#     return phone

# # Email typo correction dictionary and pattern
# corrections = {
#     '@domain': '.com', 'gmailcom': 'gmail.com', '.cm': '.com', 'gamil.com': 'gmail.com',
#     'yahoocom': 'yahoo.com', 'gmai.com': 'gmail.com', 'yahoooo.com': 'yahoo.com',
#     'gmal': 'gmail', 'hanoieduvn': 'hanoiedu.vn', 'tayho,edu,vn': 'tayho.edu.vn',
#     'gmaill.com': 'gmail.com', 'gmil.com': 'gmail.com', 'yahô.com': 'yahoo.com',
#     'yanhoo.com': 'yahoo.com', 'gmailk': 'gmail', 'gmail..com': 'gmail.com',
#     'hanoiưdu': 'hanoiedu', 'gmaill.com': 'gmail.com', 'nocomment.con': 'nocomment.com',
#     'gmaill@com': 'gmail.com', 'yahoocomvn': 'yahoo.com.vn', 'gmail. Com': 'gmail.com',
#     'gmail,com': 'gmail.com', '@.gmail': '@gmail', '"gmail': '@gmail', 'Gmail': 'gmail',
#     '.con': '.com', '.co': '.com'
# }
# email_pattern = re.compile(r'[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+')

# def fix_common_typos(email):
#     if email is None:
#         return None
#     email = email.strip().strip('-').replace(' ', '')  # Strip and remove spaces

#     # Replace general patterns first
#     for wrong, right in corrections.items():
#         email = email.replace(wrong, right)

#     # Use regex to validate email structure
#     match = email_pattern.search(email)
#     if match:
#         email = match.group(0)

#     # Handling multiple '@' cases
#     if email.count('@') > 1:
#         parts = email.split('@')
#         email = parts[0] + '@' + ''.join(parts[1:])
        
#     # Lowercase the email
#     if email.isupper():
#         email = email.lower()
        
#     # Remove trailing dot if exists
#     if email.endswith('.'):
#         email = email[:-1]
        
#     # Explicitly handle ending with 'gmail'
#     if email.endswith('gmail'):
#         email = email.replace('gmail', 'gmail.com')

#     # Additional check for a final '.com' adjustment
#     if email.endswith('.com'):
#         return email
#     elif email.endswith('.comm'):
#         return email[:-1]  # Remove the extra 'm'

#     return email

# def validate_email(email):
#     if email is None or not isinstance(email, str):
#         return None, False
#     email = fix_common_typos(email)
#     if re.match(email_pattern, email):
#         return email, True
#     return email, False

# def clean_emails(email_cell):
#     if isinstance(email_cell, str):
#         emails = [email.strip() for email in email_cell.split(' - ') if email.strip()]
#         cleaned_emails = []
#         is_valid = True
#         for email in emails:
#             cleaned_email, valid = validate_email(email)
#             if not valid:
#                 is_valid = False
#             cleaned_emails.append(cleaned_email)
#         return ' - '.join(cleaned_emails), is_valid
#     else:
#         return None, False

# # Main validation function
# def validate_data(data):
#     errors = []
#     # Name validation
#     if "name" in data and not re.match(r'^[\w\sÀÁẢÃẠÂẤẦẨẪẬĂẮẰẲẴẶÈÉẺẼẸÊẾỀỂỄỆÌÍỈĨỊÒÓỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÙÚỦŨỤƯỨỪỬỮỰÝỲÝỶỸỴ]+$', data["name"]):
#         errors.append("Tên riêng không thể bao gồm số.")
#     # Company validation
#     if "company" in data and not data["company"].replace(" ", "").isalnum():
#         errors.append("Tên công ty đang chứa ký hiệu đặc biệt.")
#     # Phone number validation
#     if "phoneNo" in data:
#         cleaned_phone = clean_phone_numbers(data["phoneNo"])
#         if cleaned_phone != data["phoneNo"]:
#             data["phoneNo"] = cleaned_phone
#         if len(re.sub(r'\D', '', cleaned_phone)) not in [10, 11]:
#             errors.append("Số điện thoại chưa hợp lệ.")
#     # Email validation
#     if "email" in data:
#         cleaned_email, is_valid = clean_emails(data["email"])
#         data["email"] = cleaned_email
#         if not is_valid:
#             errors.append("Địa chỉ email chưa hợp lệ.")
#     return errors

# def send_confirmation_email(user_name, user_email):
#     # Email configuration
#     sender_email = "<EMAIL>"
#     sender_password = "HnAm2002#@!" 
#     subject = "[This is an auto email, no-reply] Confirmation of your submission"
#     body = f"Hi {user_name}. EA wants to say a huge dupe cute thank you for your submission! We appreciate your interest. More information will be provided shortly."

#     # Create message
#     msg = MIMEMultipart()
#     msg['From'] = sender_email
#     msg['To'] = user_email
#     msg['Subject'] = subject

#     # Attach body text
#     msg.attach(MIMEText(body, 'plain'))

#     try:
#         # Set up the server
#         server = smtplib.SMTP('smtp.gmail.com', 587)
#         server.starttls()  # Upgrade to a secure connection
#         server.login(sender_email, sender_password)

#         # Send the email
#         server.send_message(msg)
#         log_event(f"Email sent successfully to {user_name}!")

#     except Exception as e:
#         log_event(f"Failed to send email to {user_name}: {str(e)}")

#     finally:
#         server.quit()

# # Track input interactions
# def track_form_input(field_name, value=None):
#     if field_name:
#         has_value = value is not None and value != ""
#         track_interaction("form_field_interaction", f"Field: {field_name}, Filled: {has_value}")

# # Form Creation
# def form_creation():
#     # Initialize app and session
#     initialize_app()
#     initialize_session()
    
#     # Check if returning user
#     is_returning = check_returning_user()
    
#     # Track page view
#     track_interaction("page_view", "Registration form")
    
#     data = {}
#     col1, col2 = st.columns([8, 2])
#     col1.header('Mời Anh/Chị điền vào thông tin dưới đây để đăng ký tham gia sự kiện')
#     col2.image("/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/logo.png", caption="")
    
#     # If returning user, show welcome back message
#     if is_returning and 'user_email' in st.session_state:
#         st.info(f"Chào mừng bạn quay trở lại! Chúng tôi nhận ra bạn từ email {st.session_state.user_email}")
    
#     with st.form(key='Registration Form'):
#         name = st.text_input('Họ và tên của Anh/Chị: ')
#         if name:  # Track only after user enters something
#             track_form_input("name", name)
            
#         company = st.text_input('Tên doanh nghiệp của Anh/Chị: ')
#         if company:
#             track_form_input("company", company)
            
#         role = st.selectbox('Chức vụ của Anh/Chị: ', options=["C-level", "M-level", "E-level"], index=None)
#         if role:
#             track_form_input("role", role)
            
#         phoneNo = st.text_input('Số điện thoại của Anh/Chị: ')
#         if phoneNo:
#             track_form_input("phoneNo", phoneNo)
            
#         email = st.text_input('Địa chỉ email của Anh/Chị: ')
#         if email:
#             track_form_input("email", email)
            
#         sentiment = st.slider("Rate your experience:", 1, 5, 1, format="%d ⭐")
#         track_form_input("sentiment", sentiment)
        
#         start_time = time.time()  # Start timing form submission
#         submit = st.form_submit_button(label='Register')
        
#         if submit:
#             # Track form submission time
#             end_time = time.time()
#             duration = round(end_time - start_time, 2)
#             track_interaction("form_submit_time", f"Form submitted in {duration} seconds")
            
#             data['name'] = name
#             data['company'] = company
#             data['role'] = role
#             data['phoneNo'] = phoneNo
#             data['email'] = email
#             data['sentiment'] = sentiment

#             # Validate input data
#             if not (name and company and role and phoneNo and email and sentiment):
#                 st.warning('Anh/Chị vui lòng nhập đầy đủ các trường thông tin. Xin cảm ơn!')
#                 track_interaction("form_incomplete", "Form submission attempted with incomplete data")
#             else:
#                 # Validate the input data
#                 errors = validate_data(data)  
#                 if errors:
#                     for error in errors:
#                         st.error(error)
#                     track_interaction("form_validation_error", f"Validation errors: {', '.join(errors)}")
#                 else:
#                     # Store email in session state for returning user recognition
#                     st.session_state.user_email = email
                    
#                     add_info(data)
#                     track_interaction("form_success", "Form successfully submitted")
                    
#                     st.success('Chúc mừng Anh/Chị đã đăng ký thành công.')
#                     st.balloons()
#                     st.markdown('Anh/Chị vui lòng kiểm tra email để nhận những thông tin cập nhật từ ban tổ chức.')
                    
#                     # Send confirmation email
#                     send_confirmation_email(name, email)
    
#     # Admin sidebar
#     with st.sidebar:
#         st.title("Quản trị")
#         if st.checkbox("Hiển thị trang quản trị"):
#             admin_password = st.text_input("Mật khẩu quản trị", type="password")
#             if admin_password == "admin123":  # Replace with a secure password mechanism
#                 admin_tabs = st.radio(
#                     "Chọn chức năng quản trị:",
#                     ["Phân tích người dùng", "Cài đặt Google Sheets", "Nhật ký hệ thống"]
#                 )
                
#                 if admin_tabs == "Phân tích người dùng":
#                     show_analytics_dashboard()
#                 elif admin_tabs == "Cài đặt Google Sheets":
#                     show_google_sheets_setup()
#                 elif admin_tabs == "Nhật ký hệ thống":
#                     st.subheader("Nhật ký hệ thống")
#                     logs_df = pd.DataFrame(st.session_state.latest_logs)
#                     if not logs_df.empty:
#                         st.dataframe(logs_df)
#                     else:
#                         st.info("Chưa có nhật ký nào được ghi lại.")
    
#     # Record session end when user navigates away
#     end_session()

# # Main function to run the app
# def main():
#     st.set_page_config(
#         page_title="Event Registration Form with Analytics",
#         page_icon="📊",
#         layout="wide"
#     )
    
#     form_creation()

# if __name__ == "__main__":
#     main()








import streamlit as st
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from datetime import datetime
import re
import logging
import uuid
import json
import pandas as pd
import plotly.express as px
import hashlib
import time
import os
import sqlite3
from gspread.exceptions import APIError
import backoff
import yaml
import streamlit_authenticator as stauth
from streamlit.runtime.state import SessionStateProxy
from send_email import send_password_email, send_confirmation_email

# Set up logging with a timestamp for each log entry
logging.basicConfig(
    filename='form.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Log helper function
def log_event(message):
    logging.info(message)
    if 'latest_logs' not in st.session_state:
        st.session_state.latest_logs = []
    st.session_state.latest_logs.append({"timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"), "message": message})
    # Keep only the last 100 logs in memory
    if len(st.session_state.latest_logs) > 100:
        st.session_state.latest_logs = st.session_state.latest_logs[-100:]

# Database setup for local storage fallback
def setup_local_db():
    """Set up SQLite database for local storage when Google Sheets is unavailable"""
    try:
        # Create data directory if it doesn't exist
        if not os.path.exists('data'):
            os.makedirs('data')
        
        conn = sqlite3.connect('data/form_data.db')
        cursor = conn.cursor()
        
        # Create tables if they don't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS form_submissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT,
            company TEXT,
            role TEXT,
            phone_no TEXT,
            email TEXT,
            sentiment INTEGER,
            submission_time TIMESTAMP
        )
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_analytics (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TIMESTAMP,
            session_id TEXT,
            user_id TEXT,
            event_type TEXT,
            event_details TEXT,
            duration REAL,
            form_completion TEXT,
            browser TEXT,
            device_type TEXT,
            referrer TEXT
        )
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id TEXT,
            user_id TEXT,
            start_time TIMESTAMP,
            end_time TIMESTAMP,
            duration REAL,
            page_views INTEGER,
            form_submitted TEXT,
            browser TEXT,
            device_type TEXT,
            referrer TEXT
        )
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_retention (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT UNIQUE,
            email TEXT,
            first_visit TIMESTAMP,
            last_visit TIMESTAMP,
            visit_count INTEGER,
            form_submissions INTEGER,
            average_form_rating REAL,
            last_form_rating INTEGER
        )
        ''')
        
        conn.commit()
        conn.close()
        log_event("Local database setup successfully")
        return True
    except Exception as e:
        log_event(f"Error setting up local database: {str(e)}")
        return False

# Function to store form submission in local database
def store_form_local(data):
    """Store form submission in local SQLite database"""
    try:
        conn = sqlite3.connect('data/form_data.db')
        cursor = conn.cursor()
        
        # Insert form data
        cursor.execute('''
        INSERT INTO form_submissions 
        (name, company, role, phone_no, email, sentiment, submission_time)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            data['name'],
            data['company'],
            data['role'],
            data['phoneNo'],
            data['email'],
            data['sentiment'],
            datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        ))
        
        conn.commit()
        conn.close()
        log_event(f"Form data stored locally for {data['email']}")
        return True
    except Exception as e:
        log_event(f"Error storing form data locally: {str(e)}")
        return False

# Function to store analytics data in local database
def store_analytics_local(data):
    """Store analytics data in local SQLite database"""
    try:
        conn = sqlite3.connect('data/form_data.db')
        cursor = conn.cursor()
        
        # Insert analytics data
        cursor.execute('''
        INSERT INTO user_analytics 
        (timestamp, session_id, user_id, event_type, event_details, 
        duration, form_completion, browser, device_type, referrer)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data.get('timestamp', datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
            data.get('session_id', "unknown"),
            data.get('user_id', "anonymous"),
            data.get('event_type', "unknown"),
            data.get('event_details', ""),
            data.get('duration', 0),
            data.get('form_completion', "No"),
            data.get('browser', "Unknown"),
            data.get('device_type', "Unknown"),
            data.get('referrer', "Direct")
        ))
        
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        log_event(f"Error storing analytics data locally: {str(e)}")
        return False

# Function to store session data in local database
def store_session_local(data):
    """Store session data in local SQLite database"""
    try:
        conn = sqlite3.connect('data/form_data.db')
        cursor = conn.cursor()
        
        # Insert session data
        cursor.execute('''
        INSERT INTO user_sessions 
        (session_id, user_id, start_time, end_time, duration, 
        page_views, form_submitted, browser, device_type, referrer)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data.get('session_id', "unknown"),
            data.get('user_id', "anonymous"),
            data.get('start_time', datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
            data.get('end_time', datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
            data.get('duration', 0),
            data.get('page_views', 0),
            data.get('form_submitted', "No"),
            data.get('browser', "Unknown"),
            data.get('device_type', "Unknown"),
            data.get('referrer', "Direct")
        ))
        
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        log_event(f"Error storing session data locally: {str(e)}")
        return False

# Function to update user retention data in local database
def update_retention_local(email, form_rating=None):
    """Update user retention data in local SQLite database"""
    if not email:
        return False
    
    try:
        user_id = generate_user_id(email)
        conn = sqlite3.connect('data/form_data.db')
        cursor = conn.cursor()
        
        # Check if user exists
        cursor.execute('SELECT * FROM user_retention WHERE user_id = ?', (user_id,))
        user_exists = cursor.fetchone()
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        if user_exists:
            # Update existing user
            if form_rating:
                # Calculate new average rating
                cursor.execute('''
                SELECT visit_count, form_submissions, average_form_rating 
                FROM user_retention WHERE user_id = ?
                ''', (user_id,))
                row = cursor.fetchone()
                visit_count = row[0] + 1
                form_submissions = row[1] + 1
                avg_rating = row[2] or 0
                
                if form_submissions > 1:
                    new_avg_rating = ((avg_rating * (form_submissions - 1)) + form_rating) / form_submissions
                else:
                    new_avg_rating = form_rating
                
                cursor.execute('''
                UPDATE user_retention SET 
                last_visit = ?, 
                visit_count = ?, 
                form_submissions = ?, 
                average_form_rating = ?, 
                last_form_rating = ?
                WHERE user_id = ?
                ''', (
                    current_time, 
                    visit_count, 
                    form_submissions, 
                    round(new_avg_rating, 2),
                    form_rating,
                    user_id
                ))
            else:
                # Just update visit count
                cursor.execute('''
                UPDATE user_retention SET 
                last_visit = ?, 
                visit_count = visit_count + 1
                WHERE user_id = ?
                ''', (current_time, user_id))
        else:
            # Add new user
            cursor.execute('''
            INSERT INTO user_retention 
            (user_id, email, first_visit, last_visit, visit_count, 
            form_submissions, average_form_rating, last_form_rating)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                user_id,
                email,
                current_time,
                current_time,
                1,
                1 if form_rating else 0,
                form_rating if form_rating else None,
                form_rating if form_rating else None
            ))
        
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        log_event(f"Error updating retention data locally: {str(e)}")
        return False

# Function to get analytics data from local database
def get_analytics_data_local():
    """Get analytics data from local SQLite database"""
    try:
        conn = sqlite3.connect('data/form_data.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM user_analytics')
        rows = cursor.fetchall()
        
        # Convert to list of dicts
        data = [dict(row) for row in rows]
        conn.close()
        return data
    except Exception as e:
        log_event(f"Error getting analytics data from local database: {str(e)}")
        return []

# Function to get retention data from local database
def get_retention_data_local():
    """Get retention data from local SQLite database"""
    try:
        conn = sqlite3.connect('data/form_data.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM user_retention')
        rows = cursor.fetchall()
        
        # Convert to list of dicts
        data = [dict(row) for row in rows]
        conn.close()
        return data
    except Exception as e:
        log_event(f"Error getting retention data from local database: {str(e)}")
        return []

# Function to get session data from local database
def get_sessions_data_local():
    """Get session data from local SQLite database"""
    try:
        conn = sqlite3.connect('data/form_data.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM user_sessions')
        rows = cursor.fetchall()
        
        # Convert to list of dicts
        data = [dict(row) for row in rows]
        conn.close()
        return data
    except Exception as e:
        log_event(f"Error getting session data from local database: {str(e)}")
        return []

# Google Sheets connection with retry logic
@backoff.on_exception(backoff.expo, 
                     (APIError, ConnectionError), 
                     max_tries=3,
                     jitter=None)
def connect_to_google_sheets():
    """Connect to Google Sheets with retry logic and error handling"""
    try:
        # Check if we need to restore service account from session state
        if 'service_account_json' in st.session_state and st.session_state.service_account_json:
            # Save to temporary file
            with open('temp_service_account.json', 'w') as f:
                json.dump(st.session_state.service_account_json, f)
            
            # Use the temporary file for auth
            scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
            creds = ServiceAccountCredentials.from_json_keyfile_name('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/.streamlit/ea-database-form-876356d31535.json', scope)
            client = gspread.authorize(creds)
            
            # Test connection
            # Just get the list of spreadsheets to verify connection works
            client.list_spreadsheet_files()
            
            log_event("Successfully connected to Google Sheets API")
            st.session_state.google_sheets_available = True
            return client
        else:
            # Try regular connection from file
            try:
                scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
                creds = ServiceAccountCredentials.from_json_keyfile_name('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/.streamlit/ea-database-form-876356d31535.json', scope)
                client = gspread.authorize(creds)
                
                # Test connection
                client.list_spreadsheet_files()
                
                log_event("Successfully connected to Google Sheets API")
                st.session_state.google_sheets_available = True
                return client
            except Exception as e:
                log_event(f"Error connecting with service account file: {str(e)}")
                st.session_state.google_sheets_available = False
                return None
    except Exception as e:
        log_event(f"Error connecting to Google Sheets: {str(e)}")
        st.session_state.google_sheets_available = False
        return None

# Initialize global variables and session state
def initialize_app():
    """Initialize app state and variables"""
    # Initialize session state variables
    if 'session_id' not in st.session_state:
        st.session_state.session_id = str(uuid.uuid4())
    
    if 'session_start' not in st.session_state:
        st.session_state.session_start = datetime.now()
        
    if 'interactions' not in st.session_state:
        st.session_state.interactions = []
        
    if 'user_id' not in st.session_state:
        st.session_state.user_id = None
    
    if 'google_sheets_available' not in st.session_state:
        st.session_state.google_sheets_available = False
    
    if 'latest_logs' not in st.session_state:
        st.session_state.latest_logs = []
    
    if 'is_local_db_setup' not in st.session_state:
        st.session_state.is_local_db_setup = setup_local_db()
    
    # Try to connect to Google Sheets
    if 'client' not in st.session_state:
        st.session_state.client = connect_to_google_sheets()
    
    # Use the client from session state
    global client
    client = st.session_state.client

# Create or initialize authentication config file if it doesn't exist
def initialize_auth_config():
    """Create or initialize authentication config file"""
    config_path = 'config.yaml'
    if not os.path.exists(config_path):
        # Create default config
        default_config = {
            'cookie': {
                'expiry_days': 30,
                'key': 'some_signature_key',
                'name': 'admin_auth_cookie'
            },
            'credentials': {
                'usernames': {
                    'admin': {
                        'email': '<EMAIL>',
                        'failed_login_attempts': 0,
                        'first_name': 'Admin',
                        'last_name': 'User',
                        'logged_in': False,
                        'password': 'admin123',  # Will be hashed automatically
                        'roles': ['admin']
                    }
                }
            },
            'pre-authorized': {
                'emails': ['<EMAIL>']
            }
        }
        
        # Save config to file
        with open(config_path, 'w') as file:
            yaml.dump(default_config, file, default_flow_style=False)
        
        log_event("Created default authentication config file")
    
    # Load config
    with open(config_path) as file:
        config = yaml.load(file, Loader=yaml.SafeLoader)
    
    return config

# Session tracking initialization
def initialize_session():
    """Initialize session tracking"""
    # Already set up in initialize_app(), just track page load
    track_interaction("page_load", "Session initialized")

# Create or get an analytics worksheet with fallback
def get_analytics_sheet():
    """Get analytics worksheet with local fallback"""
    if not st.session_state.google_sheets_available or not client:
        log_event("Using local storage for analytics (Google Sheets unavailable)")
        return None
    
    try:
        spreadsheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')
        analytics_title = 'User_Analytics'
        
        try:
            worksheet = spreadsheet.worksheet(analytics_title)
        except gspread.exceptions.WorksheetNotFound:
            worksheet = spreadsheet.add_worksheet(title=analytics_title, rows="1000", cols="10")
            # Set header row
            worksheet.append_row([
                "Timestamp", "SessionID", "UserID", "EventType", 
                "EventDetails", "Duration", "FormCompletion", 
                "Browser", "DeviceType", "Referrer"
            ])
        
        return worksheet
    except Exception as e:
        log_event(f"Error accessing analytics sheet: {str(e)}")
        return None

# Create a sessions worksheet with fallback
def get_sessions_sheet():
    """Get sessions worksheet with local fallback"""
    if not st.session_state.google_sheets_available or not client:
        log_event("Using local storage for sessions (Google Sheets unavailable)")
        return None
    
    try:
        spreadsheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')
        sessions_title = 'User_Sessions'
        
        try:
            worksheet = spreadsheet.worksheet(sessions_title)
        except gspread.exceptions.WorksheetNotFound:
            worksheet = spreadsheet.add_worksheet(title=sessions_title, rows="1000", cols="10")
            # Set header row
            worksheet.append_row([
                "SessionID", "UserID", "StartTime", "EndTime", 
                "Duration", "PageViews", "FormSubmitted", 
                "Browser", "DeviceType", "Referrer"
            ])
        
        return worksheet
    except Exception as e:
        log_event(f"Error accessing sessions sheet: {str(e)}")
        return None

# Get retention worksheet with fallback
def get_retention_sheet():
    """Get retention worksheet with local fallback"""
    if not st.session_state.google_sheets_available or not client:
        log_event("Using local storage for retention (Google Sheets unavailable)")
        return None
    
    try:
        spreadsheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')
        retention_title = 'User_Retention'
        
        try:
            worksheet = spreadsheet.worksheet(retention_title)
        except gspread.exceptions.WorksheetNotFound:
            worksheet = spreadsheet.add_worksheet(title=retention_title, rows="1000", cols="8")
            # Set header row
            worksheet.append_row([
                "UserID", "Email", "FirstVisit", "LastVisit", 
                "VisitCount", "FormSubmissions", "AverageFormRating", 
                "LastFormRating"
            ])
        
        return worksheet
    except Exception as e:
        log_event(f"Error accessing retention sheet: {str(e)}")
        return None

# Record user interaction with fallback
def track_interaction(event_type, event_details=""):
    """Track user interaction with local fallback"""
    if 'interactions' in st.session_state:
        timestamp = datetime.now()
        st.session_state.interactions.append({
            "timestamp": timestamp,
            "event_type": event_type,
            "event_details": event_details
        })
        
        # Get browser and device info (simulation since Streamlit doesn't expose this directly)
        browser = "Unknown"
        device_type = "Desktop"  # Default assumption
        referrer = "Direct"
        
        # Prepare data for storage
        data = {
            "timestamp": timestamp.strftime("%Y-%m-%d %H:%M:%S"),
            "session_id": st.session_state.session_id,
            "user_id": st.session_state.user_id if st.session_state.user_id else "Anonymous",
            "event_type": event_type,
            "event_details": event_details,
            "duration": (timestamp - st.session_state.session_start).total_seconds(),
            "form_completion": "No" if event_type != "form_submitted" else "Yes",
            "browser": browser,
            "device_type": device_type,
            "referrer": referrer
        }
        
        # Try Google Sheets first
        analytics_sheet = get_analytics_sheet()
        if analytics_sheet:
            try:
                analytics_sheet.append_row([
                    data["timestamp"],
                    data["session_id"],
                    data["user_id"],
                    data["event_type"],
                    data["event_details"],
                    data["duration"],
                    data["form_completion"],
                    data["browser"],
                    data["device_type"],
                    data["referrer"]
                ])
            except Exception as e:
                log_event(f"Error adding to analytics sheet: {str(e)}")
                # Fallback to local storage
                store_analytics_local(data)
        else:
            # Use local storage
            store_analytics_local(data)

# Generate a user ID from email (hashed for privacy)
def generate_user_id(email):
    """Generate a user ID from email (hashed for privacy)"""
    if not email:
        return None
    return hashlib.md5(email.lower().encode()).hexdigest()

# Update user retention data with fallback
def update_retention(email, form_rating=None):
    """Update user retention data with local fallback"""
    if not email:
        return
    
    user_id = generate_user_id(email)
    st.session_state.user_id = user_id
    
    # Try Google Sheets first
    retention_sheet = get_retention_sheet()
    if retention_sheet:
        try:
            retention_data = retention_sheet.get_all_records()
            
            # Check if user exists
            user_exists = False
            row_index = 2  # Accounting for header row
            
            for i, row in enumerate(retention_data):
                if row.get("UserID") == user_id:
                    user_exists = True
                    row_index = i + 2  # +2 for header row and 0-indexing
                    break
            
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            if user_exists:
                # Update existing user
                user_row = retention_data[row_index - 2]  # Adjust index
                visit_count = int(user_row.get("VisitCount", 0)) + 1
                form_submissions = int(user_row.get("FormSubmissions", 0)) + 1
                
                # Calculate average rating
                if form_rating:
                    avg_rating = float(user_row.get("AverageFormRating", 0)) if user_row.get("AverageFormRating") else 0
                    total_submissions = form_submissions - 1  # Exclude current submission
                    
                    if total_submissions > 0:
                        new_avg_rating = ((avg_rating * total_submissions) + form_rating) / form_submissions
                    else:
                        new_avg_rating = form_rating
                else:
                    new_avg_rating = float(user_row.get("AverageFormRating", 0)) if user_row.get("AverageFormRating") else 0
                
                # Update the row
                retention_sheet.update(f'D{row_index}', current_time)  # LastVisit
                retention_sheet.update(f'E{row_index}', visit_count)  # VisitCount
                retention_sheet.update(f'F{row_index}', form_submissions)  # FormSubmissions
                retention_sheet.update(f'G{row_index}', round(new_avg_rating, 2))  # AverageFormRating
                
                if form_rating:
                    retention_sheet.update(f'H{row_index}', form_rating)  # LastFormRating
            else:
                # Add new user
                retention_sheet.append_row([
                    user_id,
                    email,
                    current_time,  # FirstVisit
                    current_time,  # LastVisit
                    1,  # VisitCount
                    1 if form_rating else 0,  # FormSubmissions
                    form_rating if form_rating else "",  # AverageFormRating
                    form_rating if form_rating else ""   # LastFormRating
                ])
        except Exception as e:
            log_event(f"Error updating retention sheet: {str(e)}")
            # Fallback to local storage
            update_retention_local(email, form_rating)
    else:
        # Use local storage
        update_retention_local(email, form_rating)

# End session tracking with fallback
def end_session():
    """End session tracking with local fallback"""
    if 'session_start' in st.session_state and 'session_id' in st.session_state:
        end_time = datetime.now()
        duration = (end_time - st.session_state.session_start).total_seconds()
        
        # Count page views from interactions
        page_views = sum(1 for interaction in st.session_state.interactions 
                         if interaction["event_type"] == "page_view" or
                            interaction["event_type"] == "page_load")
        
        # Check if form was submitted
        form_submitted = any(interaction["event_type"] == "form_submitted" 
                             for interaction in st.session_state.interactions)
        
        # Prepare data for storage
        data = {
            "session_id": st.session_state.session_id,
            "user_id": st.session_state.user_id if st.session_state.user_id else "Anonymous",
            "start_time": st.session_state.session_start.strftime("%Y-%m-%d %H:%M:%S"),
            "end_time": end_time.strftime("%Y-%m-%d %H:%M:%S"),
            "duration": round(duration, 2),
            "page_views": page_views,
            "form_submitted": "Yes" if form_submitted else "No",
            "browser": "Unknown",
            "device_type": "Desktop",
            "referrer": "Direct"
        }
        
        # Try Google Sheets first
        sessions_sheet = get_sessions_sheet()
        if sessions_sheet:
            try:
                sessions_sheet.append_row([
                    data["session_id"],
                    data["user_id"],
                    data["start_time"],
                    data["end_time"],
                    data["duration"],
                    data["page_views"],
                    data["form_submitted"],
                    data["browser"],
                    data["device_type"],
                    data["referrer"]
                ])
            except Exception as e:
                log_event(f"Error adding to sessions sheet: {str(e)}")
                # Fallback to local storage
                store_session_local(data)
        else:
            # Use local storage
            store_session_local(data)
        
        # Clear session state but keep necessary values
        st.session_state.interactions = []
        st.session_state.session_start = datetime.now()
        st.session_state.session_id = str(uuid.uuid4())

# Generate analytics dashboard with fallback
def show_analytics_dashboard():
    """Generate analytics dashboard with data from either Google Sheets or local storage"""
    st.title("Bảng điều khiển phân tích người dùng")
    
    # Display storage status
    if st.session_state.google_sheets_available:
        st.success("✅ Kết nối Google Sheets đang hoạt động")
    else:
        st.warning("⚠️ Đang sử dụng lưu trữ cục bộ (Google Sheets không khả dụng)")
    
    # Get data for analysis
    analytics_data = []
    retention_data = []
    sessions_data = []
    
    if st.session_state.google_sheets_available:
        # Try to get data from Google Sheets
        analytics_sheet = get_analytics_sheet()
        if analytics_sheet:
            try:
                analytics_data = analytics_sheet.get_all_records()
            except Exception as e:
                log_event(f"Error reading analytics data from Google Sheets: {str(e)}")
        
        retention_sheet = get_retention_sheet()
        if retention_sheet:
            try:
                retention_data = retention_sheet.get_all_records()
            except Exception as e:
                log_event(f"Error reading retention data from Google Sheets: {str(e)}")
        
        sessions_sheet = get_sessions_sheet()
        if sessions_sheet:
            try:
                sessions_data = sessions_sheet.get_all_records()
            except Exception as e:
                log_event(f"Error reading sessions data from Google Sheets: {str(e)}")
    
    # If data is empty or Google Sheets is unavailable, use local data
    if not analytics_data:
        analytics_data = get_analytics_data_local()
    
    if not retention_data:
        retention_data = get_retention_data_local()
    
    if not sessions_data:
        sessions_data = get_sessions_data_local()
    
    if not analytics_data and not retention_data and not sessions_data:
        st.info("Chưa có đủ dữ liệu để hiển thị phân tích.")
        return
    
    # Convert to pandas DataFrames
    analytics_df = pd.DataFrame(analytics_data)
    retention_df = pd.DataFrame(retention_data)
    sessions_df = pd.DataFrame(sessions_data)
    
    # Add timestamp columns
    if not analytics_df.empty and 'Timestamp' in analytics_df.columns:
        analytics_df['Timestamp'] = pd.to_datetime(analytics_df['Timestamp'])
    
    if not retention_df.empty:
        if 'FirstVisit' in retention_df.columns:
            retention_df['FirstVisit'] = pd.to_datetime(retention_df['FirstVisit'])
        
        if 'LastVisit' in retention_df.columns:
            retention_df['LastVisit'] = pd.to_datetime(retention_df['LastVisit'])
    
    if not sessions_df.empty and 'StartTime' in sessions_df.columns:
        sessions_df['StartTime'] = pd.to_datetime(sessions_df['StartTime'])
    
    # Key metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_users = len(retention_df) if not retention_df.empty else 0
        st.metric("Tổng số người dùng", total_users)
    
    with col2:
        total_sessions = len(sessions_df) if not sessions_df.empty else 0
        st.metric("Tổng số phiên", total_sessions)
    
    with col3:
        if not sessions_df.empty and 'FormSubmitted' in sessions_df.columns:
            form_submissions = sessions_df[sessions_df['FormSubmitted'] == 'Yes'].shape[0]
        else:
            form_submissions = 0
        st.metric("Số lượt gửi form", form_submissions)
    
    with col4:
        if form_submissions > 0 and total_sessions > 0:
            conversion_rate = round((form_submissions / total_sessions) * 100, 2)
        else:
            conversion_rate = 0
        st.metric("Tỷ lệ chuyển đổi", f"{conversion_rate}%")
    
    # Daily visitors chart
    st.subheader("Lượt truy cập theo ngày")
    if not sessions_df.empty and 'StartTime' in sessions_df.columns:
        try:
            daily_visits = sessions_df.resample('D', on='StartTime').size()
            daily_visits_df = daily_visits.reset_index()
            daily_visits_df.columns = ['Ngày', 'Số lượt truy cập']
            
            fig = px.line(daily_visits_df, x='Ngày', y='Số lượt truy cập',
                         title='Lượt truy cập theo ngày')
            st.plotly_chart(fig)
        except Exception as e:
            st.error(f"Lỗi khi hiển thị biểu đồ truy cập: {str(e)}")
    
    # User retention chart
    st.subheader("Phân tích giữ chân người dùng")
    
    if not retention_df.empty and len(retention_df) > 0 and 'FirstVisit' in retention_df.columns:
        try:
            # Calculate days since first visit
            recent_date = datetime.now()
            retention_df['DaysSinceFirstVisit'] = (recent_date - retention_df['FirstVisit']).dt.days
            
            # Calculate days since last visit
            if 'LastVisit' in retention_df.columns:
                retention_df['DaysSinceLastVisit'] = (recent_date - retention_df['LastVisit']).dt.days
            
            # Group by days since first visit
            retention_by_age = retention_df.groupby('DaysSinceFirstVisit').size().reset_index()
            retention_by_age.columns = ['Số ngày kể từ lần đầu', 'Số người dùng']
            
            fig = px.bar(retention_by_age, x='Số ngày kể từ lần đầu', y='Số người dùng',
                        title='Phân tích người dùng theo thời gian')
            st.plotly_chart(fig)
            
            # Returning users
            if 'VisitCount' in retention_df.columns:
                returning_users = retention_df[retention_df['VisitCount'] > 1].shape[0]
                returning_rate = round((returning_users / total_users) * 100, 2) if total_users > 0 else 0
                
                st.metric("Tỷ lệ người dùng quay lại", f"{returning_rate}%")
        except Exception as e:
            st.error(f"Lỗi khi hiển thị phân tích giữ chân: {str(e)}")
    
    # Event distribution
    st.subheader("Phân bố các sự kiện tương tác")
    
    if not analytics_df.empty and 'EventType' in analytics_df.columns:
        try:
            event_counts = analytics_df['EventType'].value_counts().reset_index()
            event_counts.columns = ['Loại sự kiện', 'Số lượng']
            
            fig = px.pie(event_counts, values='Số lượng', names='Loại sự kiện',
                        title='Phân bố các loại sự kiện tương tác')
            st.plotly_chart(fig)
        except Exception as e:
            st.error(f"Lỗi khi hiển thị phân bố sự kiện: {str(e)}")
    
    # Form rating distribution
    st.subheader("Phân bố đánh giá form")
    
    if not retention_df.empty and 'LastFormRating' in retention_df.columns:
        try:
            # Only include rows with ratings
            rating_df = retention_df[retention_df['LastFormRating'].notna()]
            if not rating_df.empty:
                rating_counts = rating_df['LastFormRating'].value_counts().reset_index()
                rating_counts.columns = ['Đánh giá', 'Số lượng']
                
                fig = px.bar(rating_counts, x='Đánh giá', y='Số lượng',
                            title='Phân bố đánh giá form')
                st.plotly_chart(fig)
        except Exception as e:
            st.error(f"Lỗi khi hiển thị phân bố đánh giá: {str(e)}")

# Setup for Google Sheets credentials
def show_google_sheets_setup():
    """Show Google Sheets setup interface"""
    st.subheader("Cài đặt kết nối Google Sheets")
    
    # Check current connection status
    if st.session_state.google_sheets_available:
        st.success("✅ Đã kết nối thành công với Google Sheets API")
    else:
        st.error("❌ Chưa kết nối được với Google Sheets API")
    
    # Option to upload service account JSON
    with st.expander("Tải lên tệp Service Account JSON"):
        uploaded_file = st.file_uploader("Chọn tệp JSON chứng chỉ dịch vụ", type="json")
        
        if uploaded_file is not None:
            # Read and parse JSON
            try:
                service_account_json = json.load(uploaded_file)
                
                # Verify it's a valid service account JSON
                required_keys = ["type", "project_id", "private_key_id", "private_key", "client_email"]
                if all(key in service_account_json for key in required_keys):
                    st.session_state.service_account_json = service_account_json
                    
                    if st.button("Kiểm tra kết nối"):
                        # Try to connect with the new credentials
                        st.session_state.client = connect_to_google_sheets()
                        
                        if st.session_state.google_sheets_available:
                            st.success("✅ Kết nối thành công!")
                            # Update global client
                            global client
                            client = st.session_state.client
                        else:
                            st.error("❌ Kết nối thất bại. Kiểm tra lại tệp JSON và quyền truy cập.")
                else:
                    st.error("Tệp không phải là chứng chỉ dịch vụ Google hợp lệ.")
            except json.JSONDecodeError:
                st.error("Không thể đọc tệp JSON. Định dạng không hợp lệ.")
    
    # Instructions for creating a service account
    with st.expander("Hướng dẫn tạo Service Account"):
        st.markdown("""
        ### Các bước tạo Service Account cho Google Sheets API:
        
        1. **Truy cập Google Cloud Console**: Đi tới [console.cloud.google.com](https://console.cloud.google.com/)
        
        2. **Tạo dự án mới** hoặc chọn dự án hiện có
        
        3. **Bật Google Sheets API**:
           - Điều hướng đến "APIs & Services" > "Library"
           - Tìm "Google Sheets API" và bật
        
        4. **Tạo Service Account**:
           - Đi tới "APIs & Services" > "Credentials"
           - Nhấp vào "Create Credentials" > "Service account"
           - Điền thông tin và cấp quyền "Editor"
        
        5. **Tạo khóa mới**:
           - Chọn service account vừa tạo
           - Chọn tab "Keys" > "Add key" > "Create new key"
           - Chọn định dạng JSON và tải xuống
        
        6. **Chia sẻ Google Sheet**:
           - Mở Google Sheet cần truy cập
           - Nhấp vào nút "Share"
           - Thêm email của service account (định dạng: `<EMAIL>`)
           - Cấp quyền chỉnh sửa
        
        7. **Tải lên tệp JSON** vào form phía trên
        """)

# Create a new worksheet with the current date with fallback
def create_google_sheet():
    """Create a new worksheet with the current date with local fallback"""
    if not st.session_state.google_sheets_available or not client:
        log_event("Using local storage for form data (Google Sheets unavailable)")
        return None
    
    try:
        today_date = datetime.now().strftime("%Y-%m-%d")
        spreadsheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')
        worksheet_title = f'Form_{today_date}'

        # Create the worksheet if it doesn't exist
        try:
            worksheet = spreadsheet.add_worksheet(title=worksheet_title, rows="100", cols="6")
            # Set header row
            worksheet.append_row(["Name", "Company", "Role", "PhoneNo", "Email", "Sentiment"])
        except gspread.exceptions.WorksheetExists:
            worksheet = spreadsheet.worksheet(worksheet_title)  # Get the existing worksheet

        return worksheet
    except Exception as e:
        log_event(f"Error creating/accessing Google Sheet: {str(e)}")
        return None

# Insert user info with fallback
def add_info(data):
    """Insert user info with local fallback"""
    # First try Google Sheets
    worksheet = create_google_sheet()
    success = False
    
    if worksheet:
        try:
            # Append user data
            worksheet.append_row([
                data['name'], 
                data['company'], 
                data['role'], 
                data['phoneNo'], 
                data['email'], 
                data['sentiment']
            ])
            success = True
        except Exception as e:
            log_event(f"Error adding data to Google Sheet: {str(e)}")
            success = False
    
    # If Google Sheets failed, use local storage
    if not success:
        store_form_local(data)
    
    # Update retention data
    update_retention(data['email'], data['sentiment'])
    
    # Track form submission event
    track_interaction("form_submitted", f"Form submitted by {data['email']}")

# Check if user is returning based on session state
def check_returning_user():
    """Check if user is returning based on session state"""
    # Try to get email from session state
    if 'user_email' in st.session_state:
        email = st.session_state.user_email
        if email:
            user_id = generate_user_id(email)
            st.session_state.user_id = user_id
            
            # Check if user exists in retention data
            # First try Google Sheets
            if st.session_state.google_sheets_available:
                retention_sheet = get_retention_sheet()
                if retention_sheet:
                    try:
                        retention_data = retention_sheet.get_all_records()
                        
                        for row in retention_data:
                            if row.get("UserID") == user_id:
                                # Update visit count but not form submissions
                                update_retention(email)
                                track_interaction("returning_visit", f"Returning visit by {email}")
                                return True
                    except Exception as e:
                        log_event(f"Error checking returning user in Google Sheets: {str(e)}")
            
            # If Google Sheets check failed or user not found, check local DB
            try:
                conn = sqlite3.connect('data/form_data.db')
                cursor = conn.cursor()
                
                cursor.execute('SELECT * FROM user_retention WHERE user_id = ?', (user_id,))
                user_exists = cursor.fetchone()
                
                if user_exists:
                    # Update visit count in local DB
                    update_retention_local(email)
                    track_interaction("returning_visit", f"Returning visit by {email}")
                    conn.close()
                    return True
                
                conn.close()
            except Exception as e:
                log_event(f"Error checking returning user in local DB: {str(e)}")
    
    return False

# Phone number cleaning function
def clean_phone_numbers(phone):
    if not isinstance(phone, str):
        return phone
    if re.match(r'(\d{4} \d{3} \d{3}|\d{4} \d{3} \d{4})( - (\d{4} \d{3} \d{3}|\d{4} \d{3} \d{4}))*', phone):
        return phone
    phone = phone.replace('|', '-')
    phone = re.sub(r'[.\s()]', '', phone)
    if phone.startswith('+84'):
        phone = '0' + phone[3:]
    elif phone.startswith('84'):
        phone = '0' + phone[2:]
    if '/' in phone:
        parts = phone.split('/')
        cleaned_parts = [clean_phone_numbers(part.strip().replace('-', '')) for part in parts]
        return ' - '.join(cleaned_parts)
    phone = phone.replace('-', '')
    match = re.match(r'(\d+)', phone)
    if match:
        digits = match.group(1)
        if not digits.startswith('0'):
            digits = '0' + digits
        if len(digits) == 10:
            return f"{digits[:4]} {digits[4:7]} {digits[7:]}"
        elif len(digits) == 11:
            return f"{digits[:4]} {digits[4:7]} {digits[7:]}"
    return phone

# Email typo correction dictionary and pattern
corrections = {
    '@domain': '.com', 'gmailcom': 'gmail.com', '.cm': '.com', 'gamil.com': 'gmail.com',
    'yahoocom': 'yahoo.com', 'gmai.com': 'gmail.com', 'yahoooo.com': 'yahoo.com',
    'gmal': 'gmail', 'hanoieduvn': 'hanoiedu.vn', 'tayho,edu,vn': 'tayho.edu.vn',
    'gmaill.com': 'gmail.com', 'gmil.com': 'gmail.com', 'yahô.com': 'yahoo.com',
    'yanhoo.com': 'yahoo.com', 'gmailk': 'gmail', 'gmail..com': 'gmail.com',
    'hanoiưdu': 'hanoiedu', 'gmaill.com': 'gmail.com', 'nocomment.con': 'nocomment.com',
    'gmaill@com': 'gmail.com', 'yahoocomvn': 'yahoo.com.vn', 'gmail. Com': 'gmail.com',
    'gmail,com': 'gmail.com', '@.gmail': '@gmail', '"gmail': '@gmail', 'Gmail': 'gmail',
    '.con': '.com', '.co': '.com'
}
email_pattern = re.compile(r'[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+')

def fix_common_typos(email):
    if email is None:
        return None
    email = email.strip().strip('-').replace(' ', '')  # Strip and remove spaces

    # Replace general patterns first
    for wrong, right in corrections.items():
        email = email.replace(wrong, right)

    # Use regex to validate email structure
    match = email_pattern.search(email)
    if match:
        email = match.group(0)

    # Handling multiple '@' cases
    if email.count('@') > 1:
        parts = email.split('@')
        email = parts[0] + '@' + ''.join(parts[1:])
        
    # Lowercase the email
    if email.isupper():
        email = email.lower()
        
    # Remove trailing dot if exists
    if email.endswith('.'):
        email = email[:-1]
        
    # Explicitly handle ending with 'gmail'
    if email.endswith('gmail'):
        email = email.replace('gmail', 'gmail.com')

    # Additional check for a final '.com' adjustment
    if email.endswith('.com'):
        return email
    elif email.endswith('.comm'):
        return email[:-1]  # Remove the extra 'm'

    return email

def validate_email(email):
    if email is None or not isinstance(email, str):
        return None, False
    email = fix_common_typos(email)
    if re.match(email_pattern, email):
        return email, True
    return email, False

def clean_emails(email_cell):
    if isinstance(email_cell, str):
        emails = [email.strip() for email in email_cell.split(' - ') if email.strip()]
        cleaned_emails = []
        is_valid = True
        for email in emails:
            cleaned_email, valid = validate_email(email)
            if not valid:
                is_valid = False
            cleaned_emails.append(cleaned_email)
        return ' - '.join(cleaned_emails), is_valid
    else:
        return None, False

# Main validation function
def validate_data(data):
    errors = []
    # Name validation
    if "name" in data and not re.match(r'^[\w\sÀÁẢÃẠÂẤẦẨẪẬĂẮẰẲẴẶÈÉẺẼẸÊẾỀỂỄỆÌÍỈĨỊÒÓỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÙÚỦŨỤƯỨỪỬỮỰÝỲÝỶỸỴ]+$', data["name"]):
        errors.append("Tên riêng không thể bao gồm số.")
    # Company validation
    if "company" in data and not data["company"].replace(" ", "").isalnum():
        errors.append("Tên công ty đang chứa ký hiệu đặc biệt.")
    # Phone number validation
    if "phoneNo" in data:
        cleaned_phone = clean_phone_numbers(data["phoneNo"])
        if cleaned_phone != data["phoneNo"]:
            data["phoneNo"] = cleaned_phone
        if len(re.sub(r'\D', '', cleaned_phone)) not in [10, 11]:
            errors.append("Số điện thoại chưa hợp lệ.")
    # Email validation
    if "email" in data:
        cleaned_email, is_valid = clean_emails(data["email"])
        data["email"] = cleaned_email
        if not is_valid:
            errors.append("Địa chỉ email chưa hợp lệ.")
    return errors

# Track input interactions
def track_form_input(field_name, value=None):
    if field_name:
        has_value = value is not None and value != ""
        track_interaction("form_field_interaction", f"Field: {field_name}, Filled: {has_value}")

# Form Page
def form_page():
    """Main form page for user registration"""
    # Initialize app and session
    initialize_app()
    initialize_session()
    
    # Check if returning user
    is_returning = check_returning_user()
    
    # Track page view
    track_interaction("page_view", "Registration form")
    
    data = {}
    col1, col2 = st.columns([8, 2])
    col1.header('Mời Anh/Chị điền vào thông tin dưới đây để đăng ký tham gia sự kiện')
    col2.image("/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/logo.png", caption="")
    
    # If returning user, show welcome back message
    if is_returning and 'user_email' in st.session_state:
        st.info(f"Chào mừng bạn quay trở lại! Chúng tôi nhận ra bạn từ email {st.session_state.user_email}")
    
    with st.form(key='Registration Form'):
        name = st.text_input('Họ và tên của Anh/Chị: ')
        if name:  # Track only after user enters something
            track_form_input("name", name)
            
        company = st.text_input('Tên doanh nghiệp của Anh/Chị: ')
        if company:
            track_form_input("company", company)
            
        role = st.selectbox('Chức vụ của Anh/Chị: ', options=["C-level", "M-level", "E-level"], index=None)
        if role:
            track_form_input("role", role)
            
        phoneNo = st.text_input('Số điện thoại của Anh/Chị: ')
        if phoneNo:
            track_form_input("phoneNo", phoneNo)
            
        email = st.text_input('Địa chỉ email của Anh/Chị: ')
        if email:
            track_form_input("email", email)
            
        sentiment = st.slider("Rate your experience:", 1, 5, 1, format="%d ⭐")
        track_form_input("sentiment", sentiment)
        
        start_time = time.time()  # Start timing form submission
        submit = st.form_submit_button(label='Register')
        
        if submit:
            # Track form submission time
            end_time = time.time()
            duration = round(end_time - start_time, 2)
            track_interaction("form_submit_time", f"Form submitted in {duration} seconds")
            
            data['name'] = name
            data['company'] = company
            data['role'] = role
            data['phoneNo'] = phoneNo
            data['email'] = email
            data['sentiment'] = sentiment

            # Validate input data
            if not (name and company and role and phoneNo and email and sentiment):
                st.warning('Anh/Chị vui lòng nhập đầy đủ các trường thông tin. Xin cảm ơn!')
                track_interaction("form_incomplete", "Form submission attempted with incomplete data")
            else:
                # Validate the input data
                errors = validate_data(data)  
                if errors:
                    for error in errors:
                        st.error(error)
                    track_interaction("form_validation_error", f"Validation errors: {', '.join(errors)}")
                else:
                    # Store email in session state for returning user recognition
                    st.session_state.user_email = email
                    
                    add_info(data)
                    track_interaction("form_success", "Form successfully submitted")
                    
                    st.success('Chúc mừng Anh/Chị đã đăng ký thành công.')
                    st.balloons()
                    st.markdown('Anh/Chị vui lòng kiểm tra email để nhận những thông tin cập nhật từ ban tổ chức.')
                    
                    # Send confirmation email
                    send_confirmation_email(name, email)
    
    # Record session end when user navigates away
    end_session()

# Admin Page
def admin_page():
    """Admin page with authentication and analytics dashboards"""
    st.title("Trang quản trị hệ thống")
    
    # Initialize app
    initialize_app()
    initialize_session()
    
    # Load or create authentication config
    auth_config = initialize_auth_config()
    
    # Create authentication object
    authenticator = stauth.Authenticate(
        auth_config['credentials'],
        auth_config['cookie']['name'],
        auth_config['cookie']['key'],
        auth_config['cookie']['expiry_days']
    )
    
    # Authentication area
    auth_status = st.session_state.get('authentication_status')
    
    if auth_status:
        # User is authenticated, show admin components
        # authenticator.logout('Đăng xuất', 'main')
        authenticator.logout('Đăng xuất', 'main')
        st.write(f'Xin chào, *{st.session_state.get("name")}*')
        
        # Admin tabs
        admin_tabs = st.radio(
            "Chọn chức năng quản trị:",
            ["Phân tích người dùng", "Cài đặt Google Sheets", "Nhật ký hệ thống"]
        )
        
        if admin_tabs == "Phân tích người dùng":
            show_analytics_dashboard()
        elif admin_tabs == "Cài đặt Google Sheets":
            show_google_sheets_setup()
        elif admin_tabs == "Nhật ký hệ thống":
            st.subheader("Nhật ký hệ thống")
            logs_df = pd.DataFrame(st.session_state.latest_logs)
            if not logs_df.empty:
                st.dataframe(logs_df)
            else:
                st.info("Chưa có nhật ký nào được ghi lại.")
        
        # Add reset password option
        st.subheader("Đổi mật khẩu")
        try:
            if authenticator.reset_password(st.session_state["username"]):
                st.success('Mật khẩu đã được thay đổi thành công')
                # Update config file
                with open('config.yaml', 'w') as file:
                    yaml.dump(auth_config, file, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            st.error(e)
            
    elif auth_status is False:
        st.error('Tên đăng nhập/mật khẩu không chính xác')
        # Login form
        # authenticator.login('Đăng nhập','main')
        authenticator.login()
    else:
        # User is not authenticated, show login and registration forms
        st.subheader("Đăng nhập hệ thống")
        # authenticator.login('Đăng nhập', 'main')
        authenticator.login()

        st.markdown("---")
        
        # Registration form
        st.subheader("Đăng ký tài khoản quản trị mới")
        try:
            email_of_registered_user, username_of_registered_user, name_of_registered_user = authenticator.register_user(
                pre_authorized=auth_config.get('pre-authorized', {}).get('emails', [])
            )
            if email_of_registered_user:
                st.success('Đăng ký tài khoản thành công')
                # Update config file
                with open('config.yaml', 'w') as file:
                    yaml.dump(auth_config, file, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            st.error(e)
        
        # Forgot password option
        st.markdown("---")
        st.subheader("Quên mật khẩu?")
        try:
            username_of_forgotten_password, email_of_forgotten_password, new_random_password = authenticator.forgot_password()
            if username_of_forgotten_password:
                send_password_email(email_of_forgotten_password, username_of_forgotten_password, new_random_password)
                # st.success(f'Mật khẩu mới đã được gửi đến email {email_of_forgotten_password} của bạn {username_of_forgotten_password}')
                # Update config file
                with open('config.yaml', 'w') as file:
                    yaml.dump(auth_config, file, default_flow_style=False, allow_unicode=True)
            elif username_of_forgotten_password == False:
                st.error(f'Không tìm thấy tên người dùng')
        except Exception as e:
            st.error(e)
            
    # Update config whenever authentication state changes
    with open('config.yaml', 'w') as file:
        yaml.dump(auth_config, file, default_flow_style=False, allow_unicode=True)

# Main function to run the app
def main():
    st.set_page_config(
        page_title="Event Registration System",
        page_icon="📊",
        layout="wide"
    )

    pg = st.navigation([
    st.Page(form_page, title="Biểu mẫu đăng ký", icon="🔥"),
    st.Page(admin_page, title="Quản trị viên", icon="⚙️"),
    ])
    pg.run()

if __name__ == "__main__":
    main()