import streamlit as st
import pandas as pd
import base64
from io import BytesIO
from PIL import Image
import os
import uuid
from datetime import datetime, date

# Thiết lập cấu hình trang
st.set_page_config(
    page_title="Ứng dụng Tạo Biểu Mẫu Động",
    page_icon="📋",
    layout="wide"
)

# Hàm để tạo liên kết tải xuống cho file Excel
def get_excel_download_link(df, filename="data.xlsx"):
    """
    Tạo một liên kết tải xuống cho DataFrame dưới dạng file Excel
    """
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Data')
    
    b64 = base64.b64encode(output.getvalue()).decode()
    href = f'<a href="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,{b64}" download="{filename}">Tải xuống file Excel</a>'
    return href

# Hàm để xử lý tải lên và lưu trữ ảnh
def process_image(uploaded_image):
    """
    Xử lý ảnh được tải lên và trả về đường dẫn lưu trữ
    """
    if not os.path.exists("uploaded_images"):
        os.makedirs("uploaded_images")
    
    # Tạo tên tệp duy nhất
    img_name = f"{uuid.uuid4()}.jpg"
    img_path = os.path.join("uploaded_images", img_name)
    
    # Mở và lưu ảnh
    img = Image.open(uploaded_image)
    img.save(img_path)
    
    return img_path

# Khởi tạo session state nếu chưa tồn tại
if 'data' not in st.session_state:
    st.session_state.data = []
if 'columns' not in st.session_state:
    st.session_state.columns = []
if 'column_types' not in st.session_state:
    st.session_state.column_types = []
if 'validation_rules' not in st.session_state:
    st.session_state.validation_rules = {}
if 'setup_complete' not in st.session_state:
    st.session_state.setup_complete = False

def main():
    st.title("Ứng dụng Tạo Biểu Mẫu Động")
    
    # Hiển thị tab để phân chia chức năng
    tab1, tab2, tab3 = st.tabs(["Thiết lập biểu mẫu", "Nhập dữ liệu", "Xem & Xuất dữ liệu"])
    
    with tab1:
        setup_form()
    
    with tab2:
        if st.session_state.setup_complete:
            data_entry_form()
        else:
            st.info("Vui lòng thiết lập biểu mẫu trước ở tab 'Thiết lập biểu mẫu'")
    
    with tab3:
        if st.session_state.setup_complete:
            view_and_export_data()
        else:
            st.info("Vui lòng thiết lập biểu mẫu và nhập dữ liệu trước khi xem và xuất dữ liệu")

def setup_form():
    """
    Chức năng thiết lập biểu mẫu: số cột, kiểu dữ liệu, và quy tắc xác thực
    """
    st.header("Thiết lập biểu mẫu")
    
    with st.form(key="setup_form"):
        st.subheader("Cấu hình cột")
        num_columns = st.number_input("Số lượng cột", min_value=1, max_value=20, value=3)
        
        # Danh sách kiểu dữ liệu hỗ trợ
        data_types = ["text", "numeric", "category", "date", "image"]
        
        columns = []
        column_types = []
        validation_rules = {}
        
        for i in range(int(num_columns)):
            col1, col2, col3 = st.columns([2, 2, 3])
            
            with col1:
                col_name = st.text_input(f"Tên cột {i+1}", value=f"Cột {i+1}")
                columns.append(col_name)
            
            with col2:
                col_type = st.selectbox(
                    f"Kiểu dữ liệu cho {col_name}", 
                    data_types, 
                    key=f"type_{i}"
                )
                column_types.append(col_type)
            
            with col3:
                # Thêm quy tắc xác thực tùy theo loại dữ liệu
                if col_type == "numeric":
                    min_val = st.number_input(f"Giá trị tối thiểu cho {col_name}", value=0)
                    max_val = st.number_input(f"Giá trị tối đa cho {col_name}", value=1000)
                    validation_rules[col_name] = {"min": min_val, "max": max_val}
                
                elif col_type == "text":
                    max_length = st.number_input(f"Độ dài tối đa cho {col_name}", value=100)
                    validation_rules[col_name] = {"max_length": max_length}
                
                elif col_type == "category":
                    categories = st.text_input(
                        f"Danh sách các danh mục cho {col_name} (phân tách bằng dấu phẩy)",
                        value="Danh mục 1, Danh mục 2, Danh mục 3"
                    )
                    validation_rules[col_name] = {"categories": [c.strip() for c in categories.split(",")]}
        
        submit_setup = st.form_submit_button("Lưu thiết lập biểu mẫu")
        
        if submit_setup:
            st.session_state.columns = columns
            st.session_state.column_types = column_types
            st.session_state.validation_rules = validation_rules
            st.session_state.data = []
            st.session_state.setup_complete = True
            st.success("Thiết lập biểu mẫu đã được lưu! Hãy chuyển sang tab 'Nhập dữ liệu' để bắt đầu nhập dữ liệu.")

def data_entry_form():
    """
    Biểu mẫu nhập dữ liệu dựa trên thiết lập đã được xác định
    """
    st.header("Nhập dữ liệu")
    
    with st.form(key="data_entry"):
        entry_data = {}
        
        for i, (col_name, col_type) in enumerate(zip(st.session_state.columns, st.session_state.column_types)):
            # Tạo widget nhập liệu tương ứng với loại dữ liệu
            if col_type == "numeric":
                min_val = st.session_state.validation_rules[col_name].get("min", 0)
                max_val = st.session_state.validation_rules[col_name].get("max", 1000)
                entry_data[col_name] = st.number_input(
                    f"{col_name}", 
                    min_value=float(min_val), 
                    max_value=float(max_val),
                    key=f"input_{i}"
                )
            
            elif col_type == "text":
                max_length = st.session_state.validation_rules[col_name].get("max_length", 100)
                entry_data[col_name] = st.text_input(
                    f"{col_name}", 
                    max_chars=max_length,
                    key=f"input_{i}"
                )
            
            elif col_type == "category":
                categories = st.session_state.validation_rules[col_name].get("categories", [])
                entry_data[col_name] = st.selectbox(
                    f"{col_name}", 
                    options=categories,
                    key=f"input_{i}"
                )
            
            elif col_type == "date":
                entry_data[col_name] = st.date_input(
                    f"{col_name}",
                    value="today",
                    key=f"input_{i}"
                )
            
            elif col_type == "image":
                uploaded_file = st.file_uploader(
                    f"Tải lên ảnh cho {col_name}",
                    type=["jpg", "jpeg", "png"],
                    key=f"input_{i}"
                )
                if uploaded_file is not None:
                    st.image(uploaded_file, caption=f"Ảnh đã tải lên cho {col_name}", width=200)
                    # Lưu thông tin tệp để xử lý sau khi gửi biểu mẫu
                    entry_data[col_name] = uploaded_file
                else:
                    entry_data[col_name] = None
        
        submit_entry = st.form_submit_button("Thêm dữ liệu")
        
        if submit_entry:
            # Xử lý ảnh nếu có
            for col_name, col_type in zip(st.session_state.columns, st.session_state.column_types):
                if col_type == "image" and entry_data[col_name] is not None:
                    # Lưu ảnh và cập nhật đường dẫn
                    img_path = process_image(entry_data[col_name])
                    entry_data[col_name] = img_path
            
            # Thêm dữ liệu vào session state
            st.session_state.data.append(entry_data)
            st.success("Dữ liệu đã được thêm thành công!")

def view_and_export_data():
    """
    Xem, chỉnh sửa và xuất dữ liệu đã nhập thành file Excel
    Sử dụng st.column_config để tùy chỉnh hiển thị và chỉnh sửa dữ liệu
    """
    st.header("Xem & Xuất dữ liệu")
    
    if not st.session_state.data:
        st.warning("Chưa có dữ liệu nào được nhập. Vui lòng nhập dữ liệu trước khi xuất.")
        return
    
    # Tạo DataFrame từ dữ liệu đã nhập
    df = pd.DataFrame(st.session_state.data)
    st.subheader("Xem và chỉnh sửa dữ liệu")
    
    # Cấu hình cột dựa trên loại dữ liệu
    column_config = {}
    for col_name, col_type in zip(st.session_state.columns, st.session_state.column_types):
        if col_type == "numeric":
            # Cấu hình cho cột số
            min_val = st.session_state.validation_rules[col_name].get("min", 0)
            max_val = st.session_state.validation_rules[col_name].get("max", 1000)
            column_config[col_name] = st.column_config.NumberColumn(
                label=col_name,
                min_value=float(min_val),
                max_value=float(max_val),
                format="%.2f",
                help=f"Giá trị từ {min_val} đến {max_val}"
            )
        
        elif col_type == "text":
            # Cấu hình cho cột văn bản
            max_length = st.session_state.validation_rules[col_name].get("max_length", 100)
            column_config[col_name] = st.column_config.TextColumn(
                label=col_name,
                max_chars=max_length,
                help=f"Tối đa {max_length} ký tự"
            )
        
        elif col_type == "category":
            # Cấu hình cho cột danh mục
            categories = st.session_state.validation_rules[col_name].get("categories", [])
            column_config[col_name] = st.column_config.SelectboxColumn(
                label=col_name,
                options=categories,
                help="Chọn một danh mục"
            )
        
        elif col_type == "date":
            # Cấu hình cho cột ngày tháng
            column_config[col_name] = st.column_config.DateColumn(
                label=col_name,
                format="DD/MM/YYYY",
                help="Định dạng: DD/MM/YYYY"
            )
        
        elif col_type == "image":
            # Cấu hình cho cột hình ảnh
            column_config[col_name] = st.column_config.ImageColumn(
                label=col_name,
                help="Đường dẫn đến hình ảnh"
            )
    
    # Sử dụng data_editor để hiển thị và chỉnh sửa dữ liệu
    edited_df = st.data_editor(
        df,
        column_config=column_config,
        num_rows="dynamic",
        hide_index=True,
        key="data_editor"
    )
    
    # Cập nhật dữ liệu trong session state nếu có thay đổi
    if not df.equals(edited_df):
        st.session_state.data = edited_df.to_dict('records')
        st.success("Dữ liệu đã được cập nhật!")
    
    # Tùy chọn để xóa tất cả dữ liệu
    if st.button("Xóa tất cả dữ liệu"):
        st.session_state.data = []
        st.success("Đã xóa tất cả dữ liệu!")
        st.experimental_rerun()
    
    # Xuất dữ liệu
    st.subheader("Xuất dữ liệu")
    
    # Tạo tên file mặc định
    default_filename = f"data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    filename = st.text_input("Tên file Excel", value=default_filename)
    
    if st.button("Tạo liên kết tải xuống"):
        # Sử dụng dữ liệu đã chỉnh sửa để xuất Excel
        st.markdown(get_excel_download_link(edited_df, filename), unsafe_allow_html=True)

if __name__ == "__main__":
    main()