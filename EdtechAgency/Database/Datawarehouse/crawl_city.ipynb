{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import logging\n", "from datetime import datetime\n", "import gspread\n", "from oauth2client.service_account import ServiceAccountCredentials\n", "import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.common.keys import Keys\n", "import time\n", "\n", "# Set up logging\n", "logging.basicConfig(\n", "    filename='/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/Datawarehouse/crawl_city.log',\n", "    level=logging.INFO,\n", "    format='%(asctime)s %(message)s'\n", ")\n", "\n", "def log_event(message):\n", "    logging.info(message)\n", "\n", "# Function to crawl company address\n", "def crawl_company_address(company_name):\n", "    chrome_options = Options()\n", "    chrome_options.add_argument(\"--incognito\")\n", "    chrome_options.add_argument('--headless')\n", "    driver = webdriver.Chrome(options=chrome_options)\n", "\n", "    try:\n", "        # Open Google Search\n", "        driver.get(\"https://www.google.com\")\n", "        search_box = driver.find_element(By.NAME, \"q\")\n", "        search_box.send_keys(f\"{company_name} địa chỉ\")\n", "        search_box.send_keys(Keys.RETURN)\n", "\n", "        time.sleep(2)  # Wait for results to load\n", "\n", "        # Extract search results\n", "        results = []\n", "        search_results = driver.find_elements(By.CSS_SELECTOR, 'div.g')\n", "        for result in search_results[:1]:  # Get first result\n", "            result_text = \"\"\n", "            try:\n", "                # Alternative structure 1\n", "                alt_snippet = result.find_element(By.XPATH, \"//div[@class='ULSxyf']//div[@class='sXLaOe']\").text\n", "                result_text = f\"{alt_snippet}\"\n", "                log_event(f\"{company_name}:{result_text}\")\n", "            \n", "            except Exception as e:\n", "                log_event(f\"{company_name} - Alternative structure 1 error: {e}\")\n", "                \n", "                # Try alternative structure 2\n", "                try:\n", "                    alt_title = result.find_element(By.XPATH, \".//h3\").text\n", "                    alt_snippet = result.find_element(By.XPATH, \".//div[@class='VwiC3b yXK7lf lVm3ye r025kc hJNv6b Hdw6tb']\").text\n", "                    result_text = f\"{alt_title}: {alt_snippet}\"\n", "                    log_event(f\"{company_name}:{result_text}\")\n", "                \n", "                except Exception as e:\n", "                    log_event(f\"{company_name} - Alternative structure 2 error: {e}\")\n", "                    continue\n", "            \n", "            if result_text:\n", "                results.append(result_text)\n", "        \n", "        if not results:\n", "            return \"Unknown\"\n", "        \n", "            # title = result.find_element(By.XPATH, \"//div[1][@class='hlcw0c']//h3[@class='LC20lb MBeuO DKV0Md']\").text\n", "            # snippet = result.find_element(By.XPATH, \"//div[1][@class='hlcw0c']//div[@class='VwiC3b yXK7lf lVm3ye r025kc hJNv6b Hdw6tb']\").text\n", "            # results.append(f\"{title}: {snippet}\")\n", "\n", "        return \" | \".join(results)\n", "\n", "    except Exception as e:\n", "        log_event(f\"Error while crawling school address for {company_name}: {e}\")\n", "        return \"Error\"\n", "\n", "    finally:\n", "        driver.quit()\n", "\n", "# Function to update Google Sheets with crawled information\n", "def update_google_sheets_with_crawled_info(df, column_name):\n", "    log_event('Updating Google Sheets S-K12 with crawled information')\n", "\n", "    # Create a new column for crawled info\n", "    df['Crawled_Info'] = df[column_name].apply(lambda x: crawl_company_address(x))\n", "\n", "    log_event('Completed updating Google Sheets with crawled information')\n", "    return df\n", "\n", "# Function to create a backup of the sheet\n", "def backup_sheet(sheet, worksheet_name, backup_name):\n", "    try:\n", "        worksheet = sheet.worksheet(worksheet_name)\n", "        sheet.duplicate_sheet(source_sheet_id=worksheet.id, new_sheet_name=backup_name)\n", "        log_event(f'Backup created successfully: {backup_name}')\n", "    except Exception as e:\n", "        log_event(f'Failed to create backup: {e}')\n", "\n", "# Example of using Google Sheets with crawling\n", "scope = [\"https://spreadsheets.google.com/feeds\", 'https://www.googleapis.com/auth/drive']\n", "creds = ServiceAccountCredentials.from_json_keyfile_name('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/.streamlit/ea-database-form-fbf45c7169a9.json', scope)\n", "client = gspread.authorize(creds)\n", "sheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')\n", "\n", "# Backup the original sheet\n", "backup_name = f\"Backup_S-K12_{datetime.now().strftime('%Y%m%d_%H%M%S')}\"\n", "backup_sheet(sheet, 'S-K12', backup_name)\n", "\n", "# Log retrieval\n", "log_event('Retrieving data from Google Sheets')\n", "worksheet = sheet.worksheet(\"S-K12\")\n", "\n", "# Fetch data and manually set the second row as the header\n", "data = worksheet.get_all_values()\n", "header = data[0]  # Set the second row (index 1) as the header\n", "rows = data[1:]   # Start data from the third row (index 2)\n", "\n", "# Convert to DataFrame\n", "existing_data = pd.DataFrame(rows, columns=header)\n", "\n", "# Ensure the specified column exists and apply your crawling logic\n", "column_name = 'Tên trường/công ty'\n", "\n", "if column_name in existing_data.columns:\n", "    updated_df = update_google_sheets_with_crawled_info(existing_data, column_name)\n", "    # Log completion of data processing\n", "    log_event('Saving changes to Google Sheets')\n", "\n", "    # Reconstruct the Google Sheet with the header, and updated data\n", "    updated_data = [header] + updated_df.values.tolist()\n", "\n", "    # Update Google Sheets, keeping the first row and header intact\n", "    worksheet.clear()  # Clear the worksheet before updating it\n", "    worksheet.update(f'A1', updated_data)  # Start updating from row 1 (A1)\n", "    log_event('Changes saved to Google Sheets successfully')\n", "\n", "else:\n", "    log_event(f'Error: \"{column_name}\" column not found in the sheet.')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# REAL TIME UPDATING \n", "import logging\n", "from datetime import datetime\n", "import gspread\n", "from oauth2client.service_account import ServiceAccountCredentials\n", "import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.common.keys import Keys\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "\n", "# Set up logging\n", "logging.basicConfig(\n", "    filename='/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/Datawarehouse/crawl_city-2.log',\n", "    level=logging.INFO,\n", "    format='%(asctime)s %(message)s'\n", ")\n", "\n", "def log_event(message):\n", "    logging.info(message)\n", "\n", "# Function to crawl company address\n", "def crawl_company_address(driver, company_name):\n", "    try:\n", "        # Open Google Search\n", "        driver.get(\"https://www.google.com\")\n", "        search_box = WebDriverWait(driver, 10).until(\n", "            EC.presence_of_element_located((By.NAME, \"q\"))\n", "        )\n", "        search_box.send_keys(f\"{company_name} địa chỉ\")\n", "        search_box.send_keys(Keys.RETURN)\n", "\n", "        # Wait for search results to load\n", "        WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, 'div.g')))\n", "\n", "        # Extract search results\n", "        results = []\n", "        search_results = driver.find_elements(By.CSS_SELECTOR, 'div.g')\n", "        for result in search_results[:1]:  # Get the first result\n", "            result_text = \"\"\n", "            try:\n", "                # Alternative structure 1\n", "                alt_snippet = result.find_element(By.XPATH, \"//div[@class='ULSxyf']//div[@class='sXLaOe']\").text\n", "                result_text = f\"{alt_snippet}\"\n", "                log_event(f\"{company_name}: {result_text}\")\n", "\n", "            except Exception as e:\n", "                log_event(f\"{company_name} - Alternative structure 1 error: {e}\")\n", "                \n", "                # Try alternative structure 2\n", "                try:\n", "                    alt_title = result.find_element(By.XPATH, \".//h3\").text\n", "                    alt_snippet = result.find_element(By.XPATH, \".//div[@class='VwiC3b yXK7lf lVm3ye r025kc hJNv6b Hdw6tb']\").text\n", "                    result_text = f\"{alt_title}: {alt_snippet}\"\n", "                    log_event(f\"{company_name}: {result_text}\")\n", "                \n", "                except Exception as e:\n", "                    log_event(f\"{company_name} - Alternative structure 2 error: {e}\")\n", "                    continue\n", "\n", "            if result_text:\n", "                results.append(result_text)\n", "\n", "        if not results:\n", "            return \"Unknown\"\n", "\n", "        return \" | \".join(results)\n", "\n", "    except Exception as e:\n", "        log_event(f\"Error while crawling company address for {company_name}: {e}\")\n", "        return \"Error\"\n", "\n", "# Function to update a row in Google Sheets with crawled information\n", "def update_google_sheet_row(worksheet, row_number, column_name, crawled_info):\n", "    try:\n", "        worksheet.update_acell(f'{column_name}{row_number}', crawled_info)\n", "        log_event(f\"Row {row_number} updated successfully.\")\n", "    except Exception as e:\n", "        log_event(f\"Failed to update row {row_number}: {e}\")\n", "\n", "# Function to create a backup of the sheet\n", "def backup_sheet(sheet, worksheet_name, backup_name):\n", "    try:\n", "        worksheet = sheet.worksheet(worksheet_name)\n", "        sheet.duplicate_sheet(source_sheet_id=worksheet.id, new_sheet_name=backup_name)\n", "        log_event(f'Backup created successfully: {backup_name}')\n", "    except Exception as e:\n", "        log_event(f'Failed to create backup: {e}')\n", "\n", "# Main function to process and update each row in real-time\n", "def process_and_update_sheet(worksheet, driver, column_name):\n", "    log_event('Starting the real-time processing and updating of Google Sheets')\n", "\n", "    # Fetch data and assume the second row is the header\n", "    data = worksheet.get_all_values()\n", "    header = data[0]  # Set the first row as the header\n", "    rows = data[1:]   # Start data from the second row\n", "\n", "    # Find the column index based on the column name\n", "    column_index = header.index(column_name) + 1\n", "    crawled_column_index = len(header) + 1  # Next available column for storing the crawled info\n", "\n", "    # Create a new column header for crawled info if not present\n", "    if crawled_column_index > len(header):\n", "        worksheet.update_cell(1, crawled_column_index, 'Crawled_Info')\n", "\n", "    # Iterate through each row and process it\n", "    for row_number, row in enumerate(rows, start=2):\n", "        company_name = row[column_index - 1]  # Get the company name\n", "        crawled_info = crawl_company_address(driver, company_name)\n", "        update_google_sheet_row(worksheet, row_number, chr(64 + crawled_column_index), crawled_info)\n", "\n", "    log_event('Completed real-time processing and updating of Google Sheets')\n", "\n", "# Example of using Google Sheets with crawling\n", "scope = [\"https://spreadsheets.google.com/feeds\", 'https://www.googleapis.com/auth/drive']\n", "creds = ServiceAccountCredentials.from_json_keyfile_name('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/.streamlit/ea-database-form-fbf45c7169a9.json', scope)\n", "client = gspread.authorize(creds)\n", "sheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')\n", "\n", "# Backup the original sheet\n", "backup_name = f\"Backup_S-K12_{datetime.now().strftime('%Y%m%d_%H%M%S')}\"\n", "backup_sheet(sheet, 'S-K12', backup_name)\n", "\n", "# Log retrieval\n", "log_event('Retrieving data from Google Sheets')\n", "worksheet = sheet.worksheet(\"S-K12\")\n", "\n", "# Set up Selenium WebDriver once\n", "chrome_options = Options()\n", "chrome_options.add_argument(\"--incognito\")\n", "chrome_options.add_argument('--headless')\n", "driver = webdriver.Chrome(options=chrome_options)\n", "\n", "# Process and update each row immediately\n", "try:\n", "    process_and_update_sheet(worksheet, driver, 'Tên trường/công ty')\n", "finally:\n", "    driver.quit()  # Make sure to quit the driver when done\n", "    log_event(\"Driver has been closed.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from oauth2client.service_account import ServiceAccountCredentials\n", "import gspread\n", "from datetime import datetime\n", "import logging\n", "import pandas as pd\n", "\n", "# Set up logging\n", "logging.basicConfig(\n", "    filename='/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/Datawarehouse/transform_city.log',\n", "    level=logging.INFO,\n", "    format='%(asctime)s %(message)s'\n", ")\n", "\n", "def log_event(message):\n", "    logging.info(message)\n", "\n", "# Google Sheets setup\n", "scope = [\"https://spreadsheets.google.com/feeds\", 'https://www.googleapis.com/auth/drive']\n", "creds = ServiceAccountCredentials.from_json_keyfile_name('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/.streamlit/ea-database-form-fbf45c7169a9.json', scope)\n", "client = gspread.authorize(creds)\n", "sheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')\n", "\n", "# Backup the original sheet\n", "backup_name = f\"Backup_S-K12_{datetime.now().strftime('%Y%m%d_%H%M%S')}\"\n", "def backup_sheet(sheet, worksheet_name, backup_name):\n", "    try:\n", "        worksheet = sheet.worksheet(worksheet_name)\n", "        sheet.duplicate_sheet(source_sheet_id=worksheet.id, new_sheet_name=backup_name)\n", "        log_event(f'Backup created successfully: {backup_name}')\n", "    except Exception as e:\n", "        log_event(f'Failed to create backup: {e}')\n", "backup_sheet(sheet, 'S-K12', backup_name)\n", "\n", "# Retrieve data from Google Sheets\n", "log_event('Retrieving data from Google Sheets')\n", "worksheet = sheet.worksheet(\"S-K12\")\n", "\n", "# Fetch data\n", "data = worksheet.get_all_values()\n", "header = data[0]\n", "rows = data[1:]\n", "\n", "# Convert Google Sheets data to a DataFrame\n", "df = pd.DataFrame(rows, columns=header)\n", "\n", "# List of city names to check for (case-insensitive)\n", "city_keywords = {\n", "    'hà nội': ['hà nội', 'hn', 'hanoi'],\n", "    'hồ chí minh': ['hồ chí minh', 'hcm', 'ho chi minh', 'hochiminh'],\n", "}\n", "\n", "# Function to determine city based on address\n", "def assign_city(address):\n", "    address_lower = address.lower()  # Convert address to lowercase for case-insensitive matching\n", "    for city, aliases in city_keywords.items():\n", "        for alias in aliases:\n", "            if alias in address_lower:\n", "                return city.title()  # Return city name in title case (capitalize first letter)\n", "    return 'Others'  # If no match, return 'Others'\n", "\n", "# Check if the required column is present\n", "if 'CrawlCity' in df.columns:\n", "    # Create new 'City' column based on 'CompanyAddress'\n", "    df['City'] = df['CrawlCity'].apply(assign_city)\n", "else:\n", "    log_event(f\"Error: 'CrawlCity' column not found in the sheet.\")\n", "\n", "# Update the Google Sheet row by row\n", "for i, row in df.iterrows():\n", "    try:\n", "        worksheet.update(range_name=f'D{i + 2}', values=[[row['City']]])  # Update city column starting from row 2 (D column)\n", "        log_event(f\"Row {i + 2} updated with city: {row['City']}, based on address: {row['CrawlCity']}\")\n", "    except Exception as e:\n", "        log_event(f\"Error updating row {i + 2}: {e}\")\n", "\n", "# Check for rows that did not match cities\n", "missing_cities = df[df['City'] == 'Others']\n", "if not missing_cities.empty:\n", "    log_event(f\"Rows with unmatched cities: {missing_cities[['CrawlCity']]}\")\n", "else:\n", "    log_event(\"All rows successfully matched to cities.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}