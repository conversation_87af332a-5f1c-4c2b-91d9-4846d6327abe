import gspread
import pandas as pd
import logging
from oauth2client.service_account import ServiceAccountCredentials
from datetime import datetime

# Set up logging to a file
logging.basicConfig(
    filename='/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/removeDup.log',
    level=logging.INFO,
    format='%(asctime)s %(message)s'
)

# Log helper function
def log_event(message):
    logging.info(message)

# Authenticate with Google Sheets
scope = ["https://spreadsheets.google.com/feeds", 'https://www.googleapis.com/auth/drive']
creds = ServiceAccountCredentials.from_json_keyfile_name('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/.streamlit/ea-database-form-fbf45c7169a9.json', scope)
client = gspread.authorize(creds)
sheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')

# Backup sheet function
def backup_sheet(sheet, worksheet_name, backup_name):
    try:
        worksheet = sheet.worksheet(worksheet_name)
        sheet.duplicate_sheet(source_sheet_id=worksheet.id, new_sheet_name=backup_name)
        log_event(f'Backup created successfully: {backup_name}')
    except Exception as e:
        log_event(f'Failed to create backup: {e}')

# Duplicate removal function
def remove_duplicates(df, columns, worksheet):
    # Identify duplicates
    duplicate_rows = df[df.duplicated(subset=columns, keep='first')]
    
    # Log duplicate rows
    if not duplicate_rows.empty:
        log_event(f"{len(duplicate_rows)} duplicates found:")
        for _, row in duplicate_rows.iterrows():
            log_event(f"Duplicate row: {row.to_dict()}")
    
    # Remove duplicates, keeping the first occurrence
    df_cleaned = df.drop_duplicates(subset=columns, keep='first').reset_index(drop=True)
    
    # Write the header and cleaned data back to the worksheet
    updated_data = [first_row, second_row] + df_cleaned.values.tolist()
    worksheet.clear()  # Clear existing data
    worksheet.update(updated_data)  # Update with cleaned data
    
    return df_cleaned

# Open the Google Sheets document and specific worksheet
worksheet_name = "Business-RemoveOutliersDup"
worksheet = sheet.worksheet(worksheet_name)

# Backup original sheet
backup_name = f"Backup_{worksheet_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
backup_sheet(sheet, worksheet_name, backup_name)

# Fetch data and process it
data = worksheet.get_all_values()
first_row = data[0]  # Preserve the first row (e.g., merged cells)
second_row = data[1]  # Preserve the second row as the header
rows = data[2:]   # Data starting from the third row

# Convert rows to a DataFrame for easy handling
df = pd.DataFrame(rows, columns=second_row)

# Specify columns to check for duplicates
columns_to_check = ['Tên trường/công ty', 'Người phụ trách/đại diện', 'Cleaned_Emails', 'Processed_PhoneNo']  

# Remove duplicates and log the process
cleaned_df = remove_duplicates(df, columns=columns_to_check, worksheet=worksheet)
log_event("Duplicate removal process completed.")