import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import streamlit as st
import logging
from datetime import datetime

# Set up logging with a timestamp for each log entry
logging.basicConfig(
    filename='form.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Log helper function
def log_event(message):
    logging.info(message)
    if 'latest_logs' not in st.session_state:
        st.session_state.latest_logs = []
    st.session_state.latest_logs.append({"timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"), "message": message})
    # Keep only the last 100 logs in memory
    if len(st.session_state.latest_logs) > 100:
        st.session_state.latest_logs = st.session_state.latest_logs[-100:]
        
def send_password_email(email_address, username, new_password):
    """Sends the new password to the user's email."""
    # Email configuration
    sender_email = "<EMAIL>"
    sender_password = "eanc iuge ocol jeir" 

    message = MIMEMultipart("alternative")
    message["Subject"] = "Mật khẩu mới của bạn"
    message["From"] = sender_email
    message["To"] = email_address

    # Create the plain-text and HTML version of your message
    text = f"""
    Chào {username},

    Mật khẩu mới của bạn là: {new_password}

    Vui lòng đổi mật khẩu sau khi đăng nhập.
    """
    html = f"""
    <html>
      <body>
        <p>Chào {username},<br><br>
           Mật khẩu mới của bạn là: <b>{new_password}</b><br><br>
           Vui lòng đổi mật khẩu sau khi đăng nhập.
        </p>
      </body>
    </html>
    """

    # Turn these into plain/html MIMEText objects
    part1 = MIMEText(text, "plain")
    part2 = MIMEText(html, "html")

    # Add HTML/plain-text parts to MIMEMultipart message
    # The email client will try to render the last part first
    message.attach(part1)
    message.attach(part2)

    context = ssl.create_default_context()
    try:
        with smtplib.SMTP_SSL("smtp.gmail.com", 465, context=context) as server:
            server.login(sender_email, sender_password)
            server.sendmail(sender_email, email_address, message.as_string())
        st.success(f"Mật khẩu mới đã được gửi đến {email_address}")
    except Exception as e:
        st.error(f"Lỗi khi gửi email: {e}")


def send_confirmation_email(user_name, user_email):
    # Email configuration
    sender_email = "<EMAIL>"
    sender_password = "eanc iuge ocol jeir" 
    subject = "[This is an auto email, no-reply] Confirmation of your submission"
    body = f"Hi {user_name}. EA wants to say a huge dupe cute thank you for your submission! We appreciate your interest. More information will be provided shortly."

    # Create message
    msg = MIMEMultipart()
    msg['From'] = sender_email
    msg['To'] = user_email
    msg['Subject'] = subject

    # Attach body text
    msg.attach(MIMEText(body, 'plain'))

    try:
        # Set up the server
        server = smtplib.SMTP('smtp.gmail.com', 587)
        server.starttls()  # Upgrade to a secure connection
        server.login(sender_email, sender_password)

        # Send the email
        server.send_message(msg)
        log_event(f"Email sent successfully to {user_name}!")

    except Exception as e:
        log_event(f"Failed to send email to {user_name}: {str(e)}")

    finally:
        server.quit()