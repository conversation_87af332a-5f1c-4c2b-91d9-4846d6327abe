import logging
from datetime import datetime
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import time

# Set up logging
logging.basicConfig(
    filename='/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/crawling.log',
    level=logging.INFO,
    format='%(asctime)s %(message)s'
)

def log_event(message):
    logging.info(message)

# Function to crawl company address
def crawl_company_address(company_name):
    chrome_options = Options()
    chrome_options.add_argument("--incognito")
    chrome_options.add_argument('--headless')
    driver = webdriver.Chrome(options=chrome_options)

    try:
        # Open Google Search
        driver.get("https://www.google.com")
        search_box = driver.find_element(By.NAME, "q")
        search_box.send_keys(f"{company_name} địa chỉ")
        search_box.send_keys(Keys.RETURN)

        time.sleep(2)  # Wait for results to load

        # Extract search results
        results = []
        search_results = driver.find_elements(By.CSS_SELECTOR, 'div.g')
        for result in search_results[:1]:  # Get first result
            result_text = ""
            try:
                # Alternative structure 1
                alt_title = result.find_element(By.XPATH, ".//h3").text
                alt_snippet = result.find_element(By.XPATH, ".//div[@class='VwiC3b yXK7lf lVm3ye r025kc hJNv6b Hdw6tb']").text
                result_text = f"{alt_title}: {alt_snippet}"
                log_event(f"{company_name}:{result_text}")
            
            except Exception as e:
                log_event(f"{company_name} - Alternative structure 1 error: {e}")
                
                # Try alternative structure 2
                try:
                    alt_snippet = result.find_element(By.XPATH, "//div[@class='ULSxyf']//div[@class='sXLaOe']").text
                    result_text = f"{alt_snippet}"
                    log_event(f"{company_name}:{result_text}")
                
                except Exception as e:
                    log_event(f"{company_name} - Alternative structure 2 error: {e}")
                    continue
            
            if result_text:
                results.append(result_text)
        
        if not results:
            return "Unknown"
        
            # title = result.find_element(By.XPATH, "//div[1][@class='hlcw0c']//h3[@class='LC20lb MBeuO DKV0Md']").text
            # snippet = result.find_element(By.XPATH, "//div[1][@class='hlcw0c']//div[@class='VwiC3b yXK7lf lVm3ye r025kc hJNv6b Hdw6tb']").text
            # results.append(f"{title}: {snippet}")

        return " | ".join(results)

    except Exception as e:
        log_event(f"Error while crawling company address for {company_name}: {e}")
        return "Error"

    finally:
        driver.quit()

# Function to update Google Sheets with crawled information
def update_google_sheets_with_crawled_info(df, column_name):
    log_event('Updating Google Sheets with crawled information')

    # Create a new column for crawled info
    df['Crawled_Info'] = df[column_name].apply(lambda x: crawl_company_address(x))

    log_event('Completed updating Google Sheets with crawled information')
    return df

# Function to create a backup of the sheet
def backup_sheet(sheet, worksheet_name, backup_name):
    try:
        worksheet = sheet.worksheet(worksheet_name)
        sheet.duplicate_sheet(source_sheet_id=worksheet.id, new_sheet_name=backup_name)
        log_event(f'Backup created successfully: {backup_name}')
    except Exception as e:
        log_event(f'Failed to create backup: {e}')

# Example of using Google Sheets with crawling
scope = ["https://spreadsheets.google.com/feeds", 'https://www.googleapis.com/auth/drive']
creds = ServiceAccountCredentials.from_json_keyfile_name('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/.streamlit/ea-database-form-fbf45c7169a9.json', scope)
client = gspread.authorize(creds)
sheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')

# Backup the original sheet
backup_name = f"Backup_Edtech-VN_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
backup_sheet(sheet, 'Edtech-VN', backup_name)

# Log retrieval
log_event('Retrieving data from Google Sheets')
worksheet = sheet.worksheet("Edtech-VN")

# Fetch data and manually set the second row as the header
data = worksheet.get_all_values()
header = data[0]  # Set the second row (index 1) as the header
rows = data[1:]   # Start data from the third row (index 2)

# Convert to DataFrame
existing_data = pd.DataFrame(rows, columns=header)

# Ensure the specified column exists and apply your crawling logic
column_name = 'Tên trường/công ty'

if column_name in existing_data.columns:
    updated_df = update_google_sheets_with_crawled_info(existing_data, column_name)
    # Log completion of data processing
    log_event('Saving changes to Google Sheets')

    # Reconstruct the Google Sheet with the header, and updated data
    updated_data = [header] + updated_df.values.tolist()

    # Update Google Sheets, keeping the first row and header intact
    worksheet.clear()  # Clear the worksheet before updating it
    worksheet.update(f'A1', updated_data)  # Start updating from row 1 (A1)
    log_event('Changes saved to Google Sheets successfully')

else:
    log_event(f'Error: "{column_name}" column not found in the sheet.')
