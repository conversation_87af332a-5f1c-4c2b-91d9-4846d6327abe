import logging
from datetime import datetime
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import pandas as pd
import re
import unicodedata

# Set up logging
logging.basicConfig(
    filename='/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/city_finding.log',
    level=logging.INFO,
    format='%(asctime)s %(message)s'
)

def log_event(message):
    logging.info(message)

# Function to normalize city names and text
# def normalize_text(text):
#     return re.sub(r'\s+', ' ', text.strip()).lower()

def normalize_text(text):
    """
    Normalize the text by lowercasing, removing extra spaces, and stripping diacritics (accents).
    """
    # Remove extra spaces and strip leading/trailing spaces
    text = re.sub(r'\s+', ' ', text.strip())
    # Convert to lowercase
    text = text.lower()
    # Remove diacritics (accents)
    text = ''.join(
        char for char in unicodedata.normalize('NFD', text) 
        if unicodedata.category(char) != 'Mn'
    )
    return text

# Function to find and standardize the city based on a specified column
# def find_city(df, column_name, city_mapping):
#     def get_standard_city(value):
#         normalized_value = normalize_text(str(value))
#         for city_variations, standard_city in city_mapping.items():
#             for variation in city_variations:
#                 if variation in normalized_value:
#                     return standard_city
#         return 'Unknown'  # Default value if no city is found

#     log_event(f'Started finding cities based on column: {column_name}')
    
#     # Add new column 'Tỉnh/TP' next to the last column
#     df['City'] = df[column_name].apply(get_standard_city)
    
#     log_event(f'Completed finding cities and updated "Tỉnh/TP" column.')
#     return df

def find_city(df, column_name, city_mapping):
    def get_standard_city(value):
        normalized_value = normalize_text(str(value))
        for city_variations, standard_city in city_mapping.items():
            for variation in city_variations:
                # Normalize the variation for case, spacing, and diacritics
                normalized_variation = normalize_text(variation)
                if normalized_variation in normalized_value:
                    return standard_city
        return 'Unknown'  # Default value if no city is found

    log_event(f'Started finding cities based on column: {column_name}')
    
    # Add new column 'City' (or 'Tỉnh/TP') next to the last column
    df['City'] = df[column_name].apply(get_standard_city)
    
    log_event(f'Completed finding cities and updated "City" column.')
    return df

# Function to create a backup of the sheet
def backup_sheet(sheet, worksheet_name, backup_name):
    try:
        worksheet = sheet.worksheet(worksheet_name)
        sheet.duplicate_sheet(source_sheet_id=worksheet.id, new_sheet_name=backup_name)
        log_event(f'Backup created successfully: {backup_name}')
    except Exception as e:
        log_event(f'Failed to create backup: {e}')

# Example of using Google Sheets with logging and backup
scope = ["https://spreadsheets.google.com/feeds", 'https://www.googleapis.com/auth/drive']
creds = ServiceAccountCredentials.from_json_keyfile_name('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/.streamlit/ea-database-form-fbf45c7169a9.json', scope)
client = gspread.authorize(creds)
sheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')

# Backup the original sheet
backup_name = f"Backup_Edu-ForeignLanguageCenter_City_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
backup_sheet(sheet, 'Edu-ForeignLanguageCenter', backup_name)

# Log retrieval
log_event('Retrieving data from Google Sheets')
worksheet = sheet.worksheet("Edu-ForeignLanguageCenter")

# Fetch data and manually set the second row as the header
data = worksheet.get_all_values()
first_row = data[0]  # Preserve the first row
header = data[1]  # Set the second row (index 1) as the header
rows = data[2:]   # Start data from the third row (index 2)

# Convert to DataFrame
existing_data = pd.DataFrame(rows, columns=header)

# Ensure the specified column exists and apply your city finding logic
column_name = 'Tỉnh/TP'  # Update with your actual column name

# City mapping (variations -> standardized city)
city_mapping = {
    ('hanoi', 'hn', 'hà nội', 'Cầu Giấy', 'Thanh Xuân', 'Hoàng Mai', 'Đống Đa', 'Hà Đông', 'Hai Bà Trưng', 'Ba Đình', 'Hoàn Kiếm', 'Long Biên', 'Tây Hồ', 'Nam Từ Liêm', 'Bắc Từ Liêm'): 'Hà Nội',
    ('ho chi minh city', 'hcm city', 'tp. hồ chí minh', 'hcmc', 'Tp HCM', 'TPHCM', 'Thành phố Hồ Chí Minh', 'thành phố Hồ Chí Minh',
     'Quận 1', 'Quận 3', 'Quận 4', 'Quận 5', 'Quận 6', 'Quận 7', 'Quận 8', 'Quận 10', 'Quận 11', 'Quận 12', 'Tân Bình', 'Bình Tân', 'Bình Thạnh', 'Tân Phú', 'Gò Vấp', 'Phú Nhuận', 'Bình Chánh', 'Hóc Môn', 'Cần Giờ', 'Củ Chi', 'Nhà bè'): 'TP. Hồ Chí Minh'
}

# Initialize updated_df variable
updated_df = None

if column_name in existing_data.columns:
    updated_df = find_city(existing_data, column_name, city_mapping)
else:
    log_event(f'Error: "{column_name}" column not found in the sheet.')

# Check if updated_df was successfully created
if updated_df is not None:
    # Log completion of data processing
    log_event('Saving changes to Google Sheets')

    # Reconstruct the Google Sheet with the first row, header, and updated data
    updated_data = [first_row] + [header] + updated_df.values.tolist()

    # Update Google Sheets, keeping the first row and header intact
    worksheet.clear()  # Clear the worksheet before updating it
    worksheet.update(f'A1', updated_data)  # Start updating from row 1 (A1)
    log_event('Changes saved to Google Sheets successfully')
else:
    log_event(f"Process aborted: {column_name} column not found or data processing failed.")
