import logging
from datetime import datetime
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import pandas as pd
import re

# Set up logging
logging.basicConfig(
    filename='/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/phone_number_processing.log',
    level=logging.INFO,
    format='%(asctime)s %(message)s'
)

def log_event(message):
    logging.info(message)

# Function to clean phone numbers
def clean_phone_number(phone_number):
    phone_number = str(phone_number)
    if pd.isna(phone_number) or not re.search(r'\d', phone_number):
        log_event(f'Invalid phone number: {phone_number}')
        return ''
    phone_number = phone_number.replace('\n', '/')
    numbers = phone_number.split('/')
    cleaned_numbers = []
    for number in numbers:
        number = re.sub(r'[,\.\s]', '', number)
        if number.startswith('84') and len(number) == 11:
            number = number[2:]
        if not number.startswith('0'):
            number = '0' + number
        if number.isnumeric() and len(number) >= 8:
            cleaned_numbers.append(number)
    return '/'.join(cleaned_numbers)

# Function to process phone numbers
def process_phone_numbers(df, sheet_name):
    log_event(f'Started processing phone numbers for sheet: {sheet_name}')
    
    # Process only the rows where 'ID' starts with 'EDVN' or is NaN
    edvn_rows = df['ID'].fillna('').str.startswith('EDVN') | df['ID'].isna()
    
    # Clean the 'SĐT' column only in the filtered rows
    df.loc[edvn_rows, 'SĐT'] = df.loc[edvn_rows, 'SĐT'].apply(clean_phone_number)
    
    log_event(f'Completed processing phone numbers for sheet: {sheet_name}')
    return df

# Function to create a backup of the sheet
def backup_sheet(sheet, worksheet_name, backup_name):
    try:
        worksheet = sheet.worksheet(worksheet_name)
        sheet.duplicate_sheet(source_sheet_id=worksheet.id, new_sheet_name=backup_name)
        log_event(f'Backup created successfully: {backup_name}')
    except Exception as e:
        log_event(f'Failed to create backup: {e}')

# Example of using Google Sheets with logging and backup
scope = ["https://spreadsheets.google.com/feeds", 'https://www.googleapis.com/auth/drive']
creds = ServiceAccountCredentials.from_json_keyfile_name('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/.streamlit/ea-database-form-fbf45c7169a9.json', scope)
client = gspread.authorize(creds)
sheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')

# Backup the original sheet
backup_name = f"Backup_Business_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
backup_sheet(sheet, 'Business', backup_name)

# Log retrieval
log_event('Retrieving data from Google Sheets')
worksheet = sheet.worksheet("Business")

# Fetch data and manually set the second row as the header
data = worksheet.get_all_values()
first_row = data[0]  # Preserve the first row
header = data[1]  # Set the second row (index 1) as the header
rows = data[2:]   # Start data from the third row (index 2)

# Convert to DataFrame
existing_data = pd.DataFrame(rows, columns=header)

# Ensure the 'ID' column exists and apply your filtering logic
if 'ID' in existing_data.columns:
    cleaned_df = process_phone_numbers(existing_data, 'Business')
else:
    log_event('Error: "ID" column not found in the sheet.')

# Log completion of data processing
log_event('Saving changes to Google Sheets')

# Reconstruct the Google Sheet with the first row, header, and cleaned data
updated_data = [first_row] + [header] + cleaned_df.values.tolist()

# Update Google Sheets, keeping the first row and header intact
worksheet.clear()  # Clear the worksheet before updating it
worksheet.update(f'A1', updated_data)  # Start updating from row 1 (A1)
log_event('Changes saved to Google Sheets successfully')