import mysql.connector
import bcrypt
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import pandas as pd
import streamlit as st
from streamlit_option_menu import option_menu
import io
import re
from openpyxl import load_workbook
from openpyxl.drawing.image import Image as OpenpyxlImage
from PIL import Image
import logging

# Set up logging with a timestamp for each log entry
logging.basicConfig(
    filename='webapp_processing.log',
    level=logging.ERROR,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
@st.cache_data

# Function to add a watermark to the Excel sheet
def add_watermark_to_excel(excel_buffer, watermark_image_path):
    excel_buffer.seek(0)
    workbook = load_workbook(excel_buffer)
    sheet = workbook.active  # Assuming we work on the first sheet

    img = Image.open(watermark_image_path)
    openpyxl_img = OpenpyxlImage(img)
    sheet.add_image(openpyxl_img, 'N9')  

    output = io.BytesIO()
    workbook.save(output)
    output.seek(0)
    return output

# Convert DataFrame to Excel
def convert_df(df, watermark_image_path):
    excel_buffer = io.BytesIO()
    df.to_excel(excel_buffer, index=False)

    return add_watermark_to_excel(excel_buffer, watermark_image_path)

# Download function
def download_as_xlsx(df, file_name, watermark_image_path):
    if file_name:
        try:
            xlsx = convert_df(df, watermark_image_path)
            st.download_button(
                label="Download data as XLSX format",
                data=xlsx.getvalue(),
                file_name=f'{file_name}.xlsx',
                mime='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
        except Exception as e:
            st.error(f"Error occurred: {e}")
    else:
        st.warning('File name not specified')

# MySQL Database connection
def create_connection():
    return mysql.connector.connect(
    host="localhost", 
    user="root",       
    password="HnAm2002#@!",  
    database="UserName_Password_EdtechAgencyDataWarehouse" 
)

# Hash a password
def hash_password(password):
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

# Verify a hashed password
def check_password(password, hashed):
    return bcrypt.checkpw(password.encode('utf-8'), hashed)

# Authenticate user by username, password, and return role
def authenticate_user(username, password):
    connection = create_connection()
    cursor = connection.cursor(dictionary=True)
    
    # Query to fetch all necessary fields
    query = "SELECT EA_username, role, sheet_access, EA_password FROM users WHERE EA_username = %s"
    cursor.execute(query, (username,))
    user = cursor.fetchone()
    
    connection.close()
    
    if user:
        # Handle case for missing sheet_access for older accounts
        sheet_access = user['sheet_access'] if user['sheet_access'] else 'all' if user['role'] == 'admin' else ''
        
        # Check the password
        if check_password(password, user['EA_password'].encode('utf-8')):
            return user['EA_username'], user['role'], sheet_access  
    return None  # Return None if authentication fails

# Add new user (Admin only)
# Updated add_user function to include sheet access
def add_user(username, password, role='user', sheet_access=''):
    connection = create_connection()
    cursor = connection.cursor()
    
    hashed_password = hash_password(password)
    
    # If role is admin, set sheet_access to 'all' by default
    if role == 'admin':
        sheet_access = 'all'
    
    query = "INSERT INTO users (EA_username, EA_password, role, sheet_access) VALUES (%s, %s, %s, %s)"
    cursor.execute(query, (username, hashed_password, role, sheet_access))
    
    connection.commit()
    connection.close()
    st.success(f"User '{username}' added successfully with sheet access: {sheet_access}")

# Function to add a watermark to the Excel sheet
def add_watermark_to_excel(excel_buffer, watermark_image_path):
    excel_buffer.seek(0)
    workbook = load_workbook(excel_buffer)
    sheet = workbook.active  # Assuming we work on the first sheet

    img = Image.open(watermark_image_path)
    openpyxl_img = OpenpyxlImage(img)
    sheet.add_image(openpyxl_img, 'N9')  

    output = io.BytesIO()
    workbook.save(output)
    output.seek(0)
    return output

# Convert DataFrame to Excel
def convert_df(df, watermark_image_path):
    excel_buffer = io.BytesIO()
    df.to_excel(excel_buffer, index=False)

    return add_watermark_to_excel(excel_buffer, watermark_image_path)

# Download function
def download_as_xlsx(df, file_name, watermark_image_path):
    if file_name:
        try:
            xlsx = convert_df(df, watermark_image_path)
            st.download_button(
                label="Download data as XLSX format",
                data=xlsx.getvalue(),
                file_name=f'{file_name}.xlsx',
                mime='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
        except Exception as e:
            st.error(f"Error occurred: {e}")
    else:
        st.warning('File name not specified')

# Utility function to get column data type
def get_column_type(df, col):
    if pd.api.types.is_bool_dtype(df[col]):
        return 'boolean'
    elif pd.api.types.is_numeric_dtype(df[col]):
        return 'numeric'
    elif pd.api.types.is_datetime64_any_dtype(df[col]):
        return 'date'
    else:
        return 'string'

# Filter generating function
def generate_filter(df, filter_num):
    col = st.selectbox(f"Choose a column to filter ({filter_num})", df.columns, key=f"column_select_{filter_num}")

    col_type = get_column_type(df, col)

    # Boolean Filter
    if col_type == 'boolean':
        boolean_value = st.selectbox(f"Select value for {col}", options=[True, False], key=f"boolean_select_{filter_num}")
        order = st.selectbox(f"Order {col}", options=["Ascending", "Descending"], key=f"order_boolean_{filter_num}")
        return df[(df[col] == boolean_value)].sort_values(by=col, ascending=(order == "Ascending"))

    # Numeric Filter
    elif col_type == 'numeric':
        compare_option = st.selectbox("Comparison", options=["=", ">", "<"], key=f"compare_numeric_{filter_num}")
        chosen_value = st.number_input(f"Enter value for {col}", key=f"input_numeric_{filter_num}")
        order = st.selectbox(f"Order {col}", options=["Ascending", "Descending"], key=f"order_numeric_{filter_num}")
        if compare_option == "=":
            filtered_df = df[df[col] == chosen_value]
        elif compare_option == ">":
            filtered_df = df[df[col] > chosen_value]
        else:
            filtered_df = df[df[col] < chosen_value]
        return filtered_df.sort_values(by=col, ascending=(order == "Ascending"))

    # Date Filter
    elif col_type == 'date':
        start_date = st.date_input(f"Start date for {col}", value=df[col].min(), key=f"start_date_{filter_num}")
        end_date = st.date_input(f"End date for {col}", value=df[col].max(), key=f"end_date_{filter_num}")
        order = st.selectbox(f"Order {col}", options=["Ascending", "Descending"], key=f"order_date_{filter_num}")
        return df[(df[col] >= pd.to_datetime(start_date)) & (df[col] <= pd.to_datetime(end_date))].sort_values(by=col, ascending=(order == "Ascending"))

    # String Filter
    elif col_type == 'string':
        search_value = st.text_input(f"Search value for {col}", key=f"search_string_{filter_num}")
        order = st.selectbox(f"Order {col}", options=["Ascending", "Descending"], key=f"order_string_{filter_num}")
        return df[df[col].str.contains(search_value, case=False)].sort_values(by=col, ascending=(order == "Ascending"))

# Analyzing function
def load_sheet_data(sheet):
    """Attempt to load data by checking headers in the 2nd and 1st rows for the presence of 'ID'."""
    header_row = None

    # Check if row 2 has the 'ID' column as part of its header
    try:
        data_row2 = sheet.get_all_records(head=2)
        if 'ID' in data_row2[0]:  # If 'ID' column exists in row 2 header
            header_row = 2
    except Exception as e:
        logging.error(f"Could not fetch data starting from row 2 in sheet '{sheet.title}': {e}")

    # Fallback: Check if row 1 has the 'ID' column as part of its header
    if header_row is None:
        try:
            data_row1 = sheet.get_all_records(head=1)
            if 'ID' in data_row1[0]:  # If 'ID' column exists in row 1 header
                header_row = 1
        except Exception as e:
            logging.error(f"Error fetching data from both rows 1 and 2 in sheet '{sheet.title}': {e}")
    
    # Load data based on the identified header row
    if header_row:
        try:
            return sheet.get_all_records(head=header_row)
        except Exception as e:
            logging.error(f"Failed to load data from identified header row {header_row} in sheet '{sheet.title}': {e}")
            return None
    else:
        logging.error(f"'ID' column not found in rows 1 or 2 for sheet '{sheet.title}'. Skipping.")
        return None

def display_sheet_metrics_dynamic(sheets_data, max_columns=4):
    """
    Display metrics for each sheet in a dynamic layout.
    Arguments:
    - sheets_data: list of tuples, each tuple containing (sheet, sheet_name)
    - max_columns: maximum number of columns to display in a row
    """
    current_col = 0
    for i, (sheet, sheet_name) in enumerate(sheets_data):
        # Load data and calculate metrics
        sheet_data = load_sheet_data(sheet)
        if sheet_data is None:
            total_rows, max_id_value = "N/A", "N/A"
        else:
            df = pd.DataFrame(sheet_data)
            if 'ID' in df.columns:
                total_rows = len(df)
                df['ID_numeric'] = df['ID'].apply(lambda x: int(re.search(r'-(\d+)$', str(x)).group(1)) if re.search(r'-(\d+)$', str(x)) else None)
                max_id_value = df['ID_numeric'].max()
            else:
                total_rows, max_id_value = "N/A", "N/A"
        
        # Start a new row if max_columns is reached
        if current_col == 0:
            row = st.columns(max_columns)
        
        # Display metrics within the calculated column
        with row[current_col]:
            st.metric(label=f"{sheet_name} Total Row", value=f"{total_rows}")
            st.metric(label=f"{sheet_name} Max ID", value=f"{max_id_value if max_id_value is not None else 'N/A'}")

        # Move to the next column; reset if max reached
        current_col = (current_col + 1) % max_columns

# Define a function to get sheet data and store it in session state if not already cached
def get_cached_sheet_data(spreadsheet, sheet_name):
    if sheet_name not in st.session_state:
        # Fetch the sheet and cache its data in session state
        sheet = spreadsheet.worksheet(sheet_name)
        st.session_state[sheet_name] = sheet
    return st.session_state[sheet_name]

# Set up Google Sheets API scope
scope = ["https://spreadsheets.google.com/feeds", 'https://www.googleapis.com/auth/drive']
creds = ServiceAccountCredentials.from_json_keyfile_name('/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/.streamlit/ea-database-form-fbf45c7169a9.json', scope)
client = gspread.authorize(creds)
spreadsheet = client.open_by_url('https://docs.google.com/spreadsheets/d/1YdblYk8ovrtLmbkGBJAtdNoAXqYXKILGLJH9GTvbtpE')

# Main login and data loading function
def main():
    st.set_page_config(page_title="WebPage",page_icon="/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/logo.png",layout="wide")
    # Function to inject CSS and HTML
    def set_custom_styles():
        st.markdown(
            """
            <style>
            .title-container {
                background-image: url('https://img.freepik.com/premium-photo/cabin-sits-lake-with-full-moon-background_960396-778419.jpg');
                background-size: cover;
                border-radius: 15px;
                padding: 20px;
                text-align: center;
                color: #ffffff; /* Text color */
                font-size: 2rem; /* Title font size */
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Add shadow */
            }
            </style>
            """,
            unsafe_allow_html=True
        )

    # Set custom styles
    set_custom_styles()

    # Display the title inside a styled box
    st.markdown('<div class="title-container">Welcome to Edtech Agency Data Warehouse</div>', unsafe_allow_html=True)

    st.sidebar.image("/Users/<USER>/Documents/PROJECT_DA/EdtechAgency/Database/logo.png",caption="")

    # Check if the user is authenticated
    if not st.session_state.get("authenticated", False):
        st.title("Login")

        # Input fields for login
        username = st.text_input("Username")
        password = st.text_input("Password", type="password")

        # Authentication button
        # Call the authenticate_user function during login
        if st.button("Login"):
            user_info = authenticate_user(username, password)
            if user_info:
                user_name, user_role, sheet_access = user_info  # Unpack user role and sheet access
                st.session_state.authenticated = True
                st.session_state.role = user_role
                st.session_state.name = user_name
                st.session_state.sheet_access = sheet_access  # Store sheet access in session state
                st.success(f"Login successful! You are logged in as '{user_name}' with access to: {sheet_access}")
                st.rerun()
            else:
                st.error("Invalid username or password.")
    else:
        # User is authenticated
        def set_custom_styles2():
            st.markdown(
                """
                <style>
                .centered-title {
                    text-align: center; /* Center the text */
                    font-size: 2.5rem; /* Title size can be adjusted */
                    color: #000000; /* Change text color if needed */
                }
                </style>
                """,
                unsafe_allow_html=True
            )

        # Set custom styles
        set_custom_styles2()

        # Display the title inside a styled HTML element
        st.markdown(f'<h1 class="centered-title">Hi, {st.session_state.name}!</h1>', unsafe_allow_html=True)


        with st.sidebar:
            selection = option_menu(
                menu_title="Menu",
                options=["Main","Extension"],
                icons=["house","eye"],
                menu_icon="cast",
                default_index=0
            )
        
        if selection == "Main":
            st.divider()
            st.subheader("Data Loading Interface")

            # Initialize session state for the selected sheet and the data
            if "loaded_sheet" not in st.session_state:
                st.session_state.loaded_sheet = None
            if "loaded_data" not in st.session_state:
                st.session_state.loaded_data = None

            # Provided text that sheet names should not start with
            provided_text = "Backup"
            # Select an object to display metrics
            object_display_metric = st.selectbox(
                'Object to display metrics', 
                options=['Others', 'Edu', 'S', 'Edtech', 'Business'], 
                index=None,
                placeholder='S comprises of Kinder, K12, and HE'
            )

            # Initialize session state for metrics display and selected sheet
            if "metrics_displayed" not in st.session_state:
                st.session_state.metrics_displayed = False
            if "loaded_sheet" not in st.session_state:
                st.session_state.loaded_sheet = None

            # Initialize all_sheet_names as an empty list
            all_sheet_names = []

            if object_display_metric:
                # Get all sheet names that start with the selected prefix
                all_sheet_names = [
                    sheet.title 
                    for sheet in spreadsheet.worksheets() 
                    if sheet.title.startswith(object_display_metric)
                ]
            else:
                st.warning('Please select an object')

            # Filter sheets based on user role and sheet access
            if st.session_state.role == "admin":
                sheet_names = all_sheet_names  # Admins can access all sheets
            else:
                # Filter sheets based on user's sheet access permissions
                sheet_names = [sheet for sheet in all_sheet_names if sheet in st.session_state.sheet_access]

            # Display metrics in an expander and only run once when the metric object is first selected
            with st.expander("Metrics Display", expanded=True):
                if not st.session_state.metrics_displayed and sheet_names:
                    sheets_data = [(get_cached_sheet_data(spreadsheet, sheet_name), sheet_name) for sheet_name in sheet_names]
                    display_sheet_metrics_dynamic(sheets_data, max_columns=4)
                    st.session_state.metrics_displayed = True  # Set flag to prevent re-display

            # Create a dropdown menu with the sheet names
            selected_sheet = st.selectbox("Select a sheet", sheet_names, index=None)
            
            # Check if the user has selected a worksheet
            if selected_sheet:
                # Only reload the data if the selected sheet has changed
                if selected_sheet != st.session_state.loaded_sheet:
                    st.session_state.loaded_sheet = selected_sheet
                    sheet = spreadsheet.worksheet(selected_sheet)
                    
                    try:
                        # Try fetching data with header at row 2
                        st.session_state.loaded_data = sheet.get_all_records(head=2)
                    except Exception as e:
                        st.warning(f"Error fetching data with header at row 2: {e}")
                        
                        # If an error occurs, try fetching data with header at row 1
                        try:
                            st.session_state.loaded_data = sheet.get_all_records(head=1)
                        except Exception as e:
                            st.error(f"Error fetching data with both methods: {e}")
                            st.session_state.loaded_data = None

                # Now use the session state data if available
                if st.session_state.loaded_data:
                    df = pd.DataFrame(st.session_state.loaded_data)
                    st.write(f"Data from {selected_sheet}:")
                    st.dataframe(df)
                    st.divider()
                    st.header("Data Manipulation Interface")
                    option = st.radio(
                            "Choose an option:",
                            [":rainbow[Filter Data]", "***Add Data***", "Remove Data :movie_camera:", "Describe Data"],
                            captions=[
                                "Filter and download.",
                                "Add but conforming to the rules.",
                                "Remove what you can access.",
                                "Get a summary of the data."
                            ],
                            horizontal=True,
                            index=None,
                        )
                    
                    if option == ":rainbow[Filter Data]":
                        st.header("Filter Data and Download")

                        # Column-based filters (Dropdown for filtering specific columns)
                        selected_columns = st.multiselect("Select columns to display", options=df.columns.tolist(), default=df.columns.tolist())

                        # Apply column filter
                        filtered_data1 = df[selected_columns]
                        st.write(filtered_data1)

                        # Filter section
                        st.subheader("Apply Filters")

                        # Button to add a new filter
                        if 'filter_count' not in st.session_state:
                            st.session_state.filter_count = 1

                        # Flags to track whether add or remove button was pressed
                        c1, c2 = st.columns(2)
                        add_filter = c1.button("Add another filter")
                        remove_filter = c2.button("Remove the latest filter")

                        # Adjust filter count based on user actions
                        if add_filter:
                            st.session_state.filter_count += 1
                        elif remove_filter and st.session_state.filter_count > 1:
                            st.session_state.filter_count -= 1
                        filtered_data1 = df  # Initial dataframe to filter

                        # Apply filters based on the current filter count
                        for i in range(st.session_state.filter_count):
                            st.write(f"Filter {i + 1}")
                            filtered_data1 = generate_filter(filtered_data1, i) 

                        # Display the final filtered dataframe
                        st.write("Filtered Data:")
                        st.dataframe(filtered_data1)

                        if filtered_data1 is not None:
                            file_name = st.text_input('Specify your file name:', value='', placeholder='...')
                            watermark_image_path = 'EdtechAgency.png' 
                            download_as_xlsx(filtered_data1, file_name, watermark_image_path)
                        else:
                            st.error('No filtered data to download.')
                    
                    elif option == "***Add Data***":
                        st.header("Add Data")

                        # Mapping of sheets to their respective columns and input types
                        sheet_columns = {
                            "Sheet1": {
                                "Name": {"type": "text"},
                                "Age": {"type": "number"},
                                "Email": {"type": "email"},
                                "Phone Number": {"type": "text"},
                            },
                            "Sheet2": {
                                "Product Name": {"type": "text"},
                                "Price": {"type": "number"},
                                "Category": {"type": "select", "options": ["Electronics", "Books", "Clothing", "Other"]},
                                "Available Stock": {"type": "number"},
                            },
                            # Add more sheets and their respective fields here
                        }

                        # Allow user to select the target sheet
                        target_sheet = st.selectbox("Select a sheet to add data", list(sheet_columns.keys()))

                        # Check if a target sheet is selected
                        if target_sheet:
                            # Retrieve the columns and input configurations for the selected sheet
                            columns_config = sheet_columns[target_sheet]
                            
                            # Form for user input based on the selected sheet's columns
                            form_data = {}
                            for field, config in columns_config.items():
                                if config["type"] == "text":
                                    form_data[field] = st.text_input(f"Enter {field}", placeholder=f"Type in {field}")
                                elif config["type"] == "number":
                                    form_data[field] = st.number_input(f"Enter {field}", min_value=0, step=1)
                                elif config["type"] == "email":
                                    form_data[field] = st.text_input(f"Enter {field}", placeholder="<EMAIL>")
                                elif config["type"] == "select":
                                    form_data[field] = st.selectbox(f"Select {field}", options=config["options"])
                                elif config["type"] == "date":
                                    form_data[field] = st.date_input(f"Select {field}")

                            # Validation rules for each predefined column (Example rules, modify as needed)
                            def validate_data(data):
                                errors = []
                                if "Name" in data and not data["Name"]:
                                    errors.append("Name is required.")
                                if "Age" in data and data["Age"] <= 0:
                                    errors.append("Age must be a positive number.")
                                if "Email" in data and "@" not in data["Email"]:
                                    errors.append("Please enter a valid email.")
                                # Additional validation rules can be added here
                                return errors

                            # Submit button to add the data
                            if st.button("Submit"):
                                # Validate the input data
                                errors = validate_data(form_data)
                                if errors:
                                    for error in errors:
                                        st.error(error)
                                else:
                                    # Append validated data to the selected sheet
                                    try:
                                        sheet = spreadsheet.worksheet(target_sheet)
                                        sheet.append_row(list(form_data.values()))
                                        st.success("Data added successfully!")
                                    except Exception as e:
                                        st.error(f"Failed to add data: {e}")

                    elif option == "Remove Data :movie_camera:":
                        pass
                    elif option == "Describe Data":
                        pass
                                
                else:
                    st.warning("No data available.")
            else:
                st.warning("Please select a worksheet.")
        

        # Updated Admin Panel: Add New User Form
        if selection == "Extension":

            # If the logged-in user is an admin, show the Add User form
            if st.session_state.role == 'admin':
                st.subheader("Admin Panel: Add New User")

                new_username = st.text_input("New Username")
                new_password = st.text_input("New Password", type="password")
                new_role = st.selectbox("Role", options=['admin', 'user'], index=1)

                # Retrieve and filter available sheets for selection
                provided_text = "Backup"
                available_sheets = [sheet.title for sheet in spreadsheet.worksheets() if not sheet.title.startswith(provided_text)]

                # For non-admin users, select sheet access from the available sheets
                if new_role == 'user':
                    selected_sheets = st.multiselect("Sheet Access", options=available_sheets)
                    sheet_access = ','.join(selected_sheets)  # Join selected sheets as a comma-separated string
                else:
                    sheet_access = 'all'  # Grant all sheet access to admin by default

                # Add user button with validation
                if st.button("Add User"):
                    if new_username and new_password:
                        add_user(new_username, new_password, new_role, sheet_access)
                        st.success(f"User '{new_username}' added successfully with role '{new_role}' and access to: {sheet_access}")
                    else:
                        st.warning("Please fill in both username and password.")

        # Logout button
        if st.sidebar.button("Logout"):
            st.session_state.authenticated = False
            st.session_state.role = None  # Reset user role
            st.rerun()  # Force a rerun to reflect the logout immediately

if __name__ == "__main__":
    main()












