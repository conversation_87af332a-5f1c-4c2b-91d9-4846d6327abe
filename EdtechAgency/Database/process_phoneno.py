import pandas as pd
import re

def clean_phone_number(phone_number):
    # Convert to string to avoid AttributeError for non-string values
    phone_number = str(phone_number)

    # Handle NaN or non-numeric values
    if pd.isna(phone_number) or not re.search(r'\d', phone_number):
        return ''

    # Remove newlines and replace them with '/'
    phone_number = phone_number.replace('\n', '/')

    # Split the phone number by '/' to handle two phone numbers in one cell
    numbers = phone_number.split('/')

    cleaned_numbers = []
    for number in numbers:
        # Remove commas, dots, and spaces
        number = re.sub(r'[,\.\s]', '', number)

        # Remove the '84' prefix if it's followed by 9 digits
        if number.startswith('84') and len(number) == 11:
            number = number[2:]

        # Add '0' to the front if the number doesn't start with '0'
        if not number.startswith('0'):
            number = '0' + number

        # Only include numbers that are numeric and have at least 8 characters
        if number.isnumeric() and len(number) >= 8:
            cleaned_numbers.append(number)

    # Join cleaned numbers with '/' if there are multiple numbers
    return '/'.join(cleaned_numbers)

def process_phone_numbers(file_path, sheet_name='Sheet1', header=0):
    # Load your dataset
    df = pd.read_excel(file_path, sheet_name=sheet_name, header=header)

    # Clean the 'SĐT' column directly
    df['SĐT'] = df['SĐT'].apply(clean_phone_number)

    # Save the changes back to the original file
    with pd.ExcelWriter(file_path, mode='a', if_sheet_exists='replace') as writer:
        df.to_excel(writer, sheet_name=sheet_name, index=False)

    return df

# Example of using the function
file_path = '/Users/<USER>/Downloads/Database.xlsx'
sheet_name = 'Business'  # Update with your sheet name
header = 1  # Update with your header row index
cleaned_df = process_phone_numbers(file_path, sheet_name, header)